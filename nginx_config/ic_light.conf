server {
    listen 443 ssl;
    # SSL 证书路径
    ssl_certificate /data/lsc/sorealhuman.com_nginx/sorealhuman.com_nginx/sorealhuman.com_bundle.crt;
    ssl_certificate_key /data/lsc/sorealhuman.com_nginx/sorealhuman.com_nginx/sorealhuman.com.key;

    # SSL 协议配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_session_timeout 10m;
    ssl_prefer_server_ciphers on;

    server_name sorealhuman.com;
    client_max_body_size 20M;

    access_log /data5/IC-Light/output/IC/sorealhuman.access.log;
    error_log /data5/IC-Light/output/IC/sorealhuman.error.log debug;    
    # WebSocket超时设置
    proxy_read_timeout 600s;
    proxy_connect_timeout 600s;
    proxy_send_timeout 600s; 
    
    # 图片文件精确匹配路径
    location /images/file/ {
        alias /data5/IC-Light/output/IC/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        try_files $uri =404;
    }
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;  # 必须的WebSocket协议版本
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";  # 关键连接头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 新增Vite HMR专用代理（关键）
    location /_vite/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}