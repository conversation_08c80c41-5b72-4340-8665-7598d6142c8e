-- 创建device_token表
CREATE TABLE IF NOT EXISTS device_token (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    device_token VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user_account(user_id),
    UNIQUE KEY unique_user_token (user_id, device_token)
);

-- 添加索引以提高查询性能
CREATE INDEX idx_device_token_user_id ON device_token(user_id);
CREATE INDEX idx_device_token_is_active ON device_token(is_active); 