// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		284E25232E53104D005ABF22 /* Pods_NIENIE.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4E63A97E3FABBA07AC16A332 /* Pods_NIENIE.framework */; };
		284E25242E53104D005ABF22 /* Pods_NIENIE.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 4E63A97E3FABBA07AC16A332 /* Pods_NIENIE.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		2880C76C2E2621D900BB8C27 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2880C76B2E2621D900BB8C27 /* StoreKit.framework */; };
		2880C7992E2DF4BA00BB8C27 /* Vision.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2880C7982E2DF4BA00BB8C27 /* Vision.framework */; };
		CCF6703CBFDBA6A574434178 /* Pods_NIENIE_NIENIEUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 67F4EB298774AE502324E7F5 /* Pods_NIENIE_NIENIEUITests.framework */; };
		CFA5B6C6FE7BB32774CEF158 /* Pods_NIENIETests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7F2B015788790EB12428412B /* Pods_NIENIETests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		2818DFEA2DFA9CA5005903F7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2818DFD42DFA9CA3005903F7 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2818DFDB2DFA9CA3005903F7;
			remoteInfo = NIENIE;
		};
		2818DFF42DFA9CA5005903F7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2818DFD42DFA9CA3005903F7 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2818DFDB2DFA9CA3005903F7;
			remoteInfo = NIENIE;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		284E25252E53104D005ABF22 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				284E25242E53104D005ABF22 /* Pods_NIENIE.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		2818DFDC2DFA9CA3005903F7 /* NIENIE.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = NIENIE.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2818DFE92DFA9CA5005903F7 /* NIENIETests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = NIENIETests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		2818DFF32DFA9CA5005903F7 /* NIENIEUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = NIENIEUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		2880C76B2E2621D900BB8C27 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		2880C7982E2DF4BA00BB8C27 /* Vision.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Vision.framework; path = System/Library/Frameworks/Vision.framework; sourceTree = SDKROOT; };
		289E065543F013542FEE2653 /* Pods-NIENIETests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NIENIETests.release.xcconfig"; path = "Target Support Files/Pods-NIENIETests/Pods-NIENIETests.release.xcconfig"; sourceTree = "<group>"; };
		29FD225F99E84B909D20F794 /* Pods-NIENIE-NIENIEUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NIENIE-NIENIEUITests.release.xcconfig"; path = "Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests.release.xcconfig"; sourceTree = "<group>"; };
		2B31EB9ADC02E927B0AAB55A /* Pods-NIENIE.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NIENIE.release.xcconfig"; path = "Target Support Files/Pods-NIENIE/Pods-NIENIE.release.xcconfig"; sourceTree = "<group>"; };
		4E63A97E3FABBA07AC16A332 /* Pods_NIENIE.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_NIENIE.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		67F4EB298774AE502324E7F5 /* Pods_NIENIE_NIENIEUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_NIENIE_NIENIEUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7F2B015788790EB12428412B /* Pods_NIENIETests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_NIENIETests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		CFEC932FD280E029173A2B88 /* Pods-NIENIETests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NIENIETests.debug.xcconfig"; path = "Target Support Files/Pods-NIENIETests/Pods-NIENIETests.debug.xcconfig"; sourceTree = "<group>"; };
		D86A2AFDC647F540A01FB11F /* Pods-NIENIE-NIENIEUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NIENIE-NIENIEUITests.debug.xcconfig"; path = "Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests.debug.xcconfig"; sourceTree = "<group>"; };
		DA3545A36781E4DBF0F5D6DC /* Pods-NIENIE.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NIENIE.debug.xcconfig"; path = "Target Support Files/Pods-NIENIE/Pods-NIENIE.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		28E5FF292E028F0800A13A56 /* Exceptions for "NIENIE" folder in "NIENIE" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 2818DFDB2DFA9CA3005903F7 /* NIENIE */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet section */
		284E251C2E5306DA005ABF22 /* Exceptions for "NIENIE" folder in "Compile Sources" phase from "NIENIE" target */ = {
			isa = PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet;
			buildPhase = 2856AB3B2E4604D300C9C029 /* Sources */;
			membershipExceptions = (
				"NIENIE-Bridging-Header.h",
			);
		};
/* End PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		2818DFDE2DFA9CA3005903F7 /* NIENIE */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				28E5FF292E028F0800A13A56 /* Exceptions for "NIENIE" folder in "NIENIE" target */,
				284E251C2E5306DA005ABF22 /* Exceptions for "NIENIE" folder in "Compile Sources" phase from "NIENIE" target */,
			);
			path = NIENIE;
			sourceTree = "<group>";
		};
		2818DFEC2DFA9CA5005903F7 /* NIENIETests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = NIENIETests;
			sourceTree = "<group>";
		};
		2818DFF62DFA9CA5005903F7 /* NIENIEUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = NIENIEUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		2818DFD92DFA9CA3005903F7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				284E25232E53104D005ABF22 /* Pods_NIENIE.framework in Frameworks */,
				2880C7992E2DF4BA00BB8C27 /* Vision.framework in Frameworks */,
				2880C76C2E2621D900BB8C27 /* StoreKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2818DFE62DFA9CA5005903F7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CFA5B6C6FE7BB32774CEF158 /* Pods_NIENIETests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2818DFF02DFA9CA5005903F7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CCF6703CBFDBA6A574434178 /* Pods_NIENIE_NIENIEUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2818DFD32DFA9CA3005903F7 = {
			isa = PBXGroup;
			children = (
				2818DFDE2DFA9CA3005903F7 /* NIENIE */,
				2818DFEC2DFA9CA5005903F7 /* NIENIETests */,
				2818DFF62DFA9CA5005903F7 /* NIENIEUITests */,
				2880C76A2E2621D900BB8C27 /* Frameworks */,
				2818DFDD2DFA9CA3005903F7 /* Products */,
				A338EA37288AFA45E0E64B8D /* Pods */,
			);
			sourceTree = "<group>";
		};
		2818DFDD2DFA9CA3005903F7 /* Products */ = {
			isa = PBXGroup;
			children = (
				2818DFDC2DFA9CA3005903F7 /* NIENIE.app */,
				2818DFE92DFA9CA5005903F7 /* NIENIETests.xctest */,
				2818DFF32DFA9CA5005903F7 /* NIENIEUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		2880C76A2E2621D900BB8C27 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				2880C7982E2DF4BA00BB8C27 /* Vision.framework */,
				2880C76B2E2621D900BB8C27 /* StoreKit.framework */,
				4E63A97E3FABBA07AC16A332 /* Pods_NIENIE.framework */,
				67F4EB298774AE502324E7F5 /* Pods_NIENIE_NIENIEUITests.framework */,
				7F2B015788790EB12428412B /* Pods_NIENIETests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A338EA37288AFA45E0E64B8D /* Pods */ = {
			isa = PBXGroup;
			children = (
				DA3545A36781E4DBF0F5D6DC /* Pods-NIENIE.debug.xcconfig */,
				2B31EB9ADC02E927B0AAB55A /* Pods-NIENIE.release.xcconfig */,
				D86A2AFDC647F540A01FB11F /* Pods-NIENIE-NIENIEUITests.debug.xcconfig */,
				29FD225F99E84B909D20F794 /* Pods-NIENIE-NIENIEUITests.release.xcconfig */,
				CFEC932FD280E029173A2B88 /* Pods-NIENIETests.debug.xcconfig */,
				289E065543F013542FEE2653 /* Pods-NIENIETests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2818DFDB2DFA9CA3005903F7 /* NIENIE */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2818DFFD2DFA9CA5005903F7 /* Build configuration list for PBXNativeTarget "NIENIE" */;
			buildPhases = (
				C51238C33C484767D910097E /* [CP] Check Pods Manifest.lock */,
				2818DFD92DFA9CA3005903F7 /* Frameworks */,
				2818DFDA2DFA9CA3005903F7 /* Resources */,
				2856AB3B2E4604D300C9C029 /* Sources */,
				284E25252E53104D005ABF22 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				2818DFDE2DFA9CA3005903F7 /* NIENIE */,
			);
			name = NIENIE;
			productName = NIENIE;
			productReference = 2818DFDC2DFA9CA3005903F7 /* NIENIE.app */;
			productType = "com.apple.product-type.application";
		};
		2818DFE82DFA9CA5005903F7 /* NIENIETests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2818E0002DFA9CA5005903F7 /* Build configuration list for PBXNativeTarget "NIENIETests" */;
			buildPhases = (
				0A1ED992E7C7795CA1D85ED3 /* [CP] Check Pods Manifest.lock */,
				2818DFE62DFA9CA5005903F7 /* Frameworks */,
				2818DFE72DFA9CA5005903F7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2818DFEB2DFA9CA5005903F7 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2818DFEC2DFA9CA5005903F7 /* NIENIETests */,
			);
			name = NIENIETests;
			productName = NIENIETests;
			productReference = 2818DFE92DFA9CA5005903F7 /* NIENIETests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		2818DFF22DFA9CA5005903F7 /* NIENIEUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2818E0032DFA9CA5005903F7 /* Build configuration list for PBXNativeTarget "NIENIEUITests" */;
			buildPhases = (
				149728FE1D6127606F27CD7C /* [CP] Check Pods Manifest.lock */,
				2818DFEF2DFA9CA5005903F7 /* Sources */,
				2818DFF02DFA9CA5005903F7 /* Frameworks */,
				2818DFF12DFA9CA5005903F7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2818DFF52DFA9CA5005903F7 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2818DFF62DFA9CA5005903F7 /* NIENIEUITests */,
			);
			name = NIENIEUITests;
			productName = NIENIEUITests;
			productReference = 2818DFF32DFA9CA5005903F7 /* NIENIEUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2818DFD42DFA9CA3005903F7 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					2818DFDB2DFA9CA3005903F7 = {
						CreatedOnToolsVersion = 16.4;
					};
					2818DFE82DFA9CA5005903F7 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 2818DFDB2DFA9CA3005903F7;
					};
					2818DFF22DFA9CA5005903F7 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 2818DFDB2DFA9CA3005903F7;
					};
				};
			};
			buildConfigurationList = 2818DFD72DFA9CA3005903F7 /* Build configuration list for PBXProject "NIENIE" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 2818DFD32DFA9CA3005903F7;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 2818DFDD2DFA9CA3005903F7 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2818DFDB2DFA9CA3005903F7 /* NIENIE */,
				2818DFE82DFA9CA5005903F7 /* NIENIETests */,
				2818DFF22DFA9CA5005903F7 /* NIENIEUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2818DFDA2DFA9CA3005903F7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2818DFE72DFA9CA5005903F7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2818DFF12DFA9CA5005903F7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0A1ED992E7C7795CA1D85ED3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NIENIETests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		149728FE1D6127606F27CD7C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NIENIE-NIENIEUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C51238C33C484767D910097E /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NIENIE-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2818DFEF2DFA9CA5005903F7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2856AB3B2E4604D300C9C029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2818DFEB2DFA9CA5005903F7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2818DFDB2DFA9CA3005903F7 /* NIENIE */;
			targetProxy = 2818DFEA2DFA9CA5005903F7 /* PBXContainerItemProxy */;
		};
		2818DFF52DFA9CA5005903F7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2818DFDB2DFA9CA3005903F7 /* NIENIE */;
			targetProxy = 2818DFF42DFA9CA5005903F7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		2818DFFB2DFA9CA5005903F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		2818DFFC2DFA9CA5005903F7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		2818DFFE2DFA9CA5005903F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DA3545A36781E4DBF0F5D6DC /* Pods-NIENIE.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = NIENIE/NIENIE.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = F3SY3YNWK9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NIENIE/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "需要访问您的相机，以便拍摄照片进行处理。例如：拍摄人物照片进行背景替换。如果禁止，您将无法使用拍照上传功能。";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要保存处理后的结果或者您感兴趣的图片到您的相册。如果禁止，您将无法保存。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问您的相册，以便选择照片进行处理。例如：选取人物照片进行背景替换。如果禁止，您将无法从相册选择照片上传。";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "";
				MARKETING_VERSION = 1.1;
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = com.sorealhuman.NIENIE;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "NIENIE/NIENIE-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2818DFFF2DFA9CA5005903F7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2B31EB9ADC02E927B0AAB55A /* Pods-NIENIE.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = NIENIE/NIENIE.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = F3SY3YNWK9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NIENIE/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "需要访问您的相机，以便拍摄照片进行处理。例如：拍摄人物照片进行背景替换。如果禁止，您将无法使用拍照上传功能。";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要保存处理后的结果或者您感兴趣的图片到您的相册。如果禁止，您将无法保存。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问您的相册，以便选择照片进行处理。例如：选取人物照片进行背景替换。如果禁止，您将无法从相册选择照片上传。";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "";
				MARKETING_VERSION = 1.1;
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = com.sorealhuman.NIENIE;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "NIENIE/NIENIE-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		2818E0012DFA9CA5005903F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CFEC932FD280E029173A2B88 /* Pods-NIENIETests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = F3SY3YNWK9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sorealhuman.NIENIETests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NIENIE.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/NIENIE";
			};
			name = Debug;
		};
		2818E0022DFA9CA5005903F7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 289E065543F013542FEE2653 /* Pods-NIENIETests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = F3SY3YNWK9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sorealhuman.NIENIETests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NIENIE.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/NIENIE";
			};
			name = Release;
		};
		2818E0042DFA9CA5005903F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D86A2AFDC647F540A01FB11F /* Pods-NIENIE-NIENIEUITests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sorealhuman.NIENIEUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = NIENIE;
			};
			name = Debug;
		};
		2818E0052DFA9CA5005903F7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 29FD225F99E84B909D20F794 /* Pods-NIENIE-NIENIEUITests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sorealhuman.NIENIEUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = NIENIE;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2818DFD72DFA9CA3005903F7 /* Build configuration list for PBXProject "NIENIE" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2818DFFB2DFA9CA5005903F7 /* Debug */,
				2818DFFC2DFA9CA5005903F7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2818DFFD2DFA9CA5005903F7 /* Build configuration list for PBXNativeTarget "NIENIE" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2818DFFE2DFA9CA5005903F7 /* Debug */,
				2818DFFF2DFA9CA5005903F7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2818E0002DFA9CA5005903F7 /* Build configuration list for PBXNativeTarget "NIENIETests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2818E0012DFA9CA5005903F7 /* Debug */,
				2818E0022DFA9CA5005903F7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2818E0032DFA9CA5005903F7 /* Build configuration list for PBXNativeTarget "NIENIEUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2818E0042DFA9CA5005903F7 /* Debug */,
				2818E0052DFA9CA5005903F7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2818DFD42DFA9CA3005903F7 /* Project object */;
}
