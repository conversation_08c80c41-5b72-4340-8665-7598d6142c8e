import Foundation

// 会话免打扰状态管理器
class ConversationMuteManager: ObservableObject {
    static let shared = ConversationMuteManager()
    
    @Published private var muteStates: [String: Bool] = [:]
    private let userDefaults = UserDefaults.standard
    private let keyPrefix = "conversation_mute_"
    
    private init() {
        loadMuteStates()
    }
    
    // 从本地存储加载免打扰状态
    private func loadMuteStates() {
        let userId = UserState.shared.userId
        let key = "\(keyPrefix)\(userId)"
        
        if let data = userDefaults.data(forKey: key),
           let states = try? JSONDecoder().decode([String: Bool].self, from: data) {
            muteStates = states
        }
    }
    
    // 保存免打扰状态到本地存储
    private func saveMuteStates() {
        let userId = UserState.shared.userId
        let key = "\(keyPrefix)\(userId)"
        
        if let data = try? JSONEncoder().encode(muteStates) {
            userDefaults.set(data, forKey: key)
        }
    }
    
    // 获取指定会话的免打扰状态
    func getMuteStatus(for conversationId: String) -> Bool {
        return muteStates[conversationId] ?? false
    }
    
    // 设置指定会话的免打扰状态
    func setMuteStatus(_ isMuted: Bool, for conversationId: String) {
        muteStates[conversationId] = isMuted
        saveMuteStates()
    }
    
    // 批量更新免打扰状态（从服务器获取的会话列表）
    func updateMuteStates(from conversations: [Conversation]) {
        var hasChanges = false
        
        for conversation in conversations {
            let currentState = muteStates[conversation.conversation_id]
            if currentState != conversation.is_muted {
                muteStates[conversation.conversation_id] = conversation.is_muted
                hasChanges = true
            }
        }
        
        if hasChanges {
            saveMuteStates()
        }
    }
    
    // 清除指定用户的所有免打扰状态（用户登出时调用）
    func clearMuteStates(for userId: Int) {
        let key = "\(keyPrefix)\(userId)"
        userDefaults.removeObject(forKey: key)
        muteStates.removeAll()
    }
    
    // 获取所有免打扰状态（用于调试）
    func getAllMuteStates() -> [String: Bool] {
        return muteStates
    }
}
