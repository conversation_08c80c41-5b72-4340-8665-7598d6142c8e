import SwiftUI

// 本地聊天视图模型
class LocalChatViewModel: ObservableObject {
    @Published var messages: [Message] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let conversationId: String
    private let currentUserId: Int
    private let otherUserId: Int
    private let targetMessageId: Int?
    
    init(conversationId: String, currentUserId: Int, otherUserId: Int, targetMessageId: Int? = nil) {
        self.conversationId = conversationId
        self.currentUserId = currentUserId
        self.otherUserId = otherUserId
        self.targetMessageId = targetMessageId
        loadMessagesFromCache()
    }
    
    // 从本地缓存加载消息
    func loadMessagesFromCache() {
        isLoading = true
        errorMessage = nil
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            do {
                let fileManager = FileManager.default
                let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: false)
                let fileURL = documentsDirectory.appendingPathComponent("messages_\(self.conversationId).json")
                
                if fileManager.fileExists(atPath: fileURL.path) {
                    let data = try Data(contentsOf: fileURL)
                    let decoder = JSONDecoder()
                    decoder.dateDecodingStrategy = .iso8601
                    let loadedMessages = try decoder.decode([Message].self, from: data)
                    
                    // 按时间排序
                    let sortedMessages = loadedMessages.sorted { $0.created_at < $1.created_at }
                    
                    DispatchQueue.main.async {
                        self.messages = sortedMessages
                        self.isLoading = false
                    }
                } else {
                    DispatchQueue.main.async {
                        self.messages = []
                        self.isLoading = false
                        self.errorMessage = "没有找到本地聊天记录"
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    self.errorMessage = "读取本地聊天记录失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    // 查找目标消息的索引
    func findTargetMessageIndex() -> Int? {
        guard let targetId = targetMessageId else { return nil }
        return messages.firstIndex { $0.message_id == targetId }
    }
}

// 本地聊天视图
struct LocalChatView: View {
    @StateObject private var viewModel: LocalChatViewModel
    @State private var dragOffset: CGFloat = 0
    @Environment(\.dismiss) var dismiss
    
    let conversationId: String
    let currentUserId: Int
    let otherUserId: Int
    let otherUsername: String
    let otherUserAvatar: String
    let currentUserAvatar: String
    let targetMessageId: Int?
    
    init(conversationId: String, currentUserId: Int, otherUserId: Int, otherUsername: String, otherUserAvatar: String, currentUserAvatar: String, targetMessageId: Int? = nil) {
        self.conversationId = conversationId
        self.currentUserId = currentUserId
        self.otherUserId = otherUserId
        self.otherUsername = otherUsername
        self.otherUserAvatar = otherUserAvatar
        self.currentUserAvatar = currentUserAvatar
        self.targetMessageId = targetMessageId
        
        _viewModel = StateObject(wrappedValue: LocalChatViewModel(
            conversationId: conversationId,
            currentUserId: currentUserId,
            otherUserId: otherUserId,
            targetMessageId: targetMessageId
        ))
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.white.edgesIgnoringSafeArea(.all)
                
                VStack(spacing: 0) {
                    if viewModel.isLoading {
                        Spacer()
                        ProgressView("加载本地聊天记录...")
                        Spacer()
                    } else if let errorMessage = viewModel.errorMessage {
                        Spacer()
                        VStack(spacing: 16) {
                            Image(systemName: "exclamationmark.triangle")
                                .font(.system(size: 40))
                                .foregroundColor(.orange)
                            
                            Text(errorMessage)
                                .foregroundColor(.gray)
                                .multilineTextAlignment(.center)
                            
                            Button("重新加载") {
                                viewModel.loadMessagesFromCache()
                            }
                            .padding()
                            .foregroundColor(.white)
                            .background(Color.blue)
                            .cornerRadius(8)
                        }
                        .padding()
                        Spacer()
                    } else if viewModel.messages.isEmpty {
                        Spacer()
                        VStack(spacing: 16) {
                            Image(systemName: "message")
                                .font(.system(size: 40))
                                .foregroundColor(.gray.opacity(0.6))
                            
                            Text("暂无本地聊天记录")
                                .font(.system(size: 16))
                                .foregroundColor(.gray)
                        }
                        Spacer()
                    } else {
                        // 聊天消息列表
                        ScrollViewReader { scrollView in
                            ScrollView {
                                LazyVStack(spacing: 10) {
                                    ForEach(viewModel.messages) { message in
                                        LocalMessageBubble(
                                            message: message,
                                            otherUserAvatar: otherUserAvatar,
                                            currentUserAvatar: currentUserAvatar,
                                            isTargetMessage: message.message_id == targetMessageId
                                        )
                                        .id(message.id)
                                    }
                                }
                                .padding(.horizontal)
                                .padding(.top, 10)
                            }
                            .onAppear {
                                // 如果有目标消息，直接定位到该消息（无动画）
                                if let targetId = targetMessageId, !viewModel.messages.isEmpty {
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                        scrollView.scrollTo(targetId, anchor: .center)
                                    }
                                }
                            }
                            .onChange(of: viewModel.messages) { oldMessages, newMessages in
                                // 当消息加载完成后，定位到目标消息
                                if let targetId = targetMessageId, !newMessages.isEmpty, oldMessages.isEmpty {
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                        scrollView.scrollTo(targetId, anchor: .center)
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .offset(x: dragOffset)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        // 只允许向右滑动
                        if value.translation.width > 0 {
                            dragOffset = value.translation.width
                        }
                    }
                    .onEnded { value in
                        // 如果滑动距离超过50点，则返回上一页
                        if value.translation.width > 50 {
                            dismiss()
                        } else {
                            // 否则回弹到原位置
                            withAnimation(.spring()) {
                                dragOffset = 0
                            }
                        }
                    }
            )
            .navigationTitle("本地聊天记录")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .navigationBarItems(
                leading: Button(action: {
                    dismiss()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.black)
                        Text("返回")
                            .foregroundColor(.black)
                    }
                }
            )
        }
    }
}

// 本地消息气泡视图
struct LocalMessageBubble: View {
    let message: Message
    let otherUserAvatar: String
    let currentUserAvatar: String
    let isTargetMessage: Bool
    
    var body: some View {
        HStack(alignment: .bottom, spacing: 8) {
            if message.is_self {
                Spacer()
                
                // 自己的消息气泡
                Text(message.content)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(isTargetMessage ? Color.orange : Color.blue)
                    .foregroundColor(.white)
                    .clipShape(BubbleShape(isFromCurrentUser: true))
                    .padding(.trailing, 8)
                    .overlay(
                        // 目标消息高亮边框
                        isTargetMessage ? 
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.orange, lineWidth: 2)
                            .padding(.trailing, 8)
                        : nil
                    )
                
                // 自己的头像（右侧）
                Image(currentUserAvatar)
                    .resizable()
                    .scaledToFill()
                    .frame(width: 40, height: 40)
                    .clipShape(Circle())
            } else {
                // 对方的头像（左侧）
                if !otherUserAvatar.isEmpty {
                    if otherUserAvatar.contains("http") {
                        // 远程头像
                        AsyncImage(url: URL(string: otherUserAvatar)) { phase in
                            if let image = phase.image {
                                image
                                    .resizable()
                                    .scaledToFill()
                            } else {
                                Circle()
                                    .fill(Color.gray.opacity(0.3))
                            }
                        }
                        .frame(width: 40, height: 40)
                        .clipShape(Circle())
                    } else {
                        // 本地头像
                        Image(otherUserAvatar)
                            .resizable()
                            .scaledToFill()
                            .frame(width: 40, height: 40)
                            .clipShape(Circle())
                    }
                } else {
                    // 默认头像
                    Circle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 40, height: 40)
                }
                
                // 对方的消息气泡
                Text(message.content)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(isTargetMessage ? Color.orange.opacity(0.3) : Color(UIColor.systemGray6))
                    .foregroundColor(.black)
                    .clipShape(BubbleShape(isFromCurrentUser: false))
                    .padding(.leading, 8)
                    .overlay(
                        // 目标消息高亮边框
                        isTargetMessage ? 
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.orange, lineWidth: 2)
                            .padding(.leading, 8)
                        : nil
                    )
                
                Spacer()
            }
        }
        .padding(.horizontal, 4)
    }
}

struct LocalChatView_Previews: PreviewProvider {
    static var previews: some View {
        LocalChatView(
            conversationId: "1_2",
            currentUserId: 1,
            otherUserId: 2,
            otherUsername: "测试用户",
            otherUserAvatar: "avatar",
            currentUserAvatar: "current_avatar",
            targetMessageId: 123
        )
    }
}
