import SwiftUI

struct DeleteAccountView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userState: UserState
    @State private var showConfirmation = false
    @State private var isDeleting = false
    @State private var errorMessage: String? = nil
    @State private var navigateToSelectView = false
    
    var body: some View {
        ZStack {
            // 背景色
            Color(UIColor.systemGroupedBackground)
                .ignoresSafeArea()
            
            VStack(spacing: 20) {
                // 标题
                Text("注销账户")
                    .font(Font.custom("PingFang SC", size: 24).weight(.bold))
                    .foregroundColor(.black)
                    .padding(.top, 20)
                
                // 警告图标
                Image(systemName: "exclamationmark.triangle.fill")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 60, height: 60)
                    .foregroundColor(.red)
                    .padding(.top, 20)
                
                // 警告文本
                VStack(spacing: 15) {
                    Text("注销账户将会导致：")
                        .font(Font.custom("PingFang SC", size: 18).weight(.medium))
                        .foregroundColor(.black)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    VStack(alignment: .leading, spacing: 10) {
                        WarningItem(text: "所有个人信息将被永久删除")
                        WarningItem(text: "所有创作记录将被清空")
                        WarningItem(text: "所有上传的图片将被删除")
                        WarningItem(text: "所有点赞和评论记录将被删除")
                        WarningItem(text: "账户注销后无法恢复")
                    }
                    .padding(.leading, 10)
                }
                .padding()
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
                .padding(.horizontal)
                
                if let error = errorMessage {
                    Text(error)
                        .font(Font.custom("PingFang SC", size: 14))
                        .foregroundColor(.red)
                        .padding()
                }
                
                Spacer()
                
                // 注销按钮
                Button(action: {
                    showConfirmation = true
                }) {
                    Text("注销账户")
                        .font(Font.custom("PingFang SC", size: 18).weight(.medium))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 15)
                        .background(Color.red)
                        .cornerRadius(25)
                        .shadow(color: Color.red.opacity(0.3), radius: 5, x: 0, y: 2)
                }
                .padding(.horizontal, 30)
                .padding(.bottom, 20)
                .disabled(isDeleting)
                
                // 返回按钮
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Text("返回")
                        .font(Font.custom("PingFang SC", size: 16))
                        .foregroundColor(.gray)
                }
                .padding(.bottom, 30)
            }
            
            // 加载指示器
            if isDeleting {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                
                ProgressView("正在注销账户...")
                    .progressViewStyle(CircularProgressViewStyle())
                    .padding()
                    .background(Color.white)
                    .cornerRadius(10)
                    .shadow(radius: 10)
            }
            
            // 确认弹窗
            if showConfirmation {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {} // 防止点击背景关闭弹窗
                
                VStack(spacing: 20) {
                    Text("确认注销账户")
                        .font(Font.custom("PingFang SC", size: 18).weight(.bold))
                    
                    Text("注销后账户数据将被永久删除且无法恢复，确定要注销吗？")
                        .font(Font.custom("PingFang SC", size: 16))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                    
                    HStack(spacing: 20) {
                        Button(action: {
                            showConfirmation = false
                        }) {
                            Text("取消")
                                .font(Font.custom("PingFang SC", size: 16))
                                .foregroundColor(.gray)
                                .frame(width: 100)
                                .padding(.vertical, 10)
                                .background(Color.white)
                                .cornerRadius(20)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                                )
                        }
                        
                        Button(action: {
                            deleteAccount()
                            showConfirmation = false
                        }) {
                            Text("确定注销")
                                .font(Font.custom("PingFang SC", size: 16))
                                .foregroundColor(.white)
                                .frame(width: 100)
                                .padding(.vertical, 10)
                                .background(Color.red)
                                .cornerRadius(20)
                        }
                    }
                }
                .padding(24)
                .background(Color.white)
                .cornerRadius(16)
                .shadow(color: Color.black.opacity(0.2), radius: 10, x: 0, y: 5)
                .padding(.horizontal, 40)
            }
            
            // 导航到SelectView
            NavigationLink(destination: SelectView().navigationBarHidden(true), isActive: $navigateToSelectView) {
                EmptyView()
            }
        }
        .navigationBarBackButtonHidden(true)
        .navigationBarItems(leading: 
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .foregroundColor(.black)
                    .imageScale(.large)
            }
        )
    }
    
    private func deleteAccount() {
        isDeleting = true
        errorMessage = nil
        
        userState.deleteAccount { result in
            DispatchQueue.main.async {
                isDeleting = false
                
                switch result {
                case .success(let success):
                    if success {
                        // 注销成功，导航到选择页面
                        navigateToSelectView = true
                    } else {
                        errorMessage = "注销失败，请稍后重试"
                    }
                case .failure(let error):
                    errorMessage = "注销失败: \(error.localizedDescription)"
                }
            }
        }
    }
}

struct WarningItem: View {
    let text: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "circle.fill")
                .resizable()
                .frame(width: 6, height: 6)
                .foregroundColor(.red)
                .padding(.top, 6)
            
            Text(text)
                .font(Font.custom("PingFang SC", size: 16))
                .foregroundColor(.black)
        }
    }
}

struct DeleteAccountView_Previews: PreviewProvider {
    static var previews: some View {
        DeleteAccountView()
            .environmentObject(UserState())
    }
} 