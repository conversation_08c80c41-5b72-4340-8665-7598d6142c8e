import SwiftUI

struct CommentActionSheet: View {
    let comment: CommentData
    let currentUserId: Int
    @Binding var isPresented: Bool
    let onAction: (CommentAction) -> Void
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 半透明背景，点击关闭菜单
                Color.black.opacity(0.4)
                    .edgesIgnoringSafeArea(.all)
                    .onTapGesture {
                        withAnimation(.spring()) {
                            isPresented = false
                        }
                    }
                
                // 底部菜单
                VStack(spacing: 0) {
                    Spacer()
                    
                    VStack(spacing: 0) {
                        // 菜单项
                        VStack(spacing: 1) {
                            // 回复按钮
                            Button(action: {
                                onAction(.reply)
                                withAnimation(.spring()) {
                                    isPresented = false
                                }
                            }) {
                                HStack {
                                    Image(systemName: "arrowshape.turn.up.left.fill")
                                        .font(.system(size: 18))
                                        .foregroundColor(.blue)
                                        .frame(width: 30)
                                    
                                    Text("回复")
                                        .font(.system(size: 16))
                                        .foregroundColor(.black)
                                    
                                    Spacer()
                                }
                                .padding(.vertical, 16)
                                .padding(.horizontal, 20)
                                .background(Color.white)
                            }
                            
                            Divider()
                                .padding(.horizontal, 20)
                            
                            // 举报按钮
                            Button(action: {
                                onAction(.report)
                                withAnimation(.spring()) {
                                    isPresented = false
                                }
                            }) {
                                HStack {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .font(.system(size: 18))
                                        .foregroundColor(.orange)
                                        .frame(width: 30)
                                    
                                    Text("举报")
                                        .font(.system(size: 16))
                                        .foregroundColor(.black)
                                    
                                    Spacer()
                                }
                                .padding(.vertical, 16)
                                .padding(.horizontal, 20)
                                .background(Color.white)
                            }
                            
                            // 如果是自己的评论，显示删除按钮
                            if comment.userId == currentUserId {
                                Divider()
                                    .padding(.horizontal, 20)
                                
                                Button(action: {
                                    onAction(.delete)
                                    withAnimation(.spring()) {
                                        isPresented = false
                                    }
                                }) {
                                    HStack {
                                        Image(systemName: "trash.fill")
                                            .font(.system(size: 18))
                                            .foregroundColor(.red)
                                            .frame(width: 30)
                                        
                                        Text("删除")
                                            .font(.system(size: 16))
                                            .foregroundColor(.red)
                                        
                                        Spacer()
                                    }
                                    .padding(.vertical, 16)
                                    .padding(.horizontal, 20)
                                    .background(Color.white)
                                }
                            }
                        }
                        .background(Color.white)
                        
                        // 取消按钮
                        Button(action: {
                            withAnimation(.spring()) {
                                isPresented = false
                            }
                        }) {
                            Text("取消")
                                .font(.system(size: 16, weight: .bold))
                                .foregroundColor(.black)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 16)
                                .background(Color.white)
                        }
                        .padding(.top, 8)
                        
                        // 添加底部安全区填充
                        Color.white
                            .frame(height: geometry.safeAreaInsets.bottom)
                    }
                    .frame(width: geometry.size.width) // 设置宽度为屏幕宽度
                    .background(Color.white)
                    .cornerRadius(12, corners: [.topLeft, .topRight])
                }
            }
            .edgesIgnoringSafeArea(.all) // 确保覆盖整个屏幕
            .frame(width: geometry.size.width, height: geometry.size.height)
            .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
        }
        .transition(.move(edge: .bottom))
        .animation(.spring(), value: isPresented)
    }
}

// 预览
struct CommentActionSheet_Previews: PreviewProvider {
    static var previews: some View {
        CommentActionSheet(
            comment: CommentData(
                commentId: 1,
                imageId: 1,
                userId: 1,
                username: "用户名",
                avatar: "4-1",
                parentCommentId: nil,
                content: "这是一条评论",
                createdAt: "2023-06-01T12:00:00"
            ),
            currentUserId: 1,
            isPresented: .constant(true),
            onAction: { _ in }
        )
    }
} 