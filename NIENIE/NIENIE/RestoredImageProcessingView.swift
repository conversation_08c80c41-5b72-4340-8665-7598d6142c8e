import SwiftUI

// 恢复创作状态的图像处理视图
struct RestoredImageProcessingView: View {
    @Environment(\.presentationMode) var presentationMode
    var taskId: String
    @State private var processingStatus: String
    @State private var outputImage: UIImage?
    @State private var outputImagePath: String
    @State private var bgImagePath: String
    @State private var timer: Timer?
    @State private var showAlert: Bool = false
    @State private var alertMessage: String = ""
    
    // 用于处理保存图片的类
    private let imageHelper = ImageSaveHelper()
    
    init(taskId: String, initialProcessingStatus: String, initialOutputImage: UIImage?, initialOutputImagePath: String, initialBgImagePath: String) {
        self.taskId = taskId
        self._processingStatus = State(initialValue: initialProcessingStatus)
        self._outputImage = State(initialValue: initialOutputImage)
        self._outputImagePath = State(initialValue: initialOutputImagePath)
        self._bgImagePath = State(initialValue: initialBgImagePath)
    }
    
    var body: some View {
        ZStack {
            // 背景颜色
            Color.white.ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 标题栏
                HStack {
                    // 占位，保持标题居中
                    Color.clear
                        .frame(width: 20, height: 20)
                        .padding(.leading, 16)
                    
                    Spacer()
                    
                    // 标题
                    Text(processingStatus == "completed" ? "创作完成" : "创作中")
                        .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                        .foregroundColor(.black)
                    
                    Spacer()
                    
                    // 占位，保持标题居中
                    Color.clear
                        .frame(width: 20, height: 20)
                        .padding(.trailing, 16)
                }
                .padding(.top, 16)
                .padding(.bottom, 20)
                
                // 图片展示区域
                GeometryReader { geometry in
                    ZStack {
                        if processingStatus == "completed" && outputImage != nil {
                            // 显示处理完成的图片
                            Image(uiImage: outputImage!)
                                .resizable()
                                .scaledToFit()
                                .frame(width: geometry.size.width, height: geometry.size.height)
                        } else if processingStatus == "failed" {
                            // 显示处理失败文字
                            VStack {
                                Text("处理失败")
                                    .font(Font.custom("PingFang SC", size: 20).weight(.medium))
                                    .foregroundColor(.red)
                            }
                            .frame(width: geometry.size.width, height: geometry.size.height)
                        } else {
                            // 显示加载动画
                            VStack {
                                ProgressView()
                                    .scaleEffect(1.5)
                                    .padding(.bottom, 20)
                                
                                Text("正在处理图像...")
                                    .font(Font.custom("PingFang SC", size: 16))
                                    .foregroundColor(.gray)
                            }
                            .frame(width: geometry.size.width, height: geometry.size.height)
                        }
                    }
                }
                .clipped()
                
                // 使用共享的底部按钮组件
                CreationActionButtonsView(
                    processingStatus: processingStatus,
                    outputImage: outputImage,
                    taskId: taskId,
                    isImageEnhanced: UserState.shared.isImageEnhanced,
                    imageHelper: imageHelper,
                    onPublish: {
                        publishImage()
                    },
                    onGoToHome: {
                        navigateToHomePage()
                    },
                    onLightEnhance: {
                        navigateToLightEnhancementView()
                    },
                    showAlert: $showAlert,
                    alertMessage: $alertMessage
                )
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            // 初始化图片保存辅助类的回调
            imageHelper.onSaveCompletion = { message in
                alertMessage = message
                showAlert = true
            }
            
            // 确保原始图像被保存
            if let image = outputImage, UserState.shared.originalOutputImage == nil {
                UserState.shared.originalOutputImage = image
            }
            
            // 如果状态是处理中，则继续轮询
            if processingStatus == "processing" {
                startPollingTaskStatus()
            }
        }
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
        .onDisappear {
            // 确保视图消失时停止计时器
            timer?.invalidate()
            timer = nil
        }
    }
    
    // 开始轮询任务状态
    private func startPollingTaskStatus() {
        // 开始定时检查
        timer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { _ in
            checkTaskStatus()
        }
    }
    
    // 检查任务状态
    private func checkTaskStatus() {
        APIService.shared.getTaskStatus(taskId: taskId) { result in
            switch result {
            case .success(let statusInfo):
                let (status, outputPath, bgPath) = statusInfo
                
                DispatchQueue.main.async {
                    processingStatus = status
                    
                    // 更新UserState中的状态
                    UserState.shared.processingStatus = status
                    UserState.shared.isCreationInProgress = status == "processing"
                    UserState.shared.isCreationComplete = status == "completed"
                    
                    if status == "completed" {
                        // 停止定时器
                        timer?.invalidate()
                        
                        // 保存路径信息
                        outputImagePath = outputPath
                        bgImagePath = bgPath
                        UserState.shared.outputImagePath = outputPath
                        UserState.shared.bgImagePath = bgPath
                        
                        // 加载输出图像
                        loadOutputImage(path: outputPath)
                    }
                }
                
            case .failure(_):
                DispatchQueue.main.async {
                    // 将状态设置为失败
                    processingStatus = "failed"
                    UserState.shared.processingStatus = "failed"
                    
                    // 停止定时器
                    timer?.invalidate()
                }
            }
        }
    }
    
    // 加载输出图像
    private func loadOutputImage(path: String) {
        // 提取文件名
        let filename = path.components(separatedBy: "/").last ?? path
        // 直接使用文件名构建完整URL
        let urlString = "\(APIService.shared.baseURL)/images/file/output/\(filename)"
        
        guard let url = URL(string: urlString) else {
            DispatchQueue.main.async {
                processingStatus = "failed"
                UserState.shared.processingStatus = "failed"
            }
            return
        }
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            if error != nil {
                DispatchQueue.main.async {
                    processingStatus = "failed"
                    UserState.shared.processingStatus = "failed"
                }
                return
            }
            
            guard let data = data, let image = UIImage(data: data) else {
                DispatchQueue.main.async {
                    processingStatus = "failed"
                    UserState.shared.processingStatus = "failed"
                }
                return
            }
            
            DispatchQueue.main.async {
                outputImage = image
                UserState.shared.outputImage = image
                
                // 保存原始图像，用于补光
                UserState.shared.originalOutputImage = image
            }
        }.resume()
    }
    
    // 发布图片
    private func publishImage() {
        guard let image = outputImage else { return }
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            
            // 检查当前呈现的控制器，如果有，获取当前控制器
            var currentController = rootViewController
            while let presented = currentController.presentedViewController {
                currentController = presented
            }
            
            // 创建发布作品页面
            let hostingController = UIHostingController(rootView: 
                PublishView(
                    image: image,
                    taskId: taskId,
                    isImageEnhanced: UserState.shared.isImageEnhanced,
                    onBack: { [weak currentController] in
                        // 返回到创作完成页面
                        currentController?.dismiss(animated: true, completion: nil)
                    }
                )
            )
            hostingController.modalPresentationStyle = UIModalPresentationStyle.fullScreen
            
            // 从当前控制器呈现发布作品页面
            currentController.present(hostingController, animated: true)
        }
    }
    
    // 导航到社区页面
    private func navigateToHomePage() {
        // 停止定时器
        timer?.invalidate()
        
        // 如果创作已完成，保存图像到创作记录
        if processingStatus == "completed" && outputImage != nil {
            // 将图像保存到创作记录中
            let exists = UserState.shared.checkIfImageExists(image: outputImage!)
            if !exists {
                if UserState.shared.saveCreationImage(image: outputImage!) != nil {
                } 
            } 
            
            // 重置悬浮球状态
            UserState.shared.resetCreationState()
        } else {
            // 如果还在进行中，保留悬浮球和创作状态
            UserState.shared.showFloatingBall = true
            UserState.shared.taskId = taskId
            UserState.shared.isCreationInProgress = processingStatus == "processing"
            UserState.shared.processingStatus = processingStatus
            UserState.shared.outputImagePath = outputImagePath
            UserState.shared.bgImagePath = bgImagePath
            UserState.shared.outputImage = outputImage
        }
        
        // 使用NotificationCenter发送通知，在TabView中切换到社区标签
        NotificationCenter.default.post(name: Notification.Name("SwitchToHomeTab"), object: nil)
        
        // 关闭当前视图
        presentationMode.wrappedValue.dismiss()
        
        // 确保关闭所有模态视图
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            // 使用UIApplication.shared来关闭所有可能的模态视图
            if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let keyWindow = scene.windows.first(where: { $0.isKeyWindow }),
               let rootViewController = keyWindow.rootViewController {
                rootViewController.dismiss(animated: true, completion: nil)
            }
        }
    }
    
    // 导航到人物补光增强视图
    private func navigateToLightEnhancementView() {
        // 确保有原始图像可用
        guard let originalImage = UserState.shared.originalOutputImage ?? outputImage else { return }
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            
            // 检查当前呈现的控制器，如果有，获取当前控制器
            var currentController = rootViewController
            while let presented = currentController.presentedViewController {
                currentController = presented
            }
            
            // 创建人物补光页面
            let hostingController = UIHostingController(rootView: 
                LightEnhancementView(
                    image: originalImage, // 使用原始图像
                    taskId: taskId,
                    onBack: { [weak currentController] in
                        // 返回到创作完成页面
                        currentController?.dismiss(animated: true, completion: nil)
                    },
                    onSave: { [weak currentController] enhancedImage in
                        // 保存增强后的图片并返回
                        if let controller = currentController {
                            // 更新当前页面的图像
                            DispatchQueue.main.async {
                                self.outputImage = enhancedImage
                                
                                // 同时更新UserState中的图像，保持状态一致
                                UserState.shared.outputImage = enhancedImage
                                
                                // 标记图片已被修改
                                UserState.shared.isImageEnhanced = true
                                
                                // 检查创作记录并更新
                                if UserState.shared.checkIfImageExists(image: originalImage) {
                                    _ = UserState.shared.updateCreationImage(originalImage: originalImage, newImage: enhancedImage)
                                }
                            }
                            controller.dismiss(animated: true, completion: nil)
                        }
                    }
                )
            )
            hostingController.modalPresentationStyle = UIModalPresentationStyle.fullScreen
            
            // 从当前控制器呈现人物补光页面
            currentController.present(hostingController, animated: true)
        }
    }
} 
