import SwiftUI

struct FloatingBallView: View {
    @EnvironmentObject var userState: UserState
    @State private var dragOffset: CGSize = .zero
    @State private var isDragging = false
    
    // 旋转动画状态
    @State private var rotationAngle = 0.0
    let rotationDuration = 2.0 // 旋转一周所需时间，秒
    
    // 添加计时器属性
    @State private var pollingTimer: Timer? = nil
    
    var body: some View {
        ZStack {
            // 加载动画圆圈
            if userState.isCreationInProgress {
                Circle()
                    .stroke(
                        AngularGradient(
                            gradient: Gradient(colors: [Color.blue.opacity(0.2), Color.blue]),
                            center: .center,
                            startAngle: .degrees(rotationAngle),
                            endAngle: .degrees(rotationAngle + 300)
                        ),
                        style: StrokeStyle(lineWidth: 4, lineCap: .round)
                    )
                    .frame(width: 60, height: 60)
                    .onAppear {
                        withAnimation(Animation.linear(duration: rotationDuration).repeatForever(autoreverses: false)) {
                            rotationAngle = 360
                        }
                    }
            } else {
                // 完成状态的圆圈
                Circle()
                    .stroke(Color.green, lineWidth: 3)
                    .frame(width: 60, height: 60)
            }
            
            // 悬浮球主体
            Circle()
                .fill(Color.white)
                .frame(width: 50, height: 50)
                .shadow(color: Color.black.opacity(0.2), radius: 3)
                .overlay(
                    Image("load")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 30, height: 30)
                )
        }
        .position(x: userState.floatingBallPosition.x + dragOffset.width, 
                  y: userState.floatingBallPosition.y + dragOffset.height)
        .gesture(
            DragGesture()
                .onChanged { gesture in
                    isDragging = true
                    dragOffset = gesture.translation
                }
                .onEnded { gesture in
                    isDragging = false
                    // 更新最终位置
                    userState.floatingBallPosition = CGPoint(
                        x: userState.floatingBallPosition.x + dragOffset.width,
                        y: userState.floatingBallPosition.y + dragOffset.height
                    )
                    dragOffset = .zero
                    
                    // 确保悬浮球不会超出屏幕边界
                    constrainToScreenEdges()
                }
        )
        .onTapGesture {
            navigateToCreationView()
        }
        .onAppear {
            // 开始轮询任务状态
            startPollingTaskStatus()
        }
        .onDisappear {
            // 停止轮询
            pollingTimer?.invalidate()
            pollingTimer = nil
        }
    }
    
    // 确保悬浮球在屏幕边缘内
    private func constrainToScreenEdges() {
        let screenWidth = UIScreen.main.bounds.width
        let screenHeight = UIScreen.main.bounds.height
        let ballRadius: CGFloat = 30 // 悬浮球半径
        
        // X坐标限制
        if userState.floatingBallPosition.x < ballRadius {
            userState.floatingBallPosition.x = ballRadius
        } else if userState.floatingBallPosition.x > screenWidth - ballRadius {
            userState.floatingBallPosition.x = screenWidth - ballRadius
        }
        
        // Y坐标限制
        if userState.floatingBallPosition.y < ballRadius + 44 { // 避开状态栏
            userState.floatingBallPosition.y = ballRadius + 44
        } else if userState.floatingBallPosition.y > screenHeight - ballRadius - 83 { // 避开标签栏
            userState.floatingBallPosition.y = screenHeight - ballRadius - 83
        }
    }
    
    // 开始轮询任务状态
    private func startPollingTaskStatus() {
        // 如果不在创作中，不需要轮询
        guard userState.isCreationInProgress else { return }
        
        
        // 开始定时检查
        pollingTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { _ in
            checkTaskStatus()
        }
    }
    
    // 检查任务状态
    private func checkTaskStatus() {
        // 确保taskId存在且不为空
        guard let taskId = userState.taskId, !taskId.isEmpty else { return }
        
        APIService.shared.getTaskStatus(taskId: taskId) { result in
            switch result {
            case .success(let statusInfo):
                let (status, outputPath, bgPath) = statusInfo
                
                DispatchQueue.main.async {
                    // 更新UserState中的状态
                    userState.processingStatus = status
                    userState.isCreationInProgress = status == "processing"
                    userState.isCreationComplete = status == "completed"
                    
                    if status == "completed" {
                        // 停止轮询
                        pollingTimer?.invalidate()
                        
                        // 保存路径信息
                        userState.outputImagePath = outputPath
                        userState.bgImagePath = bgPath
                        
                        // 加载输出图像
                        loadOutputImage(path: outputPath)
                    }
                }
                
            case .failure(_):
                DispatchQueue.main.async {
                    // 将状态设置为失败
                    userState.processingStatus = "failed"
                    userState.isCreationInProgress = false
                    
                    // 停止轮询
                    pollingTimer?.invalidate()
                }
            }
        }
    }
    
    // 加载输出图像
    private func loadOutputImage(path: String) {
        // 提取文件名
        let filename = path.components(separatedBy: "/").last ?? path
        // 直接使用文件名构建完整URL
        let urlString = "\(APIService.shared.baseURL)/images/file/output/\(filename)"
        
        guard let url = URL(string: urlString) else {
            DispatchQueue.main.async {
                userState.processingStatus = "failed"
            }
            return
        }
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            if let _ = error {
                DispatchQueue.main.async {
                    userState.processingStatus = "failed"
                }
                return
            }
            
            guard let data = data, let image = UIImage(data: data) else {
                DispatchQueue.main.async {
                    userState.processingStatus = "failed"
                }
                return
            }
            
            DispatchQueue.main.async {
                userState.outputImage = image
                
                // 同时保存原始图像，用于补光
                userState.originalOutputImage = image
            }
        }.resume()
    }
    
    // 导航到创作视图
    private func navigateToCreationView() {
        // 必须确保taskId存在
        guard let taskId = userState.taskId, !taskId.isEmpty else { return }
        
        // 临时隐藏悬浮球
        userState.showFloatingBall = false
        
        // 打开创作结果视图
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            
            // 找到当前最顶层的视图控制器
            var topViewController = rootViewController
            while let presentedVC = topViewController.presentedViewController {
                topViewController = presentedVC
            }
            
            // 创建处理结果视图
            let hostingController = UIHostingController(rootView: 
                RestoredImageProcessingView(
                    taskId: taskId,
                    initialProcessingStatus: userState.processingStatus,
                    initialOutputImage: userState.outputImage,
                    initialOutputImagePath: userState.outputImagePath,
                    initialBgImagePath: userState.bgImagePath
                )
            )
            hostingController.modalPresentationStyle = .fullScreen
            
            // 从最顶层控制器呈现创作结果视图
            topViewController.present(hostingController, animated: true)
        }
    }
} 
