import SwiftUI
import PhotosUI

struct StyleTransferView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userState: UserState

    @State private var selectedImage: UIImage?
    @State private var processedImage: UIImage?
    @State private var selectedStyle: ONNXStyleTransfer.StyleType = .hayao
    @State private var isProcessing = false
    @State private var showImagePicker = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var processingProgress: Double = 0.0

    private let onnxProcessor = ONNXStyleTransfer()
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景
                LinearGradient(
                    colors: [Color.white, Color.gray.opacity(0.1)],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 20) {
                        // 标题
                        HStack {
                            Button(action: {
                                presentationMode.wrappedValue.dismiss()
                            }) {
                                Image(systemName: "xmark")
                                    .font(.system(size: 20, weight: .medium))
                                    .foregroundColor(.black)
                            }
                            
                            Spacer()
                            
                            Text("风格转换")
                                .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                                .foregroundColor(.black)
                            
                            Spacer()
                            
                            // 占位符保持对称
                            Color.clear
                                .frame(width: 20, height: 20)
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 10)
                        
                        // 图片选择区域
                        VStack(spacing: 16) {
                            if let selectedImage = selectedImage {
                                HStack(spacing: 16) {
                                    // 原图
                                    VStack {
                                        Text("原图")
                                            .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                                            .foregroundColor(.black)
                                        
                                        Image(uiImage: selectedImage)
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .frame(maxWidth: 150, maxHeight: 150)
                                            .cornerRadius(12)
                                            .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
                                    }
                                    
                                    // 箭头
                                    Image(systemName: "arrow.right")
                                        .font(.system(size: 24, weight: .medium))
                                        .foregroundColor(.blue)
                                    
                                    // 处理后的图片
                                    VStack {
                                        Text("风格化")
                                            .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                                            .foregroundColor(.black)
                                        
                                        if let processedImage = processedImage {
                                            Image(uiImage: processedImage)
                                                .resizable()
                                                .aspectRatio(contentMode: .fit)
                                                .frame(maxWidth: 150, maxHeight: 150)
                                                .cornerRadius(12)
                                                .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
                                        } else {
                                            RoundedRectangle(cornerRadius: 12)
                                                .fill(Color.gray.opacity(0.2))
                                                .frame(width: 150, height: 150)
                                                .overlay(
                                                    VStack {
                                                        if isProcessing {
                                                            ProgressView()
                                                                .progressViewStyle(CircularProgressViewStyle())
                                                                .scaleEffect(1.2)
                                                            Text("处理中...")
                                                                .font(Font.custom("PingFang SC", size: 12))
                                                                .foregroundColor(.gray)
                                                                .padding(.top, 8)
                                                        } else {
                                                            Image(systemName: "photo")
                                                                .font(.system(size: 40))
                                                                .foregroundColor(.gray)
                                                            Text("选择风格")
                                                                .font(Font.custom("PingFang SC", size: 12))
                                                                .foregroundColor(.gray)
                                                        }
                                                    }
                                                )
                                        }
                                    }
                                }
                            } else {
                                // 图片选择按钮
                                Button(action: {
                                    showImagePicker = true
                                }) {
                                    VStack(spacing: 12) {
                                        Image(systemName: "photo.badge.plus")
                                            .font(.system(size: 50))
                                            .foregroundColor(.blue)
                                        
                                        Text("选择图片")
                                            .font(Font.custom("PingFang SC", size: 18).weight(.medium))
                                            .foregroundColor(.blue)
                                        
                                        Text("支持 JPG、PNG 格式")
                                            .font(Font.custom("PingFang SC", size: 14))
                                            .foregroundColor(.gray)
                                    }
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 200)
                                    .background(
                                        RoundedRectangle(cornerRadius: 16)
                                            .stroke(Color.blue.opacity(0.3), style: StrokeStyle(lineWidth: 2, dash: [8]))
                                            .background(Color.blue.opacity(0.05))
                                    )
                                }
                            }
                        }
                        .padding(.horizontal, 20)
                        
                        // 风格选择
                        if selectedImage != nil {
                            VStack(alignment: .leading, spacing: 16) {
                                Text("选择风格")
                                    .font(Font.custom("PingFang SC", size: 18).weight(.bold))
                                    .foregroundColor(.black)
                                    .padding(.horizontal, 20)
                                
                                LazyVGrid(columns: [
                                    GridItem(.flexible()),
                                    GridItem(.flexible())
                                ], spacing: 16) {
                                    ForEach(ONNXStyleTransfer.StyleType.allCases, id: \.self) { style in
                                        StyleOptionCard(
                                            style: style,
                                            isSelected: selectedStyle == style,
                                            onTap: {
                                                selectedStyle = style
                                                processedImage = nil // 清除之前的结果
                                            }
                                        )
                                    }
                                }
                                .padding(.horizontal, 20)
                            }
                        }

                        // 处理按钮
                        if selectedImage != nil {
                            VStack(spacing: 16) {
                                Button(action: {
                                    processImage()
                                }) {
                                    HStack {
                                        if isProcessing {
                                            ProgressView()
                                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                                .scaleEffect(0.8)
                                            Text("处理中...")
                                        } else {
                                            Image(systemName: "paintbrush.fill")
                                            Text("开始风格转换")
                                        }
                                    }
                                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 50)
                                    .background(
                                        LinearGradient(
                                            colors: isProcessing ? [Color.gray] : [Color.blue, Color.purple],
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                                    .cornerRadius(25)
                                    .shadow(color: Color.blue.opacity(0.3), radius: 5, x: 0, y: 3)
                                }
                                .disabled(isProcessing)
                                .padding(.horizontal, 20)
                                
                                // 重新选择图片按钮
                                Button(action: {
                                    selectedImage = nil
                                    processedImage = nil
                                    showImagePicker = true
                                }) {
                                    HStack {
                                        Image(systemName: "photo")
                                        Text("重新选择图片")
                                    }
                                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                                    .foregroundColor(.blue)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 50)
                                    .background(Color.white)
                                    .cornerRadius(25)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 25)
                                            .stroke(Color.blue, lineWidth: 1)
                                    )
                                }
                                .padding(.horizontal, 20)
                            }
                        }
                        
                        // 保存按钮
                        if let processedImage = processedImage {
                            Button(action: {
                                saveImageToPhotos(processedImage)
                            }) {
                                HStack {
                                    Image(systemName: "square.and.arrow.down")
                                    Text("保存到相册")
                                }
                                .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 50)
                                .background(
                                    LinearGradient(
                                        colors: [Color.green, Color.mint],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .cornerRadius(25)
                                .shadow(color: Color.green.opacity(0.3), radius: 5, x: 0, y: 3)
                            }
                            .padding(.horizontal, 20)
                        }
                        
                        Spacer(minLength: 100)
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedImage)
        }
        .alert("提示", isPresented: $showAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }

    private func processImage() {
        guard let image = selectedImage else { return }

        isProcessing = true
        processingProgress = 0.0

        DispatchQueue.global(qos: .userInitiated).async {
            let result = onnxProcessor.processImage(image, styleType: selectedStyle)

            DispatchQueue.main.async {
                isProcessing = false

                if let processedImage = result {
                    self.processedImage = processedImage
                } else {
                    alertMessage = "图片处理失败，请检查ONNX模型是否正确加载"
                    showAlert = true
                }
            }
        }
    }
    
    private func saveImageToPhotos(_ image: UIImage) {
        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        alertMessage = "图片已保存到相册"
        showAlert = true
    }
}

// 风格选项卡片
struct StyleOptionCard: View {
    let style: ONNXStyleTransfer.StyleType
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                // 风格图标
                Image(systemName: getStyleIcon())
                    .font(.system(size: 30))
                    .foregroundColor(isSelected ? .white : .blue)

                Text(style.rawValue)
                    .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                    .foregroundColor(isSelected ? .white : .black)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 100)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue : Color.white)
                    .shadow(color: .black.opacity(0.1), radius: 3, x: 0, y: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.blue : Color.gray.opacity(0.3), lineWidth: isSelected ? 2 : 1)
            )
        }
    }

    private func getStyleIcon() -> String {
        switch style {
        case .hayao:
            return "leaf.fill"
        case .shinkai:
            return "cloud.fill"
        case .portraitSketch:
            return "pencil.tip"
        case .cute:
            return "heart.fill"
        }
    }
}

// 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        picker.allowsEditing = false
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.presentationMode.wrappedValue.dismiss()
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}
