import SwiftUI
import WebKit

struct WebViewContainer: View {
    let urlString: String
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            WebView(urlString: urlString)
                .navigationBarTitle("", displayMode: .inline)
                .navigationBarItems(trailing: <PERSON><PERSON>(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Text("关闭")
                        .foregroundColor(.blue)
                })
        }
    }
}

struct WebView: UIViewRepresentable {
    let urlString: String
    
    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView()
        webView.navigationDelegate = context.coordinator
        
        if let url = URL(string: urlString) {
            let request = URLRequest(url: url)
            webView.load(request)
        }
        
        return webView
    }
    
    func updateUIView(_ uiView: WKWebView, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, WKNavigationDelegate {
        var parent: WebView
        
        init(_ parent: WebView) {
            self.parent = parent
        }
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            // 网页加载完成后的处理
        }
        
        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            // 网页加载失败的处理
            print("网页加载失败: \(error.localizedDescription)")
        }
    }
}

struct WebViewContainer_Previews: PreviewProvider {
    static var previews: some View {
        WebViewContainer(urlString: "https://qianmianshixiao.com:5190/privacy")
    }
} 