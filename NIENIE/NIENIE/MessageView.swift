import SwiftUI

struct MessageView: View {
    @Environment(\.dismiss) var dismiss
    
    // 假设当前用户ID
    @AppStorage("userId") private var userId: Int = 1
    
    var body: some View {
        // 使用会话列表视图
        ConversationListView(userId: userId)
            .navigationBarHidden(true)
    }
}

struct MessageView_Previews: PreviewProvider {
    static var previews: some View {
        MessageView()
    }
} 