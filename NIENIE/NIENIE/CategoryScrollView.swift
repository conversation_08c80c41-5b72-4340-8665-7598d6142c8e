import SwiftUI

struct CategoryScrollView: View {
    @Binding var selectedCategory: Int
    @Binding var isDragging: Bool
    let onCategoryChanged: (Int) -> Void
    
    private let categories = [
        CategoryItem(id: -1, name: "全部", icon: "square.grid.2x2", colors: [Color.blue, Color.purple]),
        CategoryItem(id: 0, name: "风景", icon: "mountain.2", colors: [Color.green, Color.mint]),
        CategoryItem(id: 1, name: "动漫", icon: "person.fill", colors: [Color.pink, Color.red]),
        CategoryItem(id: 2, name: "古风", icon: "building.columns", colors: [Color.orange, Color.yellow]),
        CategoryItem(id: 3, name: "二次元", icon: "star.fill", colors: [Color.purple, Color.indigo]),
        CategoryItem(id: 4, name: "其他", icon: "ellipsis.circle", colors: [Color.gray, Color.secondary])
    ]
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(categories, id: \.id) { category in
                    CategoryButton(
                        category: category,
                        isSelected: selectedCategory == category.id,
                        isDragging: $isDragging
                    ) {
                        if !isDragging && selectedCategory != category.id {
                            selectedCategory = category.id
                            onCategoryChanged(category.id)
                        }
                    }
                }
            }
            .padding(.horizontal, 15)
        }
        .padding(.top, 8)
        .padding(.bottom, 8)
        .background(Color.gray.opacity(0.05))
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
        .padding(.horizontal, 10)
    }
}

struct CategoryItem {
    let id: Int
    let name: String
    let icon: String
    let colors: [Color]
}

struct CategoryButton: View {
    let category: CategoryItem
    let isSelected: Bool
    @Binding var isDragging: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: {
            if !isDragging {
                action()
            }
        }) {
            HStack(spacing: 4) {
                Image(systemName: category.icon)
                    .font(.system(size: 12))
                Text(category.name)
                    .font(Font.custom("PingFang SC", size: 12).weight(.medium))
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(
                Group {
                    if isSelected {
                        LinearGradient(
                            colors: category.colors,
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    } else {
                        Color.clear
                    }
                }
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.clear : Color.gray.opacity(0.3), lineWidth: 1)
            )
            .cornerRadius(12)
            .shadow(color: isSelected ? category.colors.first?.opacity(0.3) ?? Color.clear : Color.clear, radius: 2, x: 0, y: 1)
        }
        .simultaneousGesture(
            DragGesture()
                .onChanged { value in
                    let distance = sqrt(pow(value.translation.width, 2) + pow(value.translation.height, 2))
                    if distance > 5 {
                        isDragging = true
                    }
                }
                .onEnded { _ in
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        isDragging = false
                    }
                }
        )
    }
}
