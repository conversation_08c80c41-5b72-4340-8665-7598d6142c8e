import Foundation
import UIKit
import UserNotifications

// 消息API响应模型
struct APIResponse<T: Codable>: Codable {
    let success: Bool
    let message: String?
    let data: T?
    
    // 添加自定义解码器以处理不同的响应格式
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        success = try container.decode(Bool.self, forKey: .success)
        message = try container.decodeIfPresent(String.self, forKey: .message)
        data = try container.decodeIfPresent(T.self, forKey: .data)
    }
    
    enum CodingKeys: String, CodingKey {
        case success
        case message
        case data
    }
}

// 消息响应模型
struct MessageResponse: Codable {
    let message_id: Int
    let conversation_id: String
    let created_at: Date
}

// APIService的消息扩展
extension APIService {
    
    // 获取用户的会话列表
    func getUserConversations(userId: Int, completion: @escaping (Result<[Conversation], Error>) -> Void) {
        let url = URL(string: "https://sorealhuman.com:8888/messages/conversations/\(userId)")!
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                
                let response = try decoder.decode(APIResponse<[Conversation]>.self, from: data)
                if response.success {
                    // 保存会话到本地
                    self.saveConversationsToLocal(userId: userId, conversations: response.data ?? [])
                    completion(.success(response.data ?? []))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: response.message ?? "未知错误"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 获取会话消息
    func getConversationMessages(conversationId: String, userId: Int, completion: @escaping (Result<[Message], Error>) -> Void) {
        let url = URL(string: "https://sorealhuman.com:8888/messages/conversation/\(conversationId)/user/\(userId)")!
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的响应"])))
                return
            }
            
            if httpResponse.statusCode == 404 {
                // 会话不存在，返回空数组
                completion(.success([]))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                
                let response = try decoder.decode(APIResponse<[Message]>.self, from: data)
                if response.success {
                    let messages = response.data ?? []
                    let messagesWithSelfFlag = messages.map { message in
                        var msg = message
                        msg.is_self = message.sender_id == userId
                        return msg
                    }
                    
                    // 保存消息到本地
                    self.saveMessageToLocal(conversationId: conversationId, messages: messagesWithSelfFlag)
                    completion(.success(messagesWithSelfFlag))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: response.message ?? "未知错误"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 发送消息
    func sendMessage(senderId: Int, receiverId: Int, content: String, completion: @escaping (Result<Message, Error>) -> Void) {
        let url = URL(string: "https://sorealhuman.com:8888/messages/send")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let parameters: [String: Any] = [
            "sender_id": senderId,
            "receiver_id": receiverId,
            "content": content
        ]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
        } catch {
            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "参数编码失败"])))
            return
        }
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                
                let response = try decoder.decode(APIResponse<MessageResponse>.self, from: data)
                if response.success {
                    // 创建新消息对象
                    let newMessage = Message(
                        message_id: response.data?.message_id ?? 0,
                        conversation_id: response.data?.conversation_id ?? "",
                        sender_id: senderId,
                        receiver_id: receiverId,
                        content: content,
                        status: 1,
                        created_at: response.data?.created_at ?? Date(),
                        is_self: true
                    )
                    
                    // 更新本地缓存
                    self.saveMessageToLocal(conversationId: response.data?.conversation_id ?? "", message: newMessage)
                    self.updateConversationWithMessage(userId: senderId, message: newMessage)
                    
                    completion(.success(newMessage))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: response.message ?? "未知错误"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 删除消息
    func deleteMessage(userId: Int, messageId: Int, completion: @escaping (Result<Bool, Error>) -> Void) {
        let url = URL(string: "https://sorealhuman.com:8888/messages/message")!
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let parameters: [String: Any] = [
            "user_id": userId,
            "message_id": messageId
        ]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
        } catch {
            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "参数编码失败"])))
            return
        }
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            completion(.success(true))
        }.resume()
    }
    
    // 删除会话
    func deleteConversation(userId: Int, conversationId: String, completion: @escaping (Result<Bool, Error>) -> Void) {
        let url = URL(string: "https://sorealhuman.com:8888/messages/conversation")!
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let parameters: [String: Any] = [
            "user_id": userId,
            "conversation_id": conversationId
        ]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
        } catch {
            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "参数编码失败"])))
            return
        }
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            completion(.success(true))
        }.resume()
    }
    
    // 注册推送通知
    func registerForPushNotifications() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if granted {
                print("通知权限已授予")
                DispatchQueue.main.async {
                    UIApplication.shared.registerForRemoteNotifications()
                }
            } else if let error = error {
                print("请求通知权限失败: \(error.localizedDescription)")
            }
        }
    }
    
    // 注册设备令牌
    func registerDeviceToken(userId: Int, token: String, completion: @escaping (Result<Bool, Error>) -> Void) {
        let url = URL(string: "https://sorealhuman.com:8888/messages/register_device_token")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let parameters: [String: Any] = [
            "user_id": userId,
            "device_token": token
        ]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
        } catch {
            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "参数编码失败"])))
            return
        }
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            completion(.success(true))
        }.resume()
    }
    
    // 处理推送通知（已优化，不再发送额外的API请求）
    func handlePushNotification(conversationId: String, messageId: Int) {
        // 推送通知现在只用于触发本地通知，实际消息更新通过WebSocket处理
        print("收到推送通知 - 会话: \(conversationId), 消息: \(messageId)")

        // 如果WebSocket未连接，可以考虑重新连接
        if !WebSocketManager.shared.isConnected {
            let userId = UserDefaults.standard.integer(forKey: "userId")
            if userId > 0 {
                WebSocketManager.shared.connect(userId: userId)
            }
        }
    }
    
    // 保存消息到本地
    func saveMessageToLocal(conversationId: String, message: Message) {
        do {
            // 先读取现有消息
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: true)
            let fileURL = documentsDirectory.appendingPathComponent("messages_\(conversationId).json")
            
            var messages: [Message] = []
            
            // 如果文件存在，读取现有消息
            if fileManager.fileExists(atPath: fileURL.path) {
                let data = try Data(contentsOf: fileURL)
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                messages = try decoder.decode([Message].self, from: data)
            }
            
            // 添加新消息
            messages.append(message)
            
            // 保存回文件
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(messages)
            try data.write(to: fileURL)
        } catch {
            print("保存消息到本地失败: \(error)")
        }
    }
    
    // 保存多条消息到本地
    func saveMessageToLocal(conversationId: String, messages: [Message]) {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: true)
            let fileURL = documentsDirectory.appendingPathComponent("messages_\(conversationId).json")
            
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(messages)
            try data.write(to: fileURL)
        } catch {
            print("保存消息到本地失败: \(error)")
        }
    }
    
    // 更新会话列表中的最新消息
    func updateConversationWithMessage(userId: Int, message: Message) {
        do {
            // 读取现有会话列表
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: true)
            let fileURL = documentsDirectory.appendingPathComponent("conversations_\(userId).json")
            
            var conversations: [Conversation] = []
            
            // 如果文件存在，读取现有会话
            if fileManager.fileExists(atPath: fileURL.path) {
                let data = try Data(contentsOf: fileURL)
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                conversations = try decoder.decode([Conversation].self, from: data)
            }
            
            // 获取对方用户ID
            let otherUserId = message.sender_id == userId ? message.receiver_id : message.sender_id
            
            // 查找是否已有该会话
            if let index = conversations.firstIndex(where: { $0.conversation_id == message.conversation_id }) {
                // 更新现有会话
                conversations[index].latest_message = message.content
                conversations[index].latest_message_time = message.created_at
                
                // 如果是对方发送的消息，增加未读数
                if message.sender_id != userId {
                    conversations[index].unread_count += 1
                }
                
                // 将该会话移到列表顶部
                let conversation = conversations.remove(at: index)
                conversations.insert(conversation, at: 0)
            } else {
                // 创建新会话
                // 这里需要获取对方用户信息
                getUserInfo(userId: otherUserId) { result in
                    switch result {
                    case .success(let userInfo):
                        let newConversation = Conversation(
                            conversation_id: message.conversation_id,
                            user_id: otherUserId,
                            username: userInfo.username,
                            avatar_url: userInfo.avatar,
                            latest_message: message.content,
                            latest_message_time: message.created_at,
                            unread_count: message.sender_id == userId ? 0 : 1
                        )
                        
                        // 将新会话添加到列表顶部
                        var updatedConversations = conversations
                        updatedConversations.insert(newConversation, at: 0)
                        
                        // 保存更新后的会话列表
                        self.saveConversationsToLocal(userId: userId, conversations: updatedConversations)
                    case .failure(let error):
                        print("获取用户信息失败: \(error)")
                    }
                }
                return
            }
            
            // 保存更新后的会话列表
            saveConversationsToLocal(userId: userId, conversations: conversations)
        } catch {
            print("更新会话列表失败: \(error)")
        }
    }
    
    // 保存会话列表到本地
    func saveConversationsToLocal(userId: Int, conversations: [Conversation]) {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: true)
            let fileURL = documentsDirectory.appendingPathComponent("conversations_\(userId).json")
            
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(conversations)
            try data.write(to: fileURL)
        } catch {
            print("保存会话列表到本地失败: \(error)")
        }
    }
    
    // 获取用户信息 (临时实现，实际应该调用用户信息API)
    func getUserInfo(userId: Int, completion: @escaping (Result<UserData, Error>) -> Void) {
        // 这里应该调用实际的用户信息API
        // 临时返回一个假数据
        let userData = UserData(userId: userId, username: "用户\(userId)", avatar: "", token: "")
        completion(.success(userData))
    }
} 