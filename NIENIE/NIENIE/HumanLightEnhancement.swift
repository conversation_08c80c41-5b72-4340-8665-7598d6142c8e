import SwiftUI
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins
import CoreML
import Accelerate

// MARK: - 新增类型定义
enum CurveType: String, CaseIterable, Identifiable {
    case portrait = "人像曲线"
    case global = "全局曲线"
    var id: Self { self }
}

// MARK: - 主视图
struct LightEnhancementView: View {
    let image: UIImage
    let taskId: String // 添加taskID
    let onBack: () -> Void
    let onSave: (UIImage) -> Void
    
    // 保存原始图像，确保每次都从原始图像开始补光
    @State private var originalImage: UIImage
    @State private var processedImage: UIImage?
    @State private var isProcessing = false
    @State private var featherRadius: CGFloat = 30
    
    // 全局曲线控制
    @State private var globalShadows: CGFloat = 0.0
    @State private var globalMidtones: CGFloat = 0.0
    @State private var globalHighlights: CGFloat = 0.0
    
    // 人像曲线控制
    @State private var portraitShadows: CGFloat = 0.0
    @State private var portraitMidtones: CGFloat = 0.0
    @State private var portraitHighlights: CGFloat = 0.0

    @State private var segmentationMask: VNPixelBufferObservation?
    @State private var beforeAfterPosition: CGFloat = 0.5
    @State private var alertMessage: String = ""
    @State private var showAlert: Bool = false
    @State private var isInitialProcessing = true
    @State private var selectedCurve: CurveType = .portrait
    
    init(image: UIImage, taskId: String, onBack: @escaping () -> Void, onSave: @escaping (UIImage) -> Void) {
        self.image = image
        self.taskId = taskId
        self.onBack = onBack
        self.onSave = onSave
        // 初始化时保存原始图像
        self._originalImage = State(initialValue: image)
    }
    
    var body: some View {
        ZStack {
            // 背景颜色
            Color.white.ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 标题栏
                headerView
                
                // 图像显示区域
                imageDisplayView
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                
                // 控制面板
                controlPanelView
            }
        }
        .navigationBarHidden(true)
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
        .onAppear {
            // 初始化时先进行人体分割
            performHumanSegmentation()
            // 加载保存的参数
            loadEnhancementParameters()
        }
    }
    
    // MARK: - 子视图
    private var headerView: some View {
        HStack {
            // 返回按钮
            Button(action: onBack) {
                Image(systemName: "arrow.left")
                    .font(.system(size: 18))
                    .foregroundColor(.black)
            }
            
            Spacer()
            
            // 标题
            Text("人物补光")
                .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                .foregroundColor(.black)
            
            Spacer()
            
            // 保存按钮
            Button(action: {
                if let finalImage = processedImage {
                    saveEnhancementParameters() // 保存参数
                    onSave(finalImage)
                }
            }) {
                Image(systemName: "checkmark")
                    .font(.system(size: 18))
                    .foregroundColor(.black)
            }
            .disabled(processedImage == nil)
            .opacity(processedImage == nil ? 0.5 : 1.0)
        }
        .padding(.horizontal, 16)
        .padding(.top, 16)
        .padding(.bottom, 20)
    }
    
    private var imageDisplayView: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景
                Color.black.opacity(0.05)
                    .cornerRadius(8)
                
                if let processed = processedImage {
                    // 始终显示对比模式
                    BeforeAfterSlider(beforeImage: originalImage, afterImage: processed, sliderPosition: $beforeAfterPosition)
                        .cornerRadius(8)
                } else {
                    // 显示原始图像
                    Image(uiImage: originalImage)
                        .resizable()
                        .scaledToFit()
                        .cornerRadius(8)
                }
                
                if isProcessing || isInitialProcessing {
                    // 加载指示器
                    VStack {
                        ProgressView()
                            .scaleEffect(1.5)
                        Text("处理中...")
                            .font(.system(size: 16))
                            .foregroundColor(.gray)
                            .padding(.top, 10)
                    }
                }
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
        }
    }
    
    private var controlPanelView: some View {
        VStack(spacing: 15) {
            CurveSlider(title: "羽化半径", value: $featherRadius, range: 0...100, step: 1) {
                applyLightEnhancement()
            }
            .disabled(isProcessing)
            .opacity(isProcessing ? 0.5 : 1.0)

            Picker("曲线类型", selection: $selectedCurve) {
                ForEach(CurveType.allCases) { type in
                    Text(type.rawValue)
                }
            }
            .pickerStyle(.segmented)
            .padding(.horizontal, 20)

            ZStack {
                VStack {
                    CurveSlider(title: "阴影", value: $globalShadows, range: -0.5...0.5, displayMultiplier: 100, unit: "") { applyLightEnhancement() }
                    CurveSlider(title: "中间调", value: $globalMidtones, range: -0.5...0.5, displayMultiplier: 100, unit: "") { applyLightEnhancement() }
                    CurveSlider(title: "高光", value: $globalHighlights, range: -0.5...0.5, displayMultiplier: 100, unit: "") { applyLightEnhancement() }
                }
                .opacity(selectedCurve == .global ? 1 : 0)
                .allowsHitTesting(selectedCurve == .global)

                VStack {
                    CurveSlider(title: "阴影", value: $portraitShadows, range: -0.5...0.5, displayMultiplier: 100, unit: "") { applyLightEnhancement() }
                    CurveSlider(title: "中间调", value: $portraitMidtones, range: -0.5...0.5, displayMultiplier: 100, unit: "") { applyLightEnhancement() }
                    CurveSlider(title: "高光", value: $portraitHighlights, range: -0.5...0.5, displayMultiplier: 100, unit: "") { applyLightEnhancement() }
                }
                .opacity(selectedCurve == .portrait ? 1 : 0)
                .allowsHitTesting(selectedCurve == .portrait)
            }
            .disabled(isProcessing)
            .opacity(isProcessing ? 0.5 : 1.0)
        }
        .padding(.vertical, 20)
        .background(Color.white)
        .cornerRadius(20, corners: [.topLeft, .topRight])
        .shadow(color: .gray.opacity(0.2), radius: 5, y: -5)
        .animation(.easeInOut(duration: 0.2), value: isProcessing)
    }
    
    // MARK: - 逻辑处理
    
    private func loadEnhancementParameters() {
        let keyPrefix = "enhancement_\(taskId)_"
        let defaults = UserDefaults.standard
        
        featherRadius = CGFloat(defaults.double(forKey: "\(keyPrefix)featherRadius"))
        globalShadows = CGFloat(defaults.double(forKey: "\(keyPrefix)globalShadows"))
        globalMidtones = CGFloat(defaults.double(forKey: "\(keyPrefix)globalMidtones"))
        globalHighlights = CGFloat(defaults.double(forKey: "\(keyPrefix)globalHighlights"))
        portraitShadows = CGFloat(defaults.double(forKey: "\(keyPrefix)portraitShadows"))
        portraitMidtones = CGFloat(defaults.double(forKey: "\(keyPrefix)portraitMidtones"))
        portraitHighlights = CGFloat(defaults.double(forKey: "\(keyPrefix)portraitHighlights"))
        
        // 如果是第一次加载，设置默认值
        if defaults.object(forKey: "\(keyPrefix)featherRadius") == nil {
            featherRadius = 30
        }
    }
    
    private func saveEnhancementParameters() {
        let keyPrefix = "enhancement_\(taskId)_"
        let defaults = UserDefaults.standard
        
        defaults.set(Double(featherRadius), forKey: "\(keyPrefix)featherRadius")
        defaults.set(Double(globalShadows), forKey: "\(keyPrefix)globalShadows")
        defaults.set(Double(globalMidtones), forKey: "\(keyPrefix)globalMidtones")
        defaults.set(Double(globalHighlights), forKey: "\(keyPrefix)globalHighlights")
        defaults.set(Double(portraitShadows), forKey: "\(keyPrefix)portraitShadows")
        defaults.set(Double(portraitMidtones), forKey: "\(keyPrefix)portraitMidtones")
        defaults.set(Double(portraitHighlights), forKey: "\(keyPrefix)portraitHighlights")
    }

    
    // 检测人体区域
    private func performHumanSegmentation() {
        isInitialProcessing = true
        
        DispatchQueue.global(qos: .userInitiated).async {
            // 创建人体分割请求
            let segmentationRequest = VNGeneratePersonSegmentationRequest { request, error in
                // 检查是否有错误
                if let error = error {
                    DispatchQueue.main.async {
                        isInitialProcessing = false
                        alertMessage = "人体分割错误: \(error.localizedDescription)"
                        showAlert = true
                    }
                    return
                }
                
                // 获取检测结果
                guard let observations = request.results as? [VNPixelBufferObservation],
                      let observation = observations.first else {
                    DispatchQueue.main.async {
                        isInitialProcessing = false
                        alertMessage = "未检测到人体，无法进行补光"
                        showAlert = true
                    }
                    return
                }
                
                // 确保在主线程更新UI
                DispatchQueue.main.async {
                    // 将检测结果保存起来
                    self.segmentationMask = observation
                    
                    // 应用补光效果
                    applyLightEnhancement()
                    
                    // 完成初始处理
                    isInitialProcessing = false
                }
            }
            
            // 配置分割请求的质量级别
            segmentationRequest.qualityLevel = .balanced
            
            // 创建图像处理请求处理器
            guard let cgImage = originalImage.cgImage else {
                DispatchQueue.main.async {
                    isInitialProcessing = false
                    alertMessage = "无法获取图像数据"
                    showAlert = true
                }
                return
            }
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            
            // 执行请求
            do {
                try handler.perform([segmentationRequest])
            } catch {
                DispatchQueue.main.async {
                    isInitialProcessing = false
                    alertMessage = "处理图像时出错: \(error.localizedDescription)"
                    showAlert = true
                }
            }
        }
    }
    
    // 应用补光效果
    private func applyLightEnhancement() {
        // 如果正在处理中，不重复处理
        if isProcessing {
            return
        }
        
        // 标记开始处理
        isProcessing = true
        
        DispatchQueue.global(qos: .userInitiated).async {
            guard let maskObservation = self.segmentationMask else {
                DispatchQueue.main.async {
                    self.isProcessing = false
                }
                return
            }
            
            // 应用补光和轮廓效果
            let globalCurvePoints = createCurvePoints(shadows: globalShadows, midtones: globalMidtones, highlights: globalHighlights)
            let portraitCurvePoints = createCurvePoints(shadows: portraitShadows, midtones: portraitMidtones, highlights: portraitHighlights)
            
            let enhancedImage = HumanLightEnhancer.enhanceImage(
                image: originalImage,
                segmentation: maskObservation,
                featherRadius: featherRadius,
                globalCurvePoints: globalCurvePoints,
                portraitCurvePoints: portraitCurvePoints
            )
            
            // 在主线程更新UI
            DispatchQueue.main.async {
                self.processedImage = enhancedImage
                self.isProcessing = false
            }
        }
    }
    
    // 从滑块值创建色调曲线点
    private func createCurvePoints(shadows: CGFloat, midtones: CGFloat, highlights: CGFloat) -> [CIVector] {
        // 基本点，确保曲线的起点和终点
        let p0 = CIVector(x: 0.0, y: 0.0)
        let p4 = CIVector(x: 1.0, y: 1.0)
        
        // 根据滑块值调整中间点
        // X值是固定的，代表色调范围（阴影、中间、高光）
        // Y值是调整后的亮度
        let p1 = CIVector(x: 0.25, y: max(0, min(1, 0.25 + shadows)))
        let p2 = CIVector(x: 0.5, y: max(0, min(1, 0.5 + midtones)))
        let p3 = CIVector(x: 0.75, y: max(0, min(1, 0.75 + highlights)))
        
        // 如果所有调整都为0，可以返回一个简化的线性曲线，以提高效率
        if shadows == 0.0 && midtones == 0.0 && highlights == 0.0 {
            return [p0, p4]
        }
        
        return [p0, p1, p2, p3, p4]
    }
}

// MARK: - 可重用的曲线滑块视图
struct CurveSlider: View {
    let title: String
    @Binding var value: CGFloat
    let range: ClosedRange<CGFloat>
    var step: CGFloat = 0.01
    var displayMultiplier: CGFloat = 1.0
    var unit: String = ""
    let onEditingChanged: () -> Void

    var body: some View {
        HStack {
            Text(title)
                .font(Font.custom("PingFang SC", size: 16))
                .foregroundColor(.black)
                .frame(width: 80, alignment: .leading)
            
            GeometryReader { geo in
                let width = geo.size.width
                let percentage = (value - range.lowerBound) / (range.upperBound - range.lowerBound)
                let knobPosition = width * percentage
                
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: 4)
                        .cornerRadius(2)
                    
                    Rectangle()
                        .fill(Color(red: 0.98, green: 0.85, blue: 0.37))
                        .frame(width: knobPosition, height: 4)
                        .cornerRadius(2)
                    
                    Image(systemName: "circle.fill")
                        .resizable()
                        .frame(width: 20, height: 20)
                        .foregroundColor(.white)
                        .shadow(radius: 2)
                        .overlay(Circle().stroke(Color.gray.opacity(0.3), lineWidth: 0.5))
                        .position(x: knobPosition, y: geo.size.height / 2)
                        .gesture(
                            DragGesture()
                                .onChanged { gestureValue in
                                    let newPercentage = max(0, min(1, gestureValue.location.x / width))
                                    let newValue = range.lowerBound + newPercentage * (range.upperBound - range.lowerBound)
                                    self.value = newValue
                                }
                                .onEnded { _ in
                                    onEditingChanged()
                                }
                        )
                }
            }
            .frame(height: 30)
            
            Text("\(Int(value * displayMultiplier))\(unit)")
                .font(.system(size: 14))
                .foregroundColor(.gray)
                .frame(width: 40, alignment: .trailing)
        }
        .padding(.horizontal, 20)
    }
}

// MARK: - 人物补光增强处理类
class HumanLightEnhancer {
    // 使用双曲线系统（全局和人像）进行光照增强
    static func enhanceImage(
        image: UIImage,
        segmentation: VNPixelBufferObservation,
        featherRadius: CGFloat,
        globalCurvePoints: [CIVector],
        portraitCurvePoints: [CIVector]
    ) -> UIImage {
        guard let cgImage = image.cgImage else {
            return image
        }
        
        let ciImage = CIImage(cgImage: cgImage)
        let context = CIContext(options: nil)
        
        // 1. 应用全局曲线调整整个图像
        let globallyAdjustedImage = applyCurve(to: ciImage, with: globalCurvePoints)
        
        // 2. 在全局调整的基础上，应用人像曲线，创建仅针对人像的增强版本
        let portraitEnhancedImage = applyCurve(to: globallyAdjustedImage, with: portraitCurvePoints)
        
        // 3. 从分割结果创建遮罩
        var maskCIImage = CIImage(cvPixelBuffer: segmentation.pixelBuffer)
        
        // 4. 将遮罩缩放到与原图相同大小
        let imageSize = ciImage.extent.size
        let maskSize = maskCIImage.extent.size
        let scaleX = imageSize.width / maskSize.width
        let scaleY = imageSize.height / maskSize.height
        let scaleTransform = CGAffineTransform(scaleX: scaleX, y: scaleY)
        maskCIImage = maskCIImage.transformed(by: scaleTransform)
        
        // 5. 羽化（模糊）遮罩以创建平滑过渡
        let featheredMask = maskCIImage.applyingFilter("CIGaussianBlur", parameters: [kCIInputRadiusKey: featherRadius])
        
        // 6. 使用羽化后的遮罩混合全局调整图和人像增强图
        // 背景是全局调整后的图，前景是额外增强了人像的图
        let blendFilter = CIFilter(name: "CIBlendWithMask")!
        blendFilter.setValue(portraitEnhancedImage, forKey: kCIInputImageKey)
        blendFilter.setValue(globallyAdjustedImage, forKey: kCIInputBackgroundImageKey)
        blendFilter.setValue(featheredMask, forKey: kCIInputMaskImageKey)
        
        guard let blendedImage = blendFilter.outputImage else {
            return image
        }
        
        // 从CIImage创建最终的CGImage和UIImage
        guard let outputCGImage = context.createCGImage(blendedImage, from: blendedImage.extent) else {
            return image
        }
        
        return UIImage(cgImage: outputCGImage, scale: image.scale, orientation: image.imageOrientation)
    }

    // 应用色调曲线滤镜
    private static func applyCurve(to image: CIImage, with points: [CIVector]) -> CIImage {
        // 如果点为空或只有一个点，则无法形成曲线，返回原图
        guard points.count > 1 else { return image }

        if let curveFilter = CIFilter(name: "CIToneCurve") {
            curveFilter.setValue(image, forKey: kCIInputImageKey)
            
            // CIToneCurve 最多接受5个点，并且需要按 inputPoint0, inputPoint1... 的键设置
            // 我们确保点按x坐标排序
            let sortedPoints = points.sorted { $0.x < $1.x }
            
            for i in 0..<min(sortedPoints.count, 5) {
                curveFilter.setValue(sortedPoints[i], forKey: "inputPoint\(i)")
            }
            
            if let outputImage = curveFilter.outputImage {
                return outputImage.cropped(to: image.extent)
            }
        }
        return image
    }
}

// MARK: - 前后对比滑块控件
struct BeforeAfterSlider: View {
    let beforeImage: UIImage
    let afterImage: UIImage
    @Binding var sliderPosition: CGFloat
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 处理后的图像（底层）
                Image(uiImage: afterImage)
                    .resizable()
                    .scaledToFit()
                    .frame(width: geometry.size.width, height: geometry.size.height)
                
                // 原图（上层，被裁剪）
                Image(uiImage: beforeImage)
                    .resizable()
                    .scaledToFit()
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .clipShape(
                        Rectangle()
                            .path(in: CGRect(x: 0, y: 0, width: geometry.size.width * sliderPosition, height: geometry.size.height))
                    )
                
                // 分割线
                Rectangle()
                    .fill(Color.white)
                    .frame(width: 2, height: geometry.size.height)
                    .position(x: geometry.size.width * sliderPosition, y: geometry.size.height / 2)
                    .shadow(color: Color.black.opacity(0.5), radius: 2, x: 0, y: 0)
                
                // 滑块控制柄
                Circle()
                    .fill(Color.white)
                    .frame(width: 30, height: 30)
                    .shadow(color: Color.black.opacity(0.5), radius: 2, x: 0, y: 0)
                    .overlay(
                        HStack(spacing: 3) {
                            Image(systemName: "arrow.left")
                                .font(.system(size: 8))
                            Image(systemName: "arrow.right")
                                .font(.system(size: 8))
                        }
                        .foregroundColor(.black)
                    )
                    .position(x: geometry.size.width * sliderPosition, y: geometry.size.height / 2)
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                let newPosition = value.location.x / geometry.size.width
                                sliderPosition = min(1, max(0, newPosition))
                            }
                    )
            }
        }
    }
} 