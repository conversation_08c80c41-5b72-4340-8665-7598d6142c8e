<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_126_1331)">
<circle cx="24" cy="24" r="24" fill="url(#paint0_radial_126_1331)"/>
</g>
<g filter="url(#filter1_ddi_126_1331)">
<path d="M24 11C25.6569 11 27 12.3431 27 14V21H34C35.6569 21 37 22.3431 37 24C37 25.6569 35.6569 27 34 27H27V34C27 35.6569 25.6569 37 24 37C22.3431 37 21 35.6569 21 34V27H14C12.3431 27 11 25.6569 11 24C11 22.3431 12.3431 21 14 21H21V14C21 12.3431 22.3431 11 24 11Z" fill="white"/>
</g>
<g filter="url(#filter2_f_126_1331)">
<path d="M42.1375 16.6812C41.7229 17.046 40.9873 15.5 37.4667 11.5C33.9461 7.5 32.7229 7.04598 33.1375 6.68109C33.552 6.3162 36.5615 7.86564 38.9333 10.5604C41.3051 13.2551 42.552 16.3163 42.1375 16.6812Z" fill="white"/>
</g>
<g filter="url(#filter3_f_126_1331)">
<circle cx="43" cy="20" r="1" fill="white"/>
</g>
<defs>
<filter id="filter0_ii_126_1331" x="-0.4" y="0" width="48.4" height="48.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.897436 0 0 0 0 0.696088 0 0 0 0 0 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_126_1331"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.4" dy="0.4"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.865385 0 0 0 0 0.605769 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_126_1331" result="effect2_innerShadow_126_1331"/>
</filter>
<filter id="filter1_ddi_126_1331" x="4" y="7" width="36" height="36" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.4"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.589743 0 0 0 0 0.457429 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_126_1331"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="1"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.919872 0 0 0 0 0.536592 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_126_1331" result="effect2_dropShadow_126_1331"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_126_1331" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.791666 0 0 0 0 0.688341 0 0 0 0 0.33113 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_126_1331"/>
</filter>
<filter id="filter2_f_126_1331" x="31.6601" y="5.22842" width="11.956" height="12.9045" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.7" result="effect1_foregroundBlur_126_1331"/>
</filter>
<filter id="filter3_f_126_1331" x="40" y="17" width="6" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_126_1331"/>
</filter>
<radialGradient id="paint0_radial_126_1331" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(21.5 24) rotate(24.6236) scale(26.4008)">
<stop offset="0.237925" stop-color="#F49919"/>
<stop offset="0.408654" stop-color="#F2BC00"/>
<stop offset="0.639423" stop-color="#FFDC61"/>
<stop offset="0.75" stop-color="#FFECAA"/>
<stop offset="0.830521" stop-color="#FFF0BC"/>
<stop offset="1" stop-color="#FFE27D"/>
</radialGradient>
</defs>
</svg>
