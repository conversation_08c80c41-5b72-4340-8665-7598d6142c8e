import SwiftUI

struct LoginView: View {
    @EnvironmentObject var userState: UserState
    @Environment(\.dismiss) var dismiss
    
    @State var phone: String
    @State private var verificationCode: String = ""
    @State private var isLoading = false
    @State private var errorMessage: String? = nil
    @State private var isSendingCode = false
    @State private var navigateToRegister = false
    @State private var navigateToHome = false
    @State private var countdownSeconds = 0
    
    // 添加FocusState来管理输入框焦点
    @FocusState private var phoneFieldFocused: Bool
    @FocusState private var verificationFieldFocused: Bool
    
    private let countdownDuration = 60
    
    var body: some View {
            ZStack {
                // 背景色
                Color.white.ignoresSafeArea()
                    .onTapGesture {
                        // 点击背景时关闭键盘
                        hideKeyboard()
                    }
                
                ScrollView {
                    VStack(spacing: 20) {
                        Spacer(minLength: 50)
                        
                        ZStack {
                            // 装饰元素 - 渐变背景
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(width: 272, height: 272)
                                .background(
                                    EllipticalGradient(
                                        stops: [
                                            Gradient.Stop(color: Color(red: 0.98, green: 0.85, blue: 0.37).opacity(0.65), location: 0.40),
                                            Gradient.Stop(color: Color(red: 0.18, green: 0.8, blue: 0.62).opacity(0.2), location: 0.70),
                                        ],
                                        center: UnitPoint(x: 0.5, y: 0.5)
                                    )
                                )
                                .cornerRadius(272)
                                .blur(radius: 22.5)
                                .position(x: UIScreen.main.bounds.width / 2, y: 50)
                                .onTapGesture {
                                    hideKeyboard()
                                }
                            
                            // 图3-1 - 与渐变背景居中对齐
                            Image("3-1")
                                .resizable()
                                .scaledToFit()
                                .frame(width: 120, height: 120)
                                .position(x: UIScreen.main.bounds.width / 2, y: 50)
                                .onTapGesture {
                                    hideKeyboard()
                                }
                        }
                        .frame(height: 150)
                        
                        Text("NIE NIE")
                            .font(Font.custom("PingFang SC", size: 32).weight(.bold))
                            .lineSpacing(20)
                            .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                            .padding(.bottom, 20)
                            .onTapGesture {
                                hideKeyboard()
                            }
                        
                        // 错误信息
                        if let errorMessage = errorMessage {
                            Text(errorMessage)
                                .font(Font.custom("PingFang SC", size: 14))
                                .foregroundColor(.red)
                                .padding(.vertical, 5)
                                .onTapGesture {
                                    hideKeyboard()
                                }
                        }
                        
                        // 手机号输入框
                        ZStack {
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(width: 232, height: 34)
                                .cornerRadius(25.58)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 25.58)
                                        .inset(by: 0.50)
                                        .stroke(Color(red: 1, green: 0.86, blue: 0.38), lineWidth: 0.50)
                                )
                                .onTapGesture {
                                    // 点击整个区域时聚焦到输入框
                                    phoneFieldFocused = true
                                }
                            
                            // 手机号输入
                            TextField("", text: $phone)
                                .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                                .keyboardType(.numberPad)
                                .padding(.leading, 15)
                                .frame(width: 232, height: 34)
                                .focused($phoneFieldFocused) // 绑定焦点状态
                            
                            // 占位文本
                            if phone.isEmpty {
                                HStack {
                                    Text("输入手机号")
                                        .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                                        .foregroundColor(Color(red: 0.89, green: 0.91, blue: 0.93))
                                        .padding(.leading, 15)
                                    Spacer()
                                }
                                .frame(width: 232, height: 34)
                                .contentShape(Rectangle()) // 确保整个区域可点击
                                .onTapGesture {
                                    // 点击提示文字时聚焦到输入框
                                    phoneFieldFocused = true
                                }
                            }
                        }
                        .padding(.top, 10)
                        
                        // 验证码输入和发送按钮
                        ZStack {
                            // 验证码输入框
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(width: 232, height: 34)
                                .cornerRadius(25.58)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 25.58)
                                        .inset(by: 0.50)
                                        .stroke(Color(red: 1, green: 0.86, blue: 0.38), lineWidth: 0.50)
                                )
                                .onTapGesture {
                                    // 点击整个区域时聚焦到验证码输入框
                                    verificationFieldFocused = true
                                }
                            
                            // 验证码输入
                            TextField("", text: $verificationCode)
                                .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                                .keyboardType(.numberPad)
                                .padding(.leading, 15)
                                .frame(width: 132, height: 34)
                                .offset(x: -50, y: 0)
                                .focused($verificationFieldFocused) // 绑定焦点状态
                            
                            // 占位文本
                            if verificationCode.isEmpty {
                                HStack {
                                    Text("输入验证码")
                                        .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                                        .foregroundColor(Color(red: 0.89, green: 0.91, blue: 0.93))
                                        .padding(.leading, 15)
                                        .frame(width: 132, alignment: .leading)
                                    Spacer()
                                }
                                .frame(width: 132, height: 34)
                                .offset(x: -50, y: 0)
                                .contentShape(Rectangle()) // 确保整个区域可点击
                                .onTapGesture {
                                    // 点击提示文字时聚焦到验证码输入框
                                    verificationFieldFocused = true
                                }
                            }
                            
                            // 获取验证码按钮
                            Button(action: {
                                sendVerificationCode()
                            }) {
                                ZStack {
                                    Rectangle()
                                        .foregroundColor(.clear)
                                        .frame(width: 99.40, height: 34)
                                        .background(Color(red: 1, green: 0.86, blue: 0.38))
                                        .cornerRadius(25.58)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 25.58)
                                                .inset(by: 0.50)
                                                .stroke(Color(red: 0.89, green: 0.91, blue: 0.93), lineWidth: 0.50)
                                        )
                                    
                                    // 按钮文字
                                    Text(countdownSeconds > 0 ? "\(countdownSeconds)秒" : "获取验证码")
                                        .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                                        .lineSpacing(20)
                                        .foregroundColor(.white)
                                }
                            }
                            .disabled(isSendingCode || countdownSeconds > 0 || phone.isEmpty || phone.count != 11)
                            .opacity((isSendingCode || countdownSeconds > 0 || phone.isEmpty || phone.count != 11) ? 0.9 : 1.0)
                            .offset(x: 67, y: 0)
                        }
                        .padding(.top, 20)
                        
                        // 登录按钮
                        Button(action: {
                            login()
                        }) {
                            Text("登录")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(width: 232, height: 35)
                                .background(
                                    RoundedRectangle(cornerRadius: 25.58)
                                        .fill(LinearGradient(
                                            stops: [
                                                Gradient.Stop(color: Color(red: 0, green: 0.72, blue: 0.75), location: 0.00),
                                                Gradient.Stop(color: Color(red: 1, green: 0.78, blue: 0), location: 0.87),
                                            ],
                                            startPoint: UnitPoint(x: -0.41, y: -5.81),
                                            endPoint: UnitPoint(x: 0.57, y: 3.66)
                                        ))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 25.58)
                                                .stroke(Color(red: 0.89, green: 0.91, blue: 0.93), lineWidth: 1)
                                        )
                                )
                        }
                        .disabled(isLoading || verificationCode.isEmpty || phone.isEmpty || phone.count != 11)
                        .opacity((isLoading || verificationCode.isEmpty || phone.isEmpty || phone.count != 11) ? 0.9 : 1.0)
                        .padding(.top, 30)
                        
                        // 加载指示器
                        if isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle())
                                .scaleEffect(1.5)
                                .padding(.top, 20)
                        }
                        
                        Spacer(minLength: 50)
                    }
                    .frame(minWidth: UIScreen.main.bounds.width, minHeight: UIScreen.main.bounds.height - 100)
                }
            }
            .navigationDestination(isPresented: $navigateToRegister) {
                RegisterView(phone: phone)
                    .toolbar(.hidden, for: .navigationBar)
            }
            .navigationDestination(isPresented: $navigateToHome) {
                ContentView()
                    .toolbar(.hidden, for: .navigationBar)
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar(.hidden, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.black)
                            .imageScale(.large)
                    }
                }
            }
            .onAppear {
                // 不自动发送验证码，让用户手动点击发送
                // 如果需要自动发送，可以添加一个标志来控制
            }
    }
    
    // 发送验证码
    private func sendVerificationCode() {
        guard phone.count == 11 else {
            errorMessage = "请输入正确的手机号"
            return
        }
        
        errorMessage = nil
        isSendingCode = true
        
        APIService.shared.sendVerificationCode(phone: phone) { result in
            DispatchQueue.main.async {
                isSendingCode = false
                
                switch result {
                case .success:
                    // 开始倒计时
                    self.startCountdown()
                case .failure(let error):
                    self.errorMessage = "发送验证码失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    // 隐藏键盘
    private func hideKeyboard() {
        phoneFieldFocused = false
        verificationFieldFocused = false
        //UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    // 启动倒计时
    private func startCountdown() {
        countdownSeconds = countdownDuration
        
        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
            DispatchQueue.main.async {
                if countdownSeconds > 0 {
                    countdownSeconds -= 1
                } else {
                    timer.invalidate()
                }
            }
        }
    }
    
    // 登录
    private func login() {
        guard phone.count == 11 else {
            errorMessage = "请输入正确的手机号"
            return
        }
        
        guard !verificationCode.isEmpty else {
            errorMessage = "请输入验证码"
            return
        }
        
        errorMessage = nil
        isLoading = true
        
        // 先验证验证码
        APIService.shared.verifyCode(phone: phone, code: verificationCode) { result in
            switch result {
            case .success(let success):
                if success {
                    // 验证码验证成功，尝试登录
                    APIService.shared.login(phone: phone) { loginResult in
                        DispatchQueue.main.async {
                            isLoading = false
                            
                            switch loginResult {
                            case .success(let userData):
                                // 登录成功，保存用户信息
                                userState.login(
                                    userId: userData.userId,
                                    username: userData.username,
                                    avatar: userData.avatar,
                                    token: userData.token,
                                    balance: userData.balance
                                )
                                
                                // 跳转到首页
                                navigateToHome = true
                                
                            case .failure:
                                // 用户不存在，跳转到注册页面
                                navigateToRegister = true
                            }
                        }
                    }
                } else {
                    DispatchQueue.main.async {
                        isLoading = false
                        errorMessage = "验证码错误，请重新输入"
                    }
                }
                
            case .failure(let error):
                DispatchQueue.main.async {
                    isLoading = false
                    errorMessage = error.localizedDescription
                }
            }
        }
    }
}

struct LoginView_Previews: PreviewProvider {
    static var previews: some View {
        LoginView(phone: "")
            .environmentObject(UserState())
    }
}
