import SwiftUI

struct FunctionScrollView: View {
    @Binding var isDragging: Bo<PERSON>
    @ObservedObject var userState: UserState
    @Binding var showStickerView: Bo<PERSON>
    @Binding var showDepthLightingView: Bool
    @Binding var showSuperResolutionView: Bool
    @Binding var showStyleTransferView: Bool
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                // 创作功能
                CreationButton(isDragging: $isDragging, userState: userState)

                // 其他功能按钮
                FunctionButton(
                    title: "贴纸",
                    icon: "face.smiling.fill",
                    colors: [Color.purple, Color.pink],
                    isDragging: $isDragging
                ) {
                    showStickerView = true
                }

                FunctionButton(
                    title: "补光",
                    icon: "lightbulb.fill",
                    colors: [Color.blue, Color.cyan],
                    isDragging: $isDragging
                ) {
                    showDepthLightingView = true
                }

                FunctionButton(
                    title: "超分",
                    icon: "arrow.up.right.square.fill",
                    colors: [Color.orange, Color.yellow],
                    isDragging: $isDragging
                ) {
                    showSuperResolutionView = true
                }

                FunctionButton(
                    title: "风格",
                    icon: "paintbrush.fill",
                    colors: [Color.green, Color.mint],
                    isDragging: $isDragging
                ) {
                    showStyleTransferView = true
                }

                // 测试按钮
                ForEach(1...10, id: \.self) { index in
                    FunctionButton(
                        title: "测试\(index)",
                        icon: "star.fill",
                        colors: [Color.red, Color.orange],
                        isDragging: $isDragging
                    ) {
                        // 测试按钮暂无功能
                    }
                }
            }
            .padding(.horizontal, 15)
        }
        .padding(.top, 12)
        .padding(.bottom, 8)
        .background(Color.gray.opacity(0.1))
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
        )
        .padding(.horizontal, 10)
    }
}

// 创作按钮组件
struct CreationButton: View {
    @Binding var isDragging: Bool
    @ObservedObject var userState: UserState

    var body: some View {
        Button(action: {
            if !isDragging {
                if userState.hasActiveCreationTask() {
                    return
                }

                if userState.balance >= 2 {
                    userState.showAICreateSheet = true
                } else {
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let rootVC = windowScene.windows.first?.rootViewController {
                        let alert = UIAlertController(title: "余额不足", message: "进行AI创作需要至少2个捏币", preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "确定", style: .default))
                        rootVC.present(alert, animated: true)
                    }
                }
            }
        }) {
            HStack(spacing: 4) {
                Image(systemName: "plus.circle.fill")
                    .font(.system(size: 14))
                Text("创作")
                    .font(Font.custom("PingFang SC", size: 14).weight(.medium))
            }
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                LinearGradient(
                    colors: [Color.blue, Color.purple],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(15)
            .shadow(color: Color.blue.opacity(0.3), radius: 3, x: 0, y: 2)
        }
        .simultaneousGesture(
            DragGesture()
                .onChanged { value in
                    let distance = sqrt(pow(value.translation.width, 2) + pow(value.translation.height, 2))
                    if distance > 5 {
                        isDragging = true
                    }
                }
                .onEnded { _ in
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        isDragging = false
                    }
                }
        )
    }
}

// 通用功能按钮组件
struct FunctionButton: View {
    let title: String
    let icon: String
    let colors: [Color]
    @Binding var isDragging: Bool
    let action: () -> Void

    var body: some View {
        Button(action: {
            if !isDragging {
                action()
            }
        }) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 14))
                Text(title)
                    .font(Font.custom("PingFang SC", size: 14).weight(.medium))
            }
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                LinearGradient(
                    colors: colors,
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(15)
            .shadow(color: colors.first?.opacity(0.3) ?? Color.clear, radius: 3, x: 0, y: 2)
        }
        .simultaneousGesture(
            DragGesture()
                .onChanged { value in
                    let distance = sqrt(pow(value.translation.width, 2) + pow(value.translation.height, 2))
                    if distance > 5 {
                        isDragging = true
                    }
                }
                .onEnded { _ in
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        isDragging = false
                    }
                }
        )
    }
}
