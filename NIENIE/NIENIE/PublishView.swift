import SwiftUI

// 发布作品视图
struct PublishView: View {
    let image: UIImage
    let taskId: String
    @State private var title: String = ""
    @State private var content: String = ""
    @State private var selectedCategory: Int = -1 // -1 表示未选择
    @State private var isPublishing = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    let isImageEnhanced: Bool // 接收状态
    var onBack: () -> Void
    
    // 类别选项
    private let categories = [
        (id: 0, name: "风景", icon: "mountain.2", colors: [Color.green, Color.mint]),
        (id: 1, name: "动漫", icon: "person.fill", colors: [Color.pink, Color.red]),
        (id: 2, name: "古风", icon: "building.columns", colors: [Color.orange, Color.yellow]),
        (id: 3, name: "二次元", icon: "star.fill", colors: [Color.purple, Color.indigo]),
        (id: 4, name: "其他", icon: "ellipsis.circle", colors: [Color.gray, Color.secondary])
    ]
    
    init(image: UIImage, taskId: String, isImageEnhanced: Bool = false, onBack: @escaping () -> Void = {}) {
        self.image = image
        self.taskId = taskId
        self.isImageEnhanced = isImageEnhanced
        self.onBack = onBack
    }
    
    var body: some View {
        ZStack {
            // 背景颜色
            Color.white.ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 标题栏
                HStack {
                    // 返回按钮
                    Button(action: {
                        onBack()
                    }) {
                        Image(systemName: "arrow.left")
                            .font(.system(size: 18))
                            .foregroundColor(.black)
                    }
                    .padding(.leading, 16)
                    
                    Spacer()
                    
                    // 标题
                    Text("发布作品")
                        .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                        .foregroundColor(.black)
                    
                    Spacer()
                    
                    // 占位，保持标题居中
                    Color.clear
                        .frame(width: 20, height: 20)
                        .padding(.trailing, 16)
                }
                .padding(.top, 16)
                .padding(.bottom, 20)
                
                // 内容滚动视图
                ScrollView {
                    VStack(spacing: 15) {
                        // 图片预览
                        Image(uiImage: image)
                            .resizable()
                            .scaledToFit()
                            .frame(maxHeight: 300)
                            .cornerRadius(8)
                            .padding(.horizontal, 20)
                        
                        // 类别选择
                        VStack(alignment: .leading, spacing: 10) {
                            HStack {
                                Text("选择类别")
                                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                                    .foregroundColor(.black)
                                
                                Text("*")
                                    .font(Font.custom("PingFang SC", size: 16))
                                    .foregroundColor(.red)
                                
                                Spacer()
                            }
                            .padding(.horizontal, 20)
                            
                            // 类别选择网格
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 10), count: 3), spacing: 10) {
                                ForEach(categories, id: \.id) { category in
                                    Button(action: {
                                        selectedCategory = category.id
                                    }) {
                                        VStack(spacing: 8) {
                                            ZStack {
                                                LinearGradient(
                                                    gradient: Gradient(colors: selectedCategory == category.id ? category.colors : [Color.gray.opacity(0.3)]),
                                                    startPoint: .topLeading,
                                                    endPoint: .bottomTrailing
                                                )
                                                .frame(width: 40, height: 40)
                                                .clipShape(Circle())
                                                
                                                Image(systemName: category.icon)
                                                    .font(.system(size: 18))
                                                    .foregroundColor(.white)
                                            }
                                            
                                            Text(category.name)
                                                .font(Font.custom("PingFang SC", size: 12))
                                                .foregroundColor(selectedCategory == category.id ? .black : .gray)
                                        }
                                    }
                                    .scaleEffect(selectedCategory == category.id ? 1.1 : 1.0)
                                    .animation(.easeInOut(duration: 0.2), value: selectedCategory)
                                }
                            }
                            .padding(.horizontal, 20)
                        }
                        
                        // 标题输入
                        VStack(alignment: .leading, spacing: 5) {
                            ZStack(alignment: .bottomTrailing) {
                                TextField("添加标题", text: $title)
                                    .padding(12)
                                    .onChange(of: title) { oldValue, newValue in
                                        if newValue.count > 20 {
                                            title = String(newValue.prefix(20))
                                        }
                                    }
                                
                                // 字数统计
                                Text("\(title.count)/20字")
                                    .font(Font.custom("PingFang SC", size: 12))
                                    .foregroundColor(.gray)
                                    .padding(.trailing, 8)
                                    .padding(.bottom, 8)
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 10)
                        
                        // 内容输入
                        VStack(alignment: .leading, spacing: 5) {
                            ZStack(alignment: .bottomTrailing) {
                                TextEditor(text: $content)
                                    .padding(5)
                                    .frame(minHeight: 150)
                                    .overlay(
                                        Group {
                                            if content.isEmpty {
                                                Text("添加作品描述")
                                                    .foregroundColor(Color.primary.opacity(0.25))
                                                    .padding(5)
                                                    .padding(.top, 8)
                                                    .padding(.leading, 4)
                                                    .allowsHitTesting(false)
                                                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                                            }
                                        }
                                    )
                                    .onChange(of: content) { oldValue, newValue in
                                        if newValue.count > 500 {
                                            content = String(newValue.prefix(500))
                                        }
                                    }
                                
                                // 字数统计
                                Text("\(content.count)/500字")
                                    .font(Font.custom("PingFang SC", size: 12))
                                    .foregroundColor(.gray)
                                    .padding(.trailing, 8)
                                    .padding(.bottom, 8)
                            }
                        }
                        .padding(.horizontal, 20)
                    }
                    .padding(.bottom, 20)
                }
                
                // 底部按钮区域
                HStack {
                    Spacer()
                    
                    // 发布按钮 - 右侧
                    Button(action: {
                        publishImage()
                    }) {
                        HStack(spacing: 5) {
                            Image("w-1")
                                .resizable()
                                .frame(width: 20, height: 20)
                            Text("发布")
                                .font(Font.custom("PingFang SC", size: 15))
                        }
                        .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                        .frame(width: 91, height: 33)
                        .background(
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(width: 91, height: 33)
                                .background(selectedCategory == -1 ? Color.gray.opacity(0.3) : .white)
                                .cornerRadius(30)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 30)
                                        .inset(by: 0.25)
                                        .stroke(selectedCategory == -1 ? Color.gray : Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 0.5)
                                )
                        )
                    }
                    .disabled(isPublishing || selectedCategory == -1)
                    .opacity((isPublishing || selectedCategory == -1) ? 0.5 : 1.0)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 20)
                .background(Color.white)
            }
            
            // 加载中遮罩
            if isPublishing {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .overlay(
                        VStack {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.5)
                            
                            Text("发布中...")
                                .font(Font.custom("PingFang SC", size: 16))
                                .foregroundColor(.white)
                                .padding(.top, 10)
                        }
                    )
            }
        }
        .navigationBarHidden(true)
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
    }
    
    // 发布图片
    private func publishImage() {
        // 检查是否选择了类别
        guard selectedCategory != -1 else {
            alertMessage = "请选择作品类别"
            showAlert = true
            return
        }

        // 获取标题和内容，允许为空
        let trimmedTitle = title.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)

        // 开始发布
        isPublishing = true

        // 调用发布逻辑，传递类别ID
        proceedToPublish(trimmedTitle: trimmedTitle, trimmedContent: trimmedContent, categoryId: selectedCategory)
    }

    // 封装发布逻辑，以便在更新图片后调用
    private func proceedToPublish(trimmedTitle: String, trimmedContent: String, categoryId: Int) {
        APIService.shared.saveImageToDatabase(
            userId: UserState.shared.userId,
            taskId: taskId,
            saveToPublic: true,
            title: trimmedTitle,
            content: trimmedContent,
            categoryId: categoryId,
            enhancedImage: isImageEnhanced ? image : nil
        ) { result in
            DispatchQueue.main.async {
                isPublishing = false

                switch result {
                case .success(let info):
                    let (_, reward) = info

                    // 重置悬浮球状态
                    UserState.shared.resetCreationState()

                    // 将图像保存到创作记录中
                    // 检查是否已经存在该图像的记录
                    let exists = UserState.shared.checkIfImageExists(image: image)
                    if !exists {
                        if UserState.shared.saveCreationImage(image: image) != nil {
                        }
                    }

                    // 发送通知刷新用户作品列表
                    NotificationCenter.default.post(name: NSNotification.Name("RefreshUserImages"), object: nil)
                    UserState.shared.balance += 1

                    // 显示成功消息
                    alertMessage = "发布成功，获得\(reward)捏币奖励！"
                    showAlert = true

                    // 延迟导航到社区页面
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        // 直接调用导航方法，不再保存图像（因为已经在上面保存过了）
                        // 使用NotificationCenter发送通知，在TabView中切换到社区标签
                        NotificationCenter.default.post(name: Notification.Name("SwitchToHomeTab"), object: nil)

                        // 关闭所有模态视图，返回到主界面
                        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                           let rootViewController = windowScene.windows.first?.rootViewController {
                            rootViewController.dismiss(animated: true, completion: nil)
                        }
                    }

                case .failure(let error):
                    // 显示错误消息
                    alertMessage = "发布失败: \(error.localizedDescription)"
                    showAlert = true
                }
            }
        }
    }
}
