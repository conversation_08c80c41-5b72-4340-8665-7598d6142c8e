import SwiftUI
import UIKit

// 模板画廊视图
struct TemplateGalleryView: View {
    @Environment(\.presentationMode) var presentationMode
    var onSelectTemplate: (UIImage) -> Void
    @State private var templates: [TemplateData] = []
    @State private var errorMessage: String? = nil
    @State private var isLoading = true
    @State private var currentPage = 1
    @State private var hasMoreTemplates = true
    @State private var templateImages: [String: UIImage] = [:]
    @State private var showError = false
    
    private let pageSize = 20
    // 修改网格布局，使用固定大小的网格项
    private let columns = [
        GridItem(.adaptive(minimum: 150, maximum: 150), spacing: 15),
        GridItem(.adaptive(minimum: 150, maximum: 150), spacing: 15)
    ]
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                // 返回按钮
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "arrow.left")
                        .font(.system(size: 18))
                        .foregroundColor(.black)
                }
                .padding(.leading, 16)
                
                Spacer()
                
                // 标题
                Text("背景模版")
                    .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                    .foregroundColor(.black)
                
                Spacer()
                
                // 占位，保持标题居中
                Color.clear
                    .frame(width: 20, height: 20)
                    .padding(.trailing, 16)
            }
            .padding(.top, 16)
            .padding(.bottom, 20)
            
            if let error = errorMessage {
                VStack {
                    Spacer()
                    Text(error)
                        .foregroundColor(.red)
                        .padding()
                    Spacer()
                }
            } else if templates.isEmpty && isLoading {
                VStack {
                    Spacer()
                    ProgressView("加载中...")
                    Spacer()
                }
            } else {
                // 模板网格
                ScrollView {
                    LazyVGrid(columns: columns, spacing: 15) {
                        ForEach(templates) { template in
                            TemplateImageCell(
                                template: template,
                                image: templateImages[template.filename],
                                onSelect: { selectTemplate(template) }
                            )
                            .frame(width: 150, height: 150) // 确保每个单元格大小一致
                        }
                    }
                    .padding(.horizontal, 15)
                    .padding(.vertical, 10)
                    
                    // 加载更多或显示结束信息
                    if !templates.isEmpty {
                        if hasMoreTemplates {
                            Button(action: {
                                loadMoreTemplates()
                            }) {
                                if isLoading {
                                    ProgressView()
                                        .frame(height: 50)
                                } else {
                                    Text("加载更多")
                                        .foregroundColor(.blue)
                                        .frame(height: 50)
                                }
                            }
                            .onAppear {
                                if !isLoading && hasMoreTemplates {
                                    loadMoreTemplates()
                                }
                            }
                        } else {
                            Text("已经到底了")
                                .foregroundColor(.gray)
                                .frame(height: 50)
                        }
                    }
                    
                    Spacer().frame(height: 20)
                }
            }
        }
        .background(Color.white.ignoresSafeArea())
        .navigationBarHidden(true)
        .onAppear {
            loadTemplates()
        }
        .alert(isPresented: $showError) {
            Alert(
                title: Text("错误"),
                message: Text(errorMessage ?? "加载失败"),
                dismissButton: .default(Text("确定"))
            )
        }
    }
    
    // 加载模板图片
    private func loadTemplates() {
        isLoading = true
        
        APIService.shared.getTemplateImages(page: 1, limit: pageSize) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let templates):
                    self.templates = templates
                    self.currentPage = 1
                    self.hasMoreTemplates = templates.count == self.pageSize
                    self.isLoading = false
                    
                    // 预加载图片
                    for template in templates {
                        loadTemplateImage(template)
                    }
                    
                case .failure(let error):
                    self.errorMessage = "加载模板失败: \(error.localizedDescription)"
                    self.isLoading = false
                    self.showError = true
                }
            }
        }
    }
    
    // 加载更多模板
    private func loadMoreTemplates() {
        guard !isLoading && hasMoreTemplates else { return }
        
        isLoading = true
        let nextPage = currentPage + 1
        
        APIService.shared.getTemplateImages(page: nextPage, limit: pageSize) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let newTemplates):
                    self.templates.append(contentsOf: newTemplates)
                    self.currentPage = nextPage
                    self.hasMoreTemplates = newTemplates.count == self.pageSize
                    self.isLoading = false
                    
                    // 预加载图片
                    for template in newTemplates {
                        loadTemplateImage(template)
                    }
                    
                case .failure(let error):
                    self.errorMessage = "加载更多模板失败: \(error.localizedDescription)"
                    self.isLoading = false
                    self.showError = true
                }
            }
        }
    }
    
    // 加载单个模板图片
    private func loadTemplateImage(_ template: TemplateData) {
        APIService.shared.getTemplateImageData(from: template.lowResURL) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let image):
                    self.templateImages[template.filename] = image
                case .failure(_):
                    // 加载失败时不显示错误，只是不显示图片
                    break
                }
            }
        }
    }

    // 选择模板并加载高分辨率版本
    private func selectTemplate(_ template: TemplateData) {
        // 显示加载中提示
        let loadingAlert = UIAlertController(title: "加载中", message: "正在加载高分辨率模板...", preferredStyle: .alert)

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootVC = windowScene.windows.first?.rootViewController {
            var currentVC = rootVC
            while let presented = currentVC.presentedViewController {
                currentVC = presented
            }
            currentVC.present(loadingAlert, animated: true)
        }

        // 直接加载高分辨率图片
        APIService.shared.getTemplateImageData(from: template.highResURL) { result in
            DispatchQueue.main.async {
                // 关闭加载提示
                loadingAlert.dismiss(animated: true) {
                    switch result {
                    case .success(let highResImage):
                        // 使用高分辨率图片
                        self.onSelectTemplate(highResImage)
                        self.presentationMode.wrappedValue.dismiss()

                    case .failure(_):
                        // 高分辨率加载失败，尝试使用低分辨率图片
                        if let lowResImage = self.templateImages[template.filename] {
                            // 使用已缓存的低分辨率图片
                            self.onSelectTemplate(lowResImage)
                            self.presentationMode.wrappedValue.dismiss()
                        } else {
                            // 尝试加载低分辨率图片
                            APIService.shared.getTemplateImageData(from: template.lowResURL) { lowResResult in
                                DispatchQueue.main.async {
                                    switch lowResResult {
                                    case .success(let lowResImage):
                                        self.onSelectTemplate(lowResImage)
                                        self.presentationMode.wrappedValue.dismiss()
                                    case .failure(let error):
                                        // 显示错误提示
                                        let errorAlert = UIAlertController(
                                            title: "加载失败",
                                            message: "无法加载模板图片: \(error.localizedDescription)",
                                            preferredStyle: .alert
                                        )
                                        errorAlert.addAction(UIAlertAction(title: "确定", style: .default))

                                        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                           let rootVC = windowScene.windows.first?.rootViewController {
                                            var currentVC = rootVC
                                            while let presented = currentVC.presentedViewController {
                                                currentVC = presented
                                            }
                                            currentVC.present(errorAlert, animated: true)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 模板图片单元格
struct TemplateImageCell: View {
    let template: TemplateData
    let image: UIImage?
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            ZStack {
                // 背景容器，确保统一尺寸
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .aspectRatio(1, contentMode: .fit)
                    .frame(height: 150)

                // 图片内容
                if let image = image {
                    Image(uiImage: image)
                        .resizable()
                        .scaledToFill()
                        .frame(width: 150, height: 150)
                        .clipped()
                } else {
                    // 加载中状态
                    ProgressView()
                }
            }
            .frame(height: 150)
            .cornerRadius(10)
        }
    }
}
