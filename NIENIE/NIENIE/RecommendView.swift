import SwiftUI

struct RecommendView: View {
    @EnvironmentObject var userState: UserState
    @State private var selectedContentTab: Int = 0
    @State private var images: [ImageData] = []
    @State private var isLoading = false
    @State private var isLoadingMore = false
    @State private var errorMessage: String? = nil
    @State private var currentPage = 1
    @State private var hasMoreImages = true
    @State private var lastLoadMoreTime = Date(timeIntervalSince1970: 0)
    @State private var scrollToTop = false
    @State private var isRefreshing = false
    @State private var tempImages: [ImageData] = []
    @State private var showGlobalFeedbackMenu = false
    @State private var feedbackImage: ImageData?
    @State private var imageToRemove: Int?
    
    var body: some View {
        VStack(spacing: 0) {
            // 排行和最新选择区域
            HStack(spacing: 0) {
                Button(action: {
                    if selectedContentTab != 0 {
                        withAnimation {
                            selectedContentTab = 0
                            images = []  // 立即清空图片列表
                            currentPage = 1
                            hasMoreImages = true
                            lastLoadMoreTime = Date(timeIntervalSince1970: 0) // 重置加载时间
                            scrollToTop = true // 标记需要滚动到顶部
                            loadHomeImages(page: 1, resetImages: true, sortBy: "rank")
                        }
                    } else {
                        // 点击当前选中的"排行"标签时，刷新内容
                        scrollToTop = true // 标记需要滚动到顶部
                        refreshContent(sortBy: "rank")
                    }
                }) {
                    VStack(spacing: 8) {
                        if isRefreshing && selectedContentTab == 0 {
                            // 刷新动画
                            RefreshAnimationView()
                                .frame(height: 20)
                        } else {
                            Text("排行")
                                .font(Font.custom("PingFang SC", size: 16).weight(.bold))
                                .foregroundColor(selectedContentTab == 0 ? .black : .gray)
                        }
                        
                        if selectedContentTab == 0 {
                            Capsule()
                                .frame(height: 3)
                                .foregroundColor(.blue)
                                .transition(.opacity)
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(PlainButtonStyle())
                
                Button(action: {
                    if selectedContentTab != 1 {
                        withAnimation {
                            selectedContentTab = 1
                            images = []  // 立即清空图片列表
                            currentPage = 1
                            hasMoreImages = true
                            lastLoadMoreTime = Date(timeIntervalSince1970: 0) // 重置加载时间
                            scrollToTop = true // 标记需要滚动到顶部
                            loadHomeImages(page: 1, resetImages: true, sortBy: "time")
                        }
                    } else {
                        // 点击当前选中的"最新"标签时，刷新内容
                        scrollToTop = true // 标记需要滚动到顶部
                        refreshContent(sortBy: "time")
                    }
                }) {
                    VStack(spacing: 8) {
                        if isRefreshing && selectedContentTab == 1 {
                            // 刷新动画
                            RefreshAnimationView()
                                .frame(height: 20)
                        } else {
                            Text("最新")
                                .font(Font.custom("PingFang SC", size: 16).weight(.bold))
                                .foregroundColor(selectedContentTab == 1 ? .black : .gray)
                        }
                        
                        if selectedContentTab == 1 {
                            Capsule()
                                .frame(height: 3)
                                .foregroundColor(.blue)
                                .transition(.opacity)
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            .padding(.bottom, 15)
            .background(Color.white)
            
            // 图片网格
            GeometryReader { geometry in
                ScrollViewReader { scrollViewProxy in
                    ScrollView {
                        // 添加一个顶部锚点视图
                        VStack {
                            Rectangle()
                                .frame(width: 0, height: 0)
                                .id("scrollToTop") // 添加顶部锚点ID

                            HStack(alignment: .top, spacing: 15) {
                                // 第一列 - 高度模式: 1.3, 1, 1.3, 1...
                                VStack(spacing: 15) {
                                    ForEach(0..<(images.count + 1) / 2, id: \.self) { index in
                                        let imageIndex = index * 2
                                        if imageIndex < images.count {
                                            let heightMultiplier = index % 2 == 0 ? 1.3 : 1.0
                                            let image = images[imageIndex]
                                            ImageCard(
                                                image: image,
                                                geometry: geometry,
                                                heightMultiplier: heightMultiplier,
                                                onShowFeedback: { image in
                                                    // 显示全局反馈菜单
                                                    feedbackImage = image
                                                    showGlobalFeedbackMenu = true
                                                    imageToRemove = imageIndex
                                                }
                                            )
                                            .environmentObject(userState)
                                            .id("\(image.imageId)-\(selectedContentTab)") // 移除liked和likeCount，避免点赞操作触发重建
                                            .onAppear {
                                                // 当显示倒数第5个图片时，加载更多
                                                if imageIndex == images.count - 5 {
                                                    loadMoreHomeImagesWithDebounce()
                                                }
                                            }
                                        }
                                    }
                                }
                                .frame(width: (geometry.size.width - 30) / 2) // 修改宽度计算，减少左右间距

                                // 第二列 - 高度模式: 1, 1.3, 1, 1.3...
                                VStack(spacing: 15) {
                                    ForEach(0..<images.count / 2, id: \.self) { index in
                                        let imageIndex = index * 2 + 1
                                        if imageIndex < images.count {
                                            let heightMultiplier = index % 2 == 0 ? 1.0 : 1.3
                                            let image = images[imageIndex]
                                            ImageCard(
                                                image: image,
                                                geometry: geometry,
                                                heightMultiplier: heightMultiplier,
                                                onShowFeedback: { image in
                                                    // 显示全局反馈菜单
                                                    feedbackImage = image
                                                    showGlobalFeedbackMenu = true
                                                    imageToRemove = imageIndex
                                                }
                                            )
                                            .environmentObject(userState)
                                            .id("\(image.imageId)-\(selectedContentTab)") // 移除liked和likeCount，避免点赞操作触发重建
                                            .onAppear {
                                                // 当显示倒数第5个图片时，加载更多
                                                if imageIndex == images.count - 5 {
                                                    loadMoreHomeImagesWithDebounce()
                                                }
                                            }
                                        }
                                    }
                                }
                                .frame(width: (geometry.size.width - 30) / 2) // 修改宽度计算，减少左右间距
                            }
                            .padding(.horizontal, 0) // 移除水平内边距，让内容充满整个宽度
                            .padding(.bottom, 70) // 为底部导航栏留出空间
                        }
                        
                        // 加载更多指示器
                        if isLoadingMore {
                            HStack {
                                Spacer()
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                    .scaleEffect(0.8)
                                Text("加载中...")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                Spacer()
                            }
                            .frame(height: 50)
                            .padding()
                        } else if !images.isEmpty {
                            HStack {
                                Spacer()
                                Text("没有更多内容了")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                Spacer()
                            }
                            .frame(height: 50)
                            .padding()
                        }
                    }
                    .onChange(of: scrollToTop) { oldValue, newValue in
                        if newValue {
                            // 当数据加载完成后，滚动到顶部
                            withAnimation {
                                scrollViewProxy.scrollTo("scrollToTop", anchor: .top)
                            }
                            // 重置状态
                            scrollToTop = false
                        }
                    }
                    // 添加下拉刷新功能
                    .refreshable {
                        // 使用 withCheckedContinuation 桥接旧的完成回调模式
                        await withCheckedContinuation { continuation in
                            // 根据当前选中的标签页选择排序方式
                            let sortBy = selectedContentTab == 0 ? "rank" : "time"
                            
                            // 设置刷新状态为true，触发动画
                            isRefreshing = true
                            // 保存当前图片列表，以便在刷新完成前保持显示
                            tempImages = images
                            
                            // 重置加载状态
                            lastLoadMoreTime = Date(timeIntervalSince1970: 0)
                            
                            // 加载第一页数据
                            loadHomeImages(page: 1, resetImages: true, sortBy: sortBy) {
                                // 刷新完成后的回调
                                isRefreshing = false
                                tempImages = []
                                continuation.resume()
                            }
                        }
                    }
                }
                .padding(.horizontal, 0) // 移除外层水平内边距
            }
            
            if isLoading && images.isEmpty {
                VStack {
                    Spacer()
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.2)
                    Text("加载中...")
                        .font(.caption)
                        .foregroundColor(.gray)
                        .padding(.top, 10)
                    Spacer()
                }
            } else if let errorMessage = errorMessage, images.isEmpty {
                VStack {
                    Spacer()
                    Text(errorMessage)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.center)
                        .padding()
                    Button("重试") {
                        loadHomeImages(page: 1, resetImages: true, sortBy: selectedContentTab == 0 ? "rank" : "time")
                    }
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                    Spacer()
                }
            }
        }
        .onAppear {
            if images.isEmpty {
                loadHomeImages(page: 1, resetImages: true, sortBy: "rank")
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ImageLiked"))) { (notification: Notification) in
            if let userInfo = notification.userInfo,
               let imageId = userInfo["imageId"] as? Int,
               let isLiked = userInfo["isLiked"] as? Bool {

                // 更新对应图片的点赞状态
                if let index = images.firstIndex(where: { $0.imageId == imageId }) {
                    images[index].liked = isLiked
                    if isLiked {
                        images[index].likeCount += 1
                    } else {
                        images[index].likeCount = max(0, images[index].likeCount - 1)
                    }
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ImageDeleted"))) { (notification: Notification) in
            if let userInfo = notification.userInfo,
               let imageId = userInfo["imageId"] as? Int {

                // 直接从当前列表中移除图片，无需重新加载
                self.images.removeAll { $0.imageId == imageId }
            }
        }
    }
    
    // 刷新内容的函数
    private func refreshContent(sortBy: String) {
        isRefreshing = true
        tempImages = images
        lastLoadMoreTime = Date(timeIntervalSince1970: 0)
        
        loadHomeImages(page: 1, resetImages: true, sortBy: sortBy) {
            isRefreshing = false
            tempImages = []
        }
    }
    
    // 加载首页图片的函数
    private func loadHomeImages(page: Int, resetImages: Bool = false, sortBy: String = "rank", completion: (() -> Void)? = nil) {
        if resetImages {
            isLoading = true
            errorMessage = nil
        } else {
            isLoadingMore = true
        }
        
        APIService.shared.getHomeImages(page: page, sortBy: sortBy, categoryId: -1) { result in
            DispatchQueue.main.async {
                self.isLoading = false
                self.isLoadingMore = false
                
                switch result {
                case .success(let newImages):
                    if resetImages {
                        self.images = newImages
                        self.currentPage = 1
                        self.hasMoreImages = !newImages.isEmpty
                    } else {
                        self.images.append(contentsOf: newImages)
                        self.currentPage = page
                        self.hasMoreImages = !newImages.isEmpty
                    }
                    
                    completion?()
                    
                case .failure(let error):
                    if resetImages {
                        self.errorMessage = "加载图片失败: \(error.localizedDescription)"
                    }
                    completion?()
                }
            }
        }
    }
    
    // 添加防抖机制的加载更多函数
    private func loadMoreHomeImagesWithDebounce() {
        let now = Date()
        // 如果距离上次加载时间不足2秒，则不重复加载
        if now.timeIntervalSince(lastLoadMoreTime) < 2.0 {
            return
        }
        
        lastLoadMoreTime = now
        loadMoreHomeImages()
    }

    private func loadMoreHomeImages() {
        guard hasMoreImages && !isLoadingMore else {
            return
        }

        // 记录当前页码并加载下一页
        let nextPage = currentPage + 1
        loadHomeImages(page: nextPage, sortBy: selectedContentTab == 0 ? "rank" : "time")
    }
}
