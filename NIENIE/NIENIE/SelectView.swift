import SwiftUI

struct SelectView: View {
    @State private var navigateToLogin = false
    @State private var navigateToVerify = false
    @State private var isAgreed = true
    @State private var showPrivacyPolicy = false
    @State private var showUserAgreement = false
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 1. 渐变背景
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.73, green: 0.91, blue: 0.99),
                        Color(red: 0.97, green: 0.93, blue: 0.77)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                // 2. 河豚图片：放在背景之上，主内容之下
                VStack {
                    Spacer()
                    HStack {
                        Image("2-2") // 使用导入的河豚图片
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: geometry.size.width * 0.8)
                            
                        Spacer()
                    }
                }
                .ignoresSafeArea()
                
                VStack(spacing: geometry.size.height * 0.03) {
                    // 标题
                    Text("NIE NIE")
                        .offset(y: -geometry.size.height * 0.1)
                        .font(Font.custom("PingFang SC", size: min(48, geometry.size.width * 0.12)).weight(.bold))
                        .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                    
                    // 登录按钮 - 上移了一点
                    Button(action: {
                        navigateToLogin = true
                    }) {
                        Text("手机号登录")
                            .font(Font.custom("PingFang SC", size: min(18, geometry.size.width * 0.045)).weight(.medium))
                            .foregroundColor(.white)
                            .padding(.vertical, 12)
                            .frame(width: min(240, geometry.size.width * 0.7))
                            .background(Color(red: 0.27, green: 0.18, blue: 0.13))
                            .cornerRadius(25)
                    }
                    .padding(.top, geometry.size.height * 0.05) // 减小了顶部间距
                    
                    // 注册按钮
                    Button(action: {
                        navigateToVerify = true
                    }) {
                        Text("手机号注册")
                            .font(Font.custom("PingFang SC", size: min(18, geometry.size.width * 0.045)).weight(.medium))
                            .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                            .padding(.vertical, 12)
                            .frame(width: min(240, geometry.size.width * 0.7))
                            .background(Color.white.opacity(0.7))
                            .cornerRadius(25)
                            .overlay(
                                RoundedRectangle(cornerRadius: 25)
                                    .stroke(Color(red: 0.27, green: 0.18, blue: 0.13), lineWidth: 1)
                            )
                    }
                    
                    // 隐私政策和用户协议
                    HStack(alignment: .center, spacing: 5) {
                        Button(action: {
                            isAgreed.toggle()
                        }) {
                            ZStack {
                                Circle()
                                    .stroke(Color(red: 0.27, green: 0.18, blue: 0.13), lineWidth: 1)
                                    .frame(width: 18, height: 18)
                                
                                if isAgreed {
                                    Circle()
                                        .fill(Color(red: 0.27, green: 0.18, blue: 0.13))
                                        .frame(width: 12, height: 12)
                                }
                            }
                        }
                        
                        Text("同意并遵守")
                            .font(Font.custom("PingFang SC", size: 14))
                            .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                        
                        Button(action: {
                            showPrivacyPolicy = true
                        }) {
                            Text("《隐私政策》")
                                .font(Font.custom("PingFang SC", size: 14))
                                .foregroundColor(Color.blue)
                        }
                        
                        Text("及")
                            .font(Font.custom("PingFang SC", size: 14))
                            .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                        
                        Button(action: {
                            showUserAgreement = true
                        }) {
                            Text("《用户服务协议》")
                                .font(Font.custom("PingFang SC", size: 14))
                                .foregroundColor(Color.blue)
                        }
                    }
                    .padding(.top, 10)
                }
            }
            .navigationDestination(isPresented: $navigateToLogin) {
                LoginView(phone: "")
                    .toolbar(.hidden, for: .navigationBar)
            }
            .navigationDestination(isPresented: $navigateToVerify) {
                VerifyView(isForRegistration: true)
                    .toolbar(.hidden, for: .navigationBar)
            }
            .sheet(isPresented: $showPrivacyPolicy) {
                WebViewContainer(urlString: "https://qianmianshixiao.com:5190/privacy")
            }
            .sheet(isPresented: $showUserAgreement) {
                WebViewContainer(urlString: "https://qianmianshixiao.com:5190/service")
            }
        }
    }
}

struct SelectView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            SelectView()
                .previewDevice("iPhone 13 Pro Max")
                .previewDisplayName("iPhone 13 Pro Max")
            
            SelectView()
                .previewDevice("iPhone SE (3rd generation)")
                .previewDisplayName("iPhone SE")
        }
    }
} 