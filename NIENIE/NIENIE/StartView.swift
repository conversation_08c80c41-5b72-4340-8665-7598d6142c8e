import SwiftUI

struct StartView: View {
    @EnvironmentObject var userState: UserState
    @State private var isValidatingToken = true
    @State private var navigateToHome = false
    @State private var navigateToSelect = false
    
    var body: some View {
        NavigationStack {
            GeometryReader { geometry in
            ZStack {
                // 1. 渐变背景
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.73, green: 0.91, blue: 0.99),
                        Color(red: 0.97, green: 0.93, blue: 0.77)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                // 2. 河豚图片：放在背景之上，主内容之下
                VStack {
                    Spacer()
                    HStack {
                        Image("2-2") // 使用导入的河豚图片
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                                .frame(width: geometry.size.width * 0.8)
                            
                        Spacer()
                    }
                }
                .ignoresSafeArea() // 使河豚图片可以紧贴屏幕左下角（包括安全区域）
                
                VStack(spacing: 0) {
                    // 标题
                    Text("NIE NIE")
                            .offset(y: -geometry.size.height * 0.1)
                            .font(Font.custom("PingFang SC", size: min(48, geometry.size.width * 0.12)).weight(.bold))
                        .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                    
                    // 加载指示器
                    if isValidatingToken {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(1.5)
                            .padding(.top, 20)
                    }
                }
                
                    // 使用iOS 16兼容的导航方式
                    NavigationLink(value: "home") {
                    EmptyView()
                }
                    .opacity(0)
                
                    NavigationLink(value: "select") {
                    EmptyView()
                }
                    .opacity(0)
                }
                .navigationDestination(isPresented: $navigateToHome) {
                    ContentView()
                        .toolbar(.hidden, for: .navigationBar)
                }
                .navigationDestination(isPresented: $navigateToSelect) {
                    SelectView()
                        .toolbar(.hidden, for: .navigationBar)
            }
            .onAppear {
                validateToken()
            }
        }
        .toolbar(.hidden, for: .navigationBar)
        }
    }
    
    // 验证令牌
    private func validateToken() {
        // 检查是否有保存的token和用户ID
        guard !userState.token.isEmpty, userState.userId > 0 else {
            isValidatingToken = false
            navigateToSelect = true
            return
        }
        
        // 调用API验证token
        APIService.shared.validateToken(userId: userState.userId, token: userState.token) { result in
            DispatchQueue.main.async {
                isValidatingToken = false
                
                switch result {
                case .success(let userData):
                    // 更新用户信息，包括从后端获取的头像和余额
                    userState.login(userId: userData.userId, username: userData.username, avatar: userData.avatar, token: userState.token, balance: userData.balance)
                    navigateToHome = true
                case .failure:
                    // 验证失败，清空用户信息
                    userState.logout()
                    navigateToSelect = true
                }
            }
        }
    }
}

struct StartView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            StartView()
                .environmentObject(UserState())
                .previewDevice("iPhone 13 Pro Max")
                .previewDisplayName("iPhone 13 Pro Max")
            
            StartView()
                .environmentObject(UserState())
                .previewDevice("iPhone SE (3rd generation)")
                .previewDisplayName("iPhone SE")
        }
    }
}
