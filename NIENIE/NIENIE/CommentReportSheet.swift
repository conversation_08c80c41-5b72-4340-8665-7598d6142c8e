import SwiftUI

// 添加评论举报类型枚举
enum CommentReportType: String, CaseIterable {
    case advertisement = "广告"
    case tooSimilar = "相似过多"
    case adultContent = "色情低俗"
    case inappropriateContent = "内容不适"
    case clickbait = "夸张博眼球"
    case illegal = "违法违规"
    case racism = "种族歧视"
    case minorRelated = "涉未成年"
    case irrelevant = "笔记不相关"
    case other = "其他"
    
    // 获取对应的后端反馈类型
    var apiValue: String {
        return self.rawValue
    }
    
    // 获取图标名称
    var iconName: String {
        switch self {
        case .advertisement:
            return "megaphone.fill"
        case .tooSimilar:
            return "doc.on.doc.fill"
        case .adultContent:
            return "eye.slash.fill"
        case .inappropriateContent:
            return "exclamationmark.octagon.fill"
        case .clickbait:
            return "flame.fill"
        case .illegal:
            return "exclamationmark.shield.fill"
        case .racism:
            return "person.fill.xmark"
        case .minorRelated:
            return "person.fill.badge.minus"
        case .irrelevant:
            return "xmark.app.fill"
        case .other:
            return "ellipsis.circle.fill"
        }
    }
    
    // 获取图标颜色
    var iconColor: Color {
        switch self {
        case .advertisement:
            return .blue
        case .tooSimilar:
            return .green
        case .adultContent, .inappropriateContent, .illegal, .racism, .minorRelated:
            return .red
        case .clickbait:
            return .orange
        case .irrelevant, .other:
            return .gray
        }
    }
}

struct CommentReportSheet: View {
    let comment: CommentData
    @Binding var isPresented: Bool
    @EnvironmentObject var userState: UserState
    var onRemove: () -> Void
    @State private var showToast = false
    @State private var toastMessage = ""
    
    // 将举报类型分组
    let reportGroups: [[CommentReportType]] = [
        [.advertisement, .illegal],
        [.racism, .minorRelated],
        [.adultContent, .inappropriateContent],
        [.irrelevant, .other]
    ]
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 半透明背景，点击关闭菜单
                Color.black.opacity(0.4)
                    .edgesIgnoringSafeArea(.all)
                    .onTapGesture {
                        withAnimation(.spring()) {
                            isPresented = false
                        }
                    }
                
                // 底部菜单
                VStack(spacing: 0) {
                    Spacer()
                    
                    VStack(spacing: 0) {
                        // 标题
                        HStack {
                            Text("内容反馈")
                                .font(.system(size: 18, weight: .bold))
                                .foregroundColor(.black)
                                .padding(.vertical, 16)
                            
                            Spacer()
                            
                            Button(action: {
                                withAnimation(.spring()) {
                                    isPresented = false
                                }
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.system(size: 22))
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.horizontal, 20)
                        
                        Divider()
                            .padding(.horizontal, 10)
                        
                        // 报告选项
                        ScrollView {
                            VStack(spacing: 15) {
                                ForEach(reportGroups, id: \.self) { group in
                                    HStack(spacing: 15) {
                                        ForEach(group, id: \.self) { type in
                                            Button(action: {
                                                handleFeedback(type)
                                            }) {
                                                VStack(spacing: 8) {
                                                    Image(systemName: type.iconName)
                                                        .font(.system(size: 24))
                                                        .foregroundColor(type.iconColor)
                                                    
                                                    Text(type.rawValue)
                                                        .font(.system(size: 14))
                                                        .foregroundColor(.black)
                                                        .lineLimit(1)
                                                }
                                                .frame(maxWidth: .infinity)
                                                .padding(.vertical, 12)
                                                .background(Color.gray.opacity(0.1))
                                                .cornerRadius(10)
                                            }
                                        }
                                    }
                                    .padding(.horizontal, 15)
                                }
                            }
                            .padding(.vertical, 15)
                        }
                        .frame(height: 300) // 设置一个合适的高度
                        
                        // 添加底部安全区填充
                        Color.white
                            .frame(height: geometry.safeAreaInsets.bottom)
                    }
                    .frame(width: geometry.size.width) // 设置宽度为屏幕宽度
                    .background(Color.white)
                    .cornerRadius(12, corners: [.topLeft, .topRight])
                }
                
                // 显示Toast提示
                if showToast {
                    VStack {
                        Spacer()
                        Text(toastMessage)
                            .foregroundColor(.white)
                            .padding(.vertical, 10)
                            .padding(.horizontal, 20)
                            .background(Color.black.opacity(0.7))
                            .cornerRadius(20)
                            .padding(.bottom, 100)
                    }
                    .transition(.opacity)
                    .zIndex(100)
                }
            }
            .edgesIgnoringSafeArea(.all) // 确保覆盖整个屏幕
            .frame(width: geometry.size.width, height: geometry.size.height)
            .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
        }
        .transition(.move(edge: .bottom))
        .animation(.spring(), value: isPresented)
    }
    
    // 处理反馈
    private func handleFeedback(_ type: CommentReportType) {
        // 发送内容反馈到后端
        APIService.shared.submitFeedback(
            userId: userState.userId,
            imageId: comment.commentId, // 使用评论ID
            feedbackType: type.apiValue,
            imagePath: "comment_\(comment.commentId)" // 添加前缀以区分评论
        ) { result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    showToast(message: "反馈成功，将为您优化结果")
                case .failure:
                    showToast(message: "反馈失败，请稍后再试")
                }
            }
        }
    }
    
    // 显示Toast提示
    private func showToast(message: String) {
        toastMessage = message
        withAnimation {
            showToast = true
        }
        
        // 1秒后关闭Toast并关闭菜单
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            withAnimation {
                showToast = false
            }
            
            // 关闭菜单
            isPresented = false
            onRemove()
        }
    }
}

// 预览
struct CommentReportSheet_Previews: PreviewProvider {
    static var previews: some View {
        CommentReportSheet(
            comment: CommentData(
                commentId: 1,
                imageId: 1,
                userId: 1,
                username: "用户名",
                avatar: "4-1",
                parentCommentId: nil,
                content: "这是一条评论",
                createdAt: "2023-06-01T12:00:00"
            ),
            isPresented: .constant(true),
            onRemove: {}
        )
        .environmentObject(UserState())
    }
} 