import SwiftUI

// 全局函数，用于显示评论操作菜单
func showActionMenu(comment: CommentData) {
    // 获取当前的UIWindow
    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
       let _ = windowScene.windows.first {
        
        // 创建一个临时状态对象来控制菜单的显示
        let tempState = CommentMenuState.shared
        tempState.currentComment = comment
        tempState.isPresented = true
        
        // 发送通知以显示菜单
        NotificationCenter.default.post(
            name: NSNotification.Name("ShowCommentActionMenu"),
            object: nil,
            userInfo: ["comment": comment]
        )
    }
}

// 单例状态对象，用于在视图之间共享评论菜单状态
class CommentMenuState: ObservableObject {
    static let shared = CommentMenuState()
    
    @Published var isPresented: Bool = false
    @Published var currentComment: CommentData? = nil
    @Published var showReportMenu: Bool = false
    @Published var reportingComment: CommentData? = nil
    
    private init() {}
}

// 评论菜单容器视图，用于在ZStack的顶层显示菜单
struct CommentMenuContainer: View {
    @StateObject private var menuState = CommentMenuState.shared
    @EnvironmentObject var userState: UserState
    
    var body: some View {
        ZStack {
            // 评论操作菜单
            if menuState.isPresented, let comment = menuState.currentComment {
                CommentActionSheet(
                    comment: comment,
                    currentUserId: userState.userId,
                    isPresented: $menuState.isPresented,
                    onAction: { action in
                        handleAction(action, comment: comment)
                    }
                )
                .zIndex(100)
            }
            
            // 评论举报菜单
            if menuState.showReportMenu, let comment = menuState.reportingComment {
                CommentReportSheet(
                    comment: comment,
                    isPresented: $menuState.showReportMenu,
                    onRemove: {
                        menuState.reportingComment = nil
                    }
                )
                .environmentObject(userState)
                .zIndex(101)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ShowCommentActionMenu"))) { notification in
            if let comment = notification.userInfo?["comment"] as? CommentData {
                menuState.currentComment = comment
                menuState.isPresented = true
            }
        }
    }
    
    private func handleAction(_ action: CommentAction, comment: CommentData) {
        switch action {
        case .reply:
            // 发送回复通知
            NotificationCenter.default.post(
                name: NSNotification.Name("ReplyToComment"),
                object: nil,
                userInfo: ["comment": comment]
            )
        case .delete:
            // 发送删除通知
            NotificationCenter.default.post(
                name: NSNotification.Name("DeleteComment"),
                object: nil,
                userInfo: ["commentId": comment.commentId]
            )
        case .report:
            // 显示举报菜单
            menuState.reportingComment = comment
            menuState.showReportMenu = true
        }
    }
} 
