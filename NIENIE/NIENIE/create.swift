import SwiftUI
import PhotosUI
import UIKit

// 新的图片来源选择弹窗（包含在线模版选项）
struct ImageSourcePickerSheet: View {
    @Binding var isPresented: Bool
    var onTemplateSelected: () -> Void
    var onCameraSelected: () -> Void
    var onPhotoLibrarySelected: () -> Void
    var showTemplateOption: Bool = true // 控制是否显示在线模版选项

    var body: some View {
        ZStack(alignment: .bottom) {
            // 半透明背景
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                    .zIndex(0)
            }

            // 底部菜单
            VStack(spacing: 0) {
                // 提示文字
                Text("请选择您的图片来源")
                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                    .padding(.top, 20)
                    .padding(.bottom, 20)

                VStack(spacing: 18) {
                    // 在线模版按钮（可选显示）
                    if showTemplateOption {
                        Button(action: {
                            onTemplateSelected()
                        }) {
                            ZStack(alignment: .center) {
                                // 背景矩形
                                Rectangle()
                                    .foregroundColor(.clear)
                                    .frame(maxWidth: .infinity, maxHeight: 50)
                                    .background(.white)
                                    .cornerRadius(12)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .inset(by: 0.25)
                                            .stroke(
                                                Color(red: 0.98, green: 0.85, blue: 0.37),
                                                lineWidth: 0.50
                                            )
                                    )
                                    .shadow(
                                        color: Color(red: 0.98, green: 0.85, blue: 0.37, opacity: 0.40),
                                        radius: 10
                                    )

                                // 图标和文本的组合
                                HStack(spacing: 15) {
                                    Image(systemName: "photo.on.rectangle.angled")
                                        .frame(width: 20, height: 20)
                                        .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))

                                    Text("在线模版")
                                        .font(Font.custom("MiSans", size: 18))
                                        .multilineTextAlignment(.center)
                                        .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                                }
                            }
                        }
                    }

                    // 拍照上传按钮
                    Button(action: {
                        onCameraSelected()
                    }) {
                        ZStack(alignment: .center) {
                            // 背景矩形
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(maxWidth: .infinity, maxHeight: 50)
                                .background(.white)
                                .cornerRadius(12)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .inset(by: 0.25)
                                        .stroke(
                                            Color(red: 0.98, green: 0.85, blue: 0.37),
                                            lineWidth: 0.50
                                        )
                                )
                                .shadow(
                                    color: Color(red: 0.98, green: 0.85, blue: 0.37, opacity: 0.40),
                                    radius: 10
                                )

                            // 图标和文本的组合
                            HStack(spacing: 15) {
                                Image("c-1")
                                    .frame(width: 20, height: 20)

                                Text("拍照上传")
                                    .font(Font.custom("MiSans", size: 18))
                                    .multilineTextAlignment(.center)
                                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                            }
                        }
                    }

                    // 本地相册按钮
                    Button(action: {
                        onPhotoLibrarySelected()
                    }) {
                        ZStack(alignment: .center) {
                            // 背景矩形
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(maxWidth: .infinity, maxHeight: 50)
                                .background(.white)
                                .cornerRadius(12)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .inset(by: 0.25)
                                        .stroke(
                                            Color(red: 0.98, green: 0.85, blue: 0.37),
                                            lineWidth: 0.50
                                        )
                                )
                                .shadow(
                                    color: Color(red: 0.98, green: 0.85, blue: 0.37, opacity: 0.40),
                                    radius: 10
                                )

                            // 图标和文本的组合
                            HStack(spacing: 15) {
                                Image("c-2")
                                    .frame(width: 20, height: 20)

                                Text("本地相册")
                                    .font(Font.custom("MiSans", size: 18))
                                    .multilineTextAlignment(.center)
                                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                            }
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 30)
            }
            .background(
                Rectangle()
                    .fill(Color.white) // 改为白色背景
                    .cornerRadius(20, corners: [.topLeft, .topRight])
                    .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: -5)
                    .frame(width: UIScreen.main.bounds.width) // 将宽度设置为屏幕宽度
            )
            .offset(y: isPresented ? 0 : UIScreen.main.bounds.height)
            .animation(.spring(), value: isPresented)
            .zIndex(1)
            .accessibilityElement(children: .contain) // 修复AX Safe category错误
        }
        .ignoresSafeArea()
    }
}

// 保留原有的ImagePickerSheet以保持兼容性
struct ImagePickerSheet: View {
    @Binding var isPresented: Bool
    var onCameraSelected: () -> Void
    var onPhotoLibrarySelected: () -> Void

    var body: some View {
        ZStack(alignment: .bottom) {
            // 半透明背景
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                    .zIndex(0)
            }

            // 底部菜单
            VStack(spacing: 18) {
                // 拍照上传按钮
                Button(action: {
                    onCameraSelected()
                }) {
                    ZStack(alignment: .center) {
                        // 背景矩形
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(maxWidth: .infinity, maxHeight: 50)
                            .background(.white)
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .inset(by: 0.25)
                                    .stroke(
                                        Color(red: 0.98, green: 0.85, blue: 0.37),
                                        lineWidth: 0.50
                                    )
                            )
                            .shadow(
                                color: Color(red: 0.98, green: 0.85, blue: 0.37, opacity: 0.40),
                                radius: 10
                            )

                        // 图标和文本的组合
                        HStack(spacing: 15) {
                            Image("c-1")
                                .frame(width: 20, height: 20)

                            Text("拍照上传")
                                .font(Font.custom("MiSans", size: 18))
                                .multilineTextAlignment(.center)
                                .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                        }
                    }
                }

                // 本地相册按钮
                Button(action: {
                    onPhotoLibrarySelected()
                }) {
                    ZStack(alignment: .center) {
                        // 背景矩形
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(maxWidth: .infinity, maxHeight: 50)
                            .background(.white)
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .inset(by: 0.25)
                                    .stroke(
                                        Color(red: 0.98, green: 0.85, blue: 0.37),
                                        lineWidth: 0.50
                                    )
                            )
                            .shadow(
                                color: Color(red: 0.98, green: 0.85, blue: 0.37, opacity: 0.40),
                                radius: 10
                            )

                        // 图标和文本的组合
                        HStack(spacing: 15) {
                            Image("c-2")
                                .frame(width: 20, height: 20)

                            Text("本地相册")
                                .font(Font.custom("MiSans", size: 18))
                                .multilineTextAlignment(.center)
                                .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                        }
                    }
                }

                // 取消按钮已删除
            }
            .padding(.top, 10) // 按钮上方增加10个点的空间
            .padding(.bottom, 30)
            .padding(.horizontal, 20)
            .background(
                Rectangle()
                    .fill(Color.white) // 改为白色背景
                    .cornerRadius(20, corners: [.topLeft, .topRight])
                    .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: -5)
                    .frame(width: UIScreen.main.bounds.width) // 将宽度设置为屏幕宽度
            )
            .offset(y: isPresented ? 0 : UIScreen.main.bounds.height)
            .animation(.spring(), value: isPresented)
            .zIndex(1)
            .accessibilityElement(children: .contain) // 修复AX Safe category错误
        }
        .ignoresSafeArea()
    }
}

// 修改原有的CreateSheet，保留用于兼容性
struct CreateSheet: View {
    @Binding var isPresented: Bool
    @State private var showImagePicker = false
    @State private var showCamera = false
    @State private var selectedImage: UIImage?
    @State private var sourceType: UIImagePickerController.SourceType = .camera
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        ImagePickerSheet(isPresented: $isPresented, 
                        onCameraSelected: {
                            sourceType = .camera
                            showCamera = true
                        }, 
                        onPhotoLibrarySelected: {
                            sourceType = .photoLibrary
                            showImagePicker = true
                        })
        .fullScreenCover(isPresented: $showImagePicker) {
            ImagePickerView(sourceType: .photoLibrary) { image in
                if let image = image {
                    self.selectedImage = image
                    handleSelectedImage()
                }
            }
        }
        .fullScreenCover(isPresented: $showCamera) {
            ImagePickerView(sourceType: .camera) { image in
                if let image = image {
                    self.selectedImage = image
                    handleSelectedImage()
                }
            }
        }
    }
    
    // 处理选择的图片
    private func handleSelectedImage() {
        guard let image = selectedImage else {
            return
        }
        
        
        // 关闭弹窗并导航到图片编辑页面
        isPresented = false
        
        // 显示图片编辑界面，使用主队列延迟处理，确保UI更新完成
        DispatchQueue.main.async {
            presentImageEditView(image: image)
        }
    }
    
    // 直接以模态方式显示图片编辑界面
    private func presentImageEditView(image: UIImage) {
        let hostingController = UIHostingController(rootView: ImageEditView(image: image))
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            hostingController.modalPresentationStyle = .fullScreen
            rootViewController.present(hostingController, animated: true) {
            }
        } 
    }
}

struct create: View {
    @State private var isPresented = true
    
    var body: some View {
        CreateSheet(
            isPresented: $isPresented
        )
    }
}

struct createPreviews: PreviewProvider {
    static var previews: some View {
        Group {
            create()
        }
    }
}

