import SwiftUI
import Photos

// 简化版的创作记录预览，显示在个人中心页面
struct CreationRecordView: View {
    @EnvironmentObject var userState: UserState
    @State private var navigateToFullView = false
    
    var body: some View {
       
        NavigationLink(destination: FullCreationRecordView().environmentObject(userState), isActive: $navigateToFullView) {
            HStack {
                Image(systemName: "photo.stack")
                    .font(.system(size: 20))
                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                
                Text("创作记录")
                    .font(Font.custom("PingFang SC", size: 16).weight(.bold))
                    .foregroundColor(.black)
                
                Spacer()
                
                // 预览显示最近3张图片的小缩略图
                HStack(spacing: 5) {
                    ForEach(Array(userState.creationRecords.prefix(3).enumerated()), id: \.element.id) { index, record in
                        if let image = userState.loadCreationImageThumbnail(fileName: record.fileName, size: CGSize(width: 60, height: 60)) {
                            Image(uiImage: image)
                                .resizable()
                                .scaledToFill()
                                .frame(width: 30, height: 30)
                                .clipShape(RoundedRectangle(cornerRadius: 4))
                        }
                    }
                }
                .padding(.trailing, 5)
                
                // 添加箭头指示
                Image(systemName: "chevron.right")
                    .font(.system(size: 16))
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(Color.white.opacity(0.7))
            .cornerRadius(10)
            .padding(.horizontal, 16)
            .contentShape(Rectangle()) // 确保整行可点击
            .onTapGesture {
                navigateToFullView = true
            }
        }
        .buttonStyle(PlainButtonStyle()) // 移除导航链接的默认样式
    }
}

// 图片缓存管理器
class ImageCacheManager {
    static let shared = ImageCacheManager()
    private var cache = NSCache<NSString, UIImage>()
    
    private init() {
        // 设置缓存上限
        cache.countLimit = 500 // 最多缓存50张图片
        cache.totalCostLimit = 500 * 1024 * 1024 // 约500MB
    }
    
    func setImage(_ image: UIImage, forKey key: String) {
        cache.setObject(image, forKey: key as NSString)
    }
    
    func getImage(forKey key: String) -> UIImage? {
        return cache.object(forKey: key as NSString)
    }
    
    func removeImage(forKey key: String) {
        cache.removeObject(forKey: key as NSString)
    }
    
    func clearCache() {
        cache.removeAllObjects()
    }
}

// 添加可缩放移动的图片视图
struct ZoomableScrollView<Content: View>: UIViewRepresentable {
    private var content: Content
    private var minScale: CGFloat = 1.0
    private var maxScale: CGFloat = 3.0  // 最大放大倍数。
    private var onTap: () -> Void
    
    init(@ViewBuilder content: () -> Content, onTap: @escaping () -> Void) {
        self.content = content()
        self.onTap = onTap
    }
    
    func makeUIView(context: Context) -> UIScrollView {
        // 设置UIScrollView
        let scrollView = UIScrollView()
        scrollView.delegate = context.coordinator
        scrollView.maximumZoomScale = maxScale
        scrollView.minimumZoomScale = minScale
        scrollView.bouncesZoom = true
        scrollView.showsVerticalScrollIndicator = false
        scrollView.showsHorizontalScrollIndicator = false
        
        // 创建UIHostingController来托管SwiftUI视图
        let hostedView = UIHostingController(rootView: content)
        hostedView.view.translatesAutoresizingMaskIntoConstraints = false
        
        // 添加托管视图到滚动视图
        scrollView.addSubview(hostedView.view)
        
        // 设置托管视图的约束
        NSLayoutConstraint.activate([
            hostedView.view.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            hostedView.view.heightAnchor.constraint(equalTo: scrollView.heightAnchor),
            hostedView.view.centerXAnchor.constraint(equalTo: scrollView.centerXAnchor),
            hostedView.view.centerYAnchor.constraint(equalTo: scrollView.centerYAnchor)
        ])
        
        // 保存托管控制器的引用
        context.coordinator.hostingController = hostedView
        
        // 添加双击手势
        let doubleTapGesture = UITapGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handleDoubleTap(_:)))
        doubleTapGesture.numberOfTapsRequired = 2
        scrollView.addGestureRecognizer(doubleTapGesture)
        
        // 添加单击手势
        let singleTapGesture = UITapGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handleSingleTap))
        singleTapGesture.numberOfTapsRequired = 1
        scrollView.addGestureRecognizer(singleTapGesture)
        
        // 确保单击手势在双击手势失败后才触发
        singleTapGesture.require(toFail: doubleTapGesture)
        
        return scrollView
    }
    
    func updateUIView(_ uiView: UIScrollView, context: Context) {
        // 更新托管视图的内容
        context.coordinator.hostingController?.rootView = content
        context.coordinator.onTap = onTap
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self, onTap: onTap)
    }
    
    class Coordinator: NSObject, UIScrollViewDelegate {
        var parent: ZoomableScrollView
        var hostingController: UIHostingController<Content>?
        var onTap: () -> Void
        
        init(_ parent: ZoomableScrollView, onTap: @escaping () -> Void) {
            self.parent = parent
            self.onTap = onTap
        }
        
        func viewForZooming(in scrollView: UIScrollView) -> UIView? {
            return hostingController?.view
        }
        
        @objc func handleDoubleTap(_ gesture: UITapGestureRecognizer) {
            guard let scrollView = gesture.view as? UIScrollView else { return }
            
            if scrollView.zoomScale == 1 {
                // 放大到2倍
                let location = gesture.location(in: scrollView)
                let rect = CGRect(x: location.x - 50, y: location.y - 50, width: 100, height: 100)
                scrollView.zoom(to: rect, animated: true)
            } else {
                // 缩小到原始大小
                scrollView.setZoomScale(1.0, animated: true)
            }
        }
        
        @objc func handleSingleTap() {
            // 单击时退出大图模式
            onTap()
        }
    }
}

// 完整的创作记录视图页面
struct FullCreationRecordView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userState: UserState
    @State private var isLongPressing: UUID? = nil
    @State private var showActionSheet = false
    @State private var selectedRecordId: UUID? = nil
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var selectedImage: UIImage? = nil
    @State private var showFullImage = false
    
    // 每行显示两列图片
    private let columns = [GridItem(.flexible()), GridItem(.flexible())]
    
    // 图片固定宽高比 - 使用16:9的宽高比
    private let imageAspectRatio: CGFloat = 16/9
    
    init() {
    }
    
    var body: some View {
        ZStack {
            VStack(spacing: 0) {
                // 标题栏
                HStack {
                    // 返回按钮
                    Button(action: {
                        // 清除图片缓存
                        ImageCacheManager.shared.clearCache()
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "arrow.left")
                            .font(.system(size: 18))
                            .foregroundColor(.black)
                    }
                    .padding(.leading, 16)
                    
                    Spacer()
                    
                    // 标题
                    Text("创作记录")
                        .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                        .foregroundColor(.black)
                    
                    Spacer()
                    
                    // 占位，保持标题居中
                    Color.clear
                        .frame(width: 20, height: 20)
                        .padding(.trailing, 16)
                }
                .padding(.top, 16)
                .padding(.bottom, 20)
                
                if userState.creationRecords.isEmpty {
                    Spacer()
                    Text("暂无创作记录")
                        .font(.system(size: 16))
                        .foregroundColor(.gray)
                    Spacer()
                } else {
                    // 图片网格
                    ScrollView {
                        LazyVGrid(columns: columns, spacing: 15) {
                            ForEach(userState.creationRecords) { record in
                                creationRecordItem(record: record)
                                    .id(record.id) // 确保每个项有唯一ID
                            }
                        }
                        .padding(.horizontal, 16)
                    }
                }
            }
            .navigationBarHidden(true)
            
            // 全屏显示选中的图片，使用可缩放视图
            if showFullImage, let image = selectedImage {
                Color.black.opacity(0.8)
                    .edgesIgnoringSafeArea(.all)
                    .onTapGesture {
                        withAnimation {
                            showFullImage = false
                        }
                    }
                
                // 使用可缩放视图替换原来的图片视图
                ZoomableScrollView {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } onTap: {
                    // 单击时关闭大图
                    withAnimation {
                        showFullImage = false
                    }
                }
                .edgesIgnoringSafeArea(.all)
                .transition(.opacity)
            }
        }
        .actionSheet(isPresented: $showActionSheet) {
            ActionSheet(
                title: Text("图片操作"),
                message: Text("请选择要执行的操作"),
                buttons: [
                    .default(Text("保存到相册")) {
                        saveImageToAlbum()
                    },
                    .destructive(Text("删除")) {
                        deleteRecord()
                    },
                    .cancel(Text("取消"))
                ]
            )
        }
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
        .onDisappear {
            // 视图消失时清除缓存
            ImageCacheManager.shared.clearCache()
        }
    }
    
    // 创作记录项
    private func creationRecordItem(record: CreationRecord) -> some View {
        // 获取屏幕宽度
        let screenWidth = UIScreen.main.bounds.width
        // 计算每个图片的宽度 (屏幕宽度 - 左右边距 - 中间间距) / 2
        let itemWidth = (screenWidth - 32 - 15) / 2
        // 根据宽高比计算高度
        let itemHeight = itemWidth / imageAspectRatio
        
        return CachedImage(fileName: record.fileName, size: CGSize(width: itemWidth*2, height: itemHeight*2))
            .frame(height: itemHeight)
            .clipped()
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(
                        isLongPressing == record.id ? Color.blue : Color.clear,
                        lineWidth: 3
                    )
            )
            .contentShape(Rectangle())
            .onTapGesture {
                // 点击显示大图
                if let image = userState.loadCreationImage(fileName: record.fileName) {
                    selectedImage = image
                    withAnimation {
                        showFullImage = true
                    }
                }
            }
            .onLongPressGesture(
                minimumDuration: 0.5,
                pressing: { isPressing in
                    withAnimation {
                        isLongPressing = isPressing ? record.id : nil
                    }
                },
                perform: {
                    selectedRecordId = record.id
                    showActionSheet = true
                }
            )
    }
    
    // 删除记录
    private func deleteRecord() {
        guard let id = selectedRecordId else { return }
        // 清除该记录的缓存
        if let record = userState.creationRecords.first(where: { $0.id == id }) {
            ImageCacheManager.shared.removeImage(forKey: record.fileName)
        }
        userState.deleteCreationRecord(id: id)
        selectedRecordId = nil
    }
    
    // 保存图片到相册
    private func saveImageToAlbum() {
        guard let id = selectedRecordId,
              let record = userState.creationRecords.first(where: { $0.id == id }),
              let image = userState.loadCreationImage(fileName: record.fileName) else {
            return
        }
        
        // 直接保存到相册
        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        
        // 显示保存成功提示
        alertMessage = "图片已保存到相册"
        showAlert = true
        
        selectedRecordId = nil
    }
}

// 缓存图片组件
struct CachedImage: View {
    let fileName: String
    let size: CGSize
    @State private var image: UIImage? = nil
    
    var body: some View {
        Group {
            if let img = image {
                Image(uiImage: img)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } else {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .overlay(
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                    )
            }
        }
        .onAppear {
            loadImage()
        }
    }
    
    private func loadImage() {
        // 先尝试从缓存获取
        if let cachedImage = ImageCacheManager.shared.getImage(forKey: fileName) {
            self.image = cachedImage
            return
        }
        
        // 异步加载图片
        DispatchQueue.global(qos: .userInitiated).async {
            if let loadedImage = UserState.shared.loadCreationImageThumbnail(fileName: fileName, size: size) {
                // 将图片缓存
                ImageCacheManager.shared.setImage(loadedImage, forKey: fileName)
                // 主线程更新UI
                DispatchQueue.main.async {
                    self.image = loadedImage
                }
            }
        }
    }
}

struct CreationRecordView_Previews: PreviewProvider {
    static var previews: some View {
        CreationRecordView()
            .environmentObject(UserState.shared)
    }
} 