import SwiftUI
import CoreML
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins
import PhotosUI
import Photos

// MARK: - 超分辨率配置
struct SuperResolutionConfig {
    var outscale: Float = 4.0 // 放大倍数（2.0或4.0）
    var tileSize: Int = 256 // 瓦片大小（处理大图时分块）
    var enableConcurrentProcessing: Bool = true // 是否启用并发处理（可能增加CPU使用率）
    // GPU加速默认开启，不再提供UI选项
}



// MARK: - 处理状态枚举
enum ProcessingState: Equatable {
    case idle
    case loading
    case processing
    case completed
    case error(String)

    static func == (lhs: ProcessingState, rhs: ProcessingState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.loading, .loading), (.processing, .processing), (.completed, .completed):
            return true
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

// MARK: - 主视图
struct SuperResolutionView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userState: UserState

    // 图像相关状态
    @State private var selectedImage: UIImage?
    @State private var originalImage: UIImage?
    @State private var processedImage: UIImage?
    @State private var showImagePicker = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var beforeAfterPosition: CGFloat = 0.5

    // 处理状态
    @State private var processingState: ProcessingState = .idle
    @State private var processingProgress: Float = 0.0

    // 超分辨率配置
    @State private var config = SuperResolutionConfig()

    // CoreML模型
    @State private var doubleModel: MLModel?  // 2倍放大模型
    @State private var quadModel: MLModel?    // 4倍放大模型

    // UI控制
    @State private var showSaveOptions = false



    var body: some View {
        NavigationView {
            ZStack {
                Color.white.ignoresSafeArea()

                VStack(spacing: 0) {
                    headerView

                    if selectedImage == nil {
                        emptyStateView
                    } else {
                        ScrollView(.vertical, showsIndicators: false) {
                            VStack(spacing: 20) {
                                imageDisplayView
                                    .padding(.horizontal, 20)

                                controlPanelView
                                    .padding(.bottom, 100) // 给底部留出空间
                            }
                        }
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedImage)
        }
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
        .actionSheet(isPresented: $showSaveOptions) {
            ActionSheet(
                title: Text("保存超分图片"),
                message: Text("选择保存方式"),
                buttons: [
                    .default(Text("保存到相册")) {
                        saveImageToPhotoLibrary()
                    },
                    .cancel(Text("取消"))
                ]
            )
        }
        .onAppear {
            loadMLModel()
        }
        .onChange(of: selectedImage) { _, newImage in
            if let image = newImage {
                originalImage = image
                processedImage = nil
                processingState = .idle
            }
        }
    }

    // MARK: - 子视图
    private var headerView: some View {
        HStack {
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "arrow.left")
                    .font(.system(size: 18))
                    .foregroundColor(.black)
            }

            Spacer()

            Text("AI超分辨率")
                .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                .foregroundColor(.black)

            Spacer()

            Button(action: {
                showImagePicker = true
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "photo")
                        .font(.system(size: 16))
                    Text("选择图片")
                        .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue)
                .cornerRadius(8)
            }
        }
        .padding(.horizontal, 16)
        .padding(.top, 16)
        .padding(.bottom, 20)
    }

    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Spacer()

            Image(systemName: "arrow.up.right.square")
                .font(.system(size: 60))
                .foregroundColor(.blue.opacity(0.7))

            Text("选择图片开始超分辨率处理")
                .font(Font.custom("PingFang SC", size: 18))
                .foregroundColor(.gray)

            Text("AI将图片分辨率提升，让图像更清晰")
                .font(Font.custom("PingFang SC", size: 14))
                .foregroundColor(.gray.opacity(0.7))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)

            Button(action: {
                showImagePicker = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "photo.fill")
                    Text("选择图片")
                        .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: [Color.blue, Color.purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(10)
                .shadow(color: Color.blue.opacity(0.3), radius: 5, x: 0, y: 2)
            }

            // 功能说明
            VStack(spacing: 8) {
                Text("支持功能：")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.gray)

                HStack(spacing: 20) {
                    FeatureItem(icon: "arrow.up.right.square.fill", text: "4倍超分")
                    FeatureItem(icon: "speedometer", text: "快速处理")
                    FeatureItem(icon: "cpu", text: "AI增强")
                }
            }
            .padding(.top, 20)

            Spacer()
        }
    }

    private func FeatureItem(icon: String, text: String) -> some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(.blue)
            Text(text)
                .font(.system(size: 12))
                .foregroundColor(.gray)
        }
    }

    private var imageDisplayView: some View {
        ZStack {
            Color.black.opacity(0.05)
                .cornerRadius(12)

            if let originalImg = originalImage {
                if let processed = processedImage {
                    BeforeAfterSlider(
                        beforeImage: originalImg,
                        afterImage: processed,
                        sliderPosition: $beforeAfterPosition
                    )
                    .cornerRadius(12)
                } else {
                    Image(uiImage: originalImg)
                        .resizable()
                        .scaledToFit()
                        .cornerRadius(12)
                }
            }

            // 处理状态覆盖层
            if case .processing = processingState {
                VStack(spacing: 15) {
                    ProgressView(value: processingProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                        .frame(width: 200)

                    Text("AI超分处理中...")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.blue)

                    Text("\(Int(processingProgress * 100))%")
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                }
                .padding(20)
                .background(Color.white.opacity(0.95))
                .cornerRadius(12)
                .shadow(radius: 10)
            } else if case .loading = processingState {
                VStack(spacing: 10) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("准备处理...")
                        .font(.system(size: 16))
                        .foregroundColor(.gray)
                }
                .padding(20)
                .background(Color.white.opacity(0.95))
                .cornerRadius(12)
            }
        }
        .frame(height: 400)
        .clipped()
    }

    private var controlPanelView: some View {
        VStack(spacing: 15) {
            // 只显示基础控制
            basicControlsView
                .disabled(processingState == .processing || processingState == .loading)
                .opacity(processingState == .processing || processingState == .loading ? 0.5 : 1.0)

            // 处理按钮
            if originalImage != nil {
                Button(action: {
                    startSuperResolution()
                }) {
                    HStack {
                        Image(systemName: "arrow.up.right.square.fill")
                        Text(processingState == .processing ? "处理中..." : "开始超分处理")
                            .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        processingState == .processing ?
                        AnyView(Color.gray) :
                        AnyView(LinearGradient(colors: [Color.blue, Color.purple], startPoint: .leading, endPoint: .trailing))
                    )
                    .cornerRadius(10)
                    .shadow(color: Color.blue.opacity(0.3), radius: 5, x: 0, y: 2)
                }
                .disabled(processingState == .processing || processingState == .loading)
                .padding(.horizontal, 20)
            }

            // 保存按钮
            if processedImage != nil {
                Button(action: {
                    showSaveOptions = true
                }) {
                    HStack {
                        Image(systemName: "square.and.arrow.down.fill")
                        Text("保存超分图片")
                            .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.green)
                    .cornerRadius(10)
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 20)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(15)
        .animation(.easeInOut(duration: 0.2), value: processingState)
    }


    // MARK: - 基础控制视图
    private var basicControlsView: some View {
        VStack(spacing: 20) {

            // 放大倍数控制
            VStack(spacing: 15) {
                Text("放大倍数")
                    .font(.system(size: 14).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                // 预设倍数按钮（只支持2倍和4倍）
                HStack(spacing: 12) {
                    ForEach([2.0, 4.0], id: \.self) { scale in
                        Button(action: {
                            config.outscale = Float(scale)
                        }) {
                            VStack(spacing: 4) {
                                Text("\(Int(scale))x")
                                    .font(.system(size: 16, weight: .medium))
                                Text(scale == 2.0 ? "快速模式" : "高质量模式")
                                    .font(.system(size: 10))
                            }
                            .foregroundColor(config.outscale == Float(scale) ? .white : .blue)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(config.outscale == Float(scale) ? Color.blue : Color.blue.opacity(0.1))
                            .cornerRadius(8)
                        }
                    }
                    Spacer()
                }
                .padding(.horizontal, 20)

                // 模型说明
                VStack(spacing: 8) {
                    Text("模型说明")
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    HStack {
                        VStack(alignment: .leading, spacing: 2) {
                            Text("• 2倍模式：处理速度快，适合预览")
                            Text("• 4倍模式：质量更高，处理时间较长")
                        }
                        .font(.system(size: 11))
                        .foregroundColor(.gray)
                        Spacer()
                    }
                }
                .padding(.horizontal, 20)
            }

            // 图像信息显示
            if let originalImg = originalImage {
                VStack(spacing: 8) {
                    Text("图像信息")
                        .font(.system(size: 14).weight(.medium))
                        .foregroundColor(.black)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.horizontal, 20)

                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("原始尺寸")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                            Text("\(Int(originalImg.size.width)) × \(Int(originalImg.size.height))")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.black)
                        }

                        Spacer()

                        Image(systemName: "arrow.right")
                            .foregroundColor(.blue)

                        Spacer()

                        VStack(alignment: .trailing, spacing: 4) {
                            Text("输出尺寸")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                            Text("\(Int(originalImg.size.width * CGFloat(config.outscale))) × \(Int(originalImg.size.height * CGFloat(config.outscale)))")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.blue)
                        }
                    }
                    .padding(.horizontal, 20)
                }
                .padding(.vertical, 10)
                .background(Color.blue.opacity(0.05))
                .cornerRadius(8)
                .padding(.horizontal, 20)
            }
        }
    }







    // MARK: - 逻辑处理



    private func loadMLModel() {
        guard doubleModel == nil || quadModel == nil else { return }

        DispatchQueue.global(qos: .userInitiated).async {
            do {
                // 配置模型计算单元（优先使用Neural Engine）
                let configuration = MLModelConfiguration()
                // 尝试不同的计算单元配置
                if #available(iOS 13.0, *) {
                    // 优先使用Neural Engine，如果不可用则使用GPU
                    configuration.computeUnits = .all
                    // 启用GPU低精度加速
                    configuration.allowLowPrecisionAccumulationOnGPU = true
                } else {
                    configuration.computeUnits = .cpuAndGPU
                }

                // 加载2倍放大模型
                if let doubleModelURL = Bundle.main.url(forResource: "DoubleOrigin", withExtension: ".mlmodelc") {
                    self.doubleModel = try MLModel(contentsOf: doubleModelURL, configuration: configuration)
                }

                // 加载4倍放大模型
                if let quadModelURL = Bundle.main.url(forResource: "Origin", withExtension: ".mlmodelc") {
                    self.quadModel = try MLModel(contentsOf: quadModelURL, configuration: configuration)
                }

                // 检查是否至少加载了一个模型
                if self.doubleModel == nil && self.quadModel == nil {
                    DispatchQueue.main.async {
                        self.alertMessage = "未找到任何模型文件，请确保已将DoubleOrigin.mlmodelc和Origin.mlmodelc添加到项目中"
                        self.showAlert = true
                    }
                    return
                }
            } catch {
                DispatchQueue.main.async {
                    self.alertMessage = "模型加载失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
            }
        }
    }

    private func startSuperResolution() {
        guard let originalImg = originalImage else {
            alertMessage = "请先选择图片"
            showAlert = true
            return
        }

        // 根据放大倍数选择模型
        let selectedModel: MLModel?
        if config.outscale == 2.0 {
            selectedModel = doubleModel
            if selectedModel == nil {
                alertMessage = "2倍放大模型未加载，请确保DoubleOrigin.mlmodelc文件已添加到项目中"
                showAlert = true
                return
            }
        } else if config.outscale == 4.0 {
            selectedModel = quadModel
            if selectedModel == nil {
                alertMessage = "4倍放大模型未加载，请确保Origin.mlmodelc文件已添加到项目中"
                showAlert = true
                return
            }
        } else {
            alertMessage = "不支持的放大倍数，请选择2倍或4倍"
            showAlert = true
            return
        }

        guard let model = selectedModel else {
            alertMessage = "模型未加载"
            showAlert = true
            return
        }

        processingState = .loading
        processingProgress = 0.0

        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let result = try self.performSuperResolution(image: originalImg, model: model)

                DispatchQueue.main.async {
                    self.processedImage = result
                    self.processingState = .completed
                    self.processingProgress = 1.0
                }
            } catch {
                DispatchQueue.main.async {
                    self.processingState = .error(error.localizedDescription)
                    self.alertMessage = "超分处理失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
            }
        }
    }

    private func performSuperResolution(image: UIImage, model: MLModel) throws -> UIImage {
        // 更新处理状态
        DispatchQueue.main.async {
            self.processingState = .processing
            self.processingProgress = 0.1
        }

        // 1. 预处理图像
        guard let _ = image.cgImage else {
            throw SuperResolutionError.invalidImage
        }

        // 根据配置调整图像大小（如果需要分块处理）
        let _ = min(config.tileSize, Int(min(image.size.width, image.size.height)))
        let processedImage: UIImage

        // 统一使用tile处理，简化逻辑（小图可以看成一个tile）
        let maxTileSize = 256  // 模型支持的最大tile尺寸

        // 所有图片都使用tile处理方法，确保正确的填充和裁剪
        processedImage = try processTiledImage(image: image, model: model, tileSize: maxTileSize)

        DispatchQueue.main.async {
            self.processingProgress = 0.9
        }

        // 3. 后处理（如果需要）
        let finalImage = postProcessImage(processedImage)

        return finalImage
    }



    private func processTiledImage(image: UIImage, model: MLModel, tileSize: Int) throws -> UIImage {
        // 获取模型输入输出名称
        let inputDescriptions = model.modelDescription.inputDescriptionsByName
        let outputDescriptions = model.modelDescription.outputDescriptionsByName

        guard let inputName = inputDescriptions.keys.first,
              let outputName = outputDescriptions.keys.first else {
            throw SuperResolutionError.predictionFailed
        }

        guard let cgImage = image.cgImage else {
            throw SuperResolutionError.invalidImage
        }

        let inputWidth = cgImage.width
        let inputHeight = cgImage.height
        let scale = Int(config.outscale)  // 使用配置的放大倍数

        // 计算输出尺寸
        let outputWidth = inputWidth * scale
        let outputHeight = inputHeight * scale

        // 创建输出图像的上下文
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        guard let outputContext = CGContext(
            data: nil,
            width: outputWidth,
            height: outputHeight,
            bitsPerComponent: 8,
            bytesPerRow: 0,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else {
            throw SuperResolutionError.conversionFailed
        }

        // 计算tile数量（按256x256分割）
        let tilesX = Int(ceil(Double(inputWidth) / Double(tileSize)))
        let tilesY = Int(ceil(Double(inputHeight) / Double(tileSize)))
        let totalTiles = tilesX * tilesY



        // 处理每个tile（从左到右，从上到下）
        for y in 0..<tilesY {
            for x in 0..<tilesX {
                let tileIndex = y * tilesX + x + 1


                // 计算当前tile在原图中的位置和尺寸
                let startX = x * tileSize
                let startY = y * tileSize
                let endX = min(startX + tileSize, inputWidth)
                let endY = min(startY + tileSize, inputHeight)

                // 实际tile尺寸（可能小于256x256）
                let actualTileWidth = endX - startX
                let actualTileHeight = endY - startY

                // 提取tile
                let tileRect = CGRect(x: startX, y: startY, width: actualTileWidth, height: actualTileHeight)
                guard let tileImage = image.cropped(to: tileRect) else {
                    continue
                }

                // 如果tile尺寸不足256x256，需要填充
                let needsPadding = actualTileWidth < tileSize || actualTileHeight < tileSize
                let paddedTile = needsPadding ? tileImage.paddedToSquare(targetSize: tileSize) : tileImage

                if needsPadding {
                    let _ = tileSize - actualTileWidth
                    let _ = tileSize - actualTileHeight
                }

                // 处理tile（输入256x256，输出1024x1024）
                let processedTile = try processTile(paddedTile, model: model, inputName: inputName, outputName: outputName)

                // 计算在输出图像中需要提取的有效区域（移除填充对应的部分）
                let validOutputWidth = actualTileWidth * scale
                let validOutputHeight = actualTileHeight * scale



                // 从处理后的tile中提取有效区域（从左上角开始，移除右侧和下侧的填充）
                let validRect = CGRect(x: 0, y: 0, width: validOutputWidth, height: validOutputHeight)
                guard let validTile = processedTile.cropped(to: validRect),
                      let validCGImage = validTile.cgImage else {
                    continue
                }

                // 计算在最终输出图像中的位置
                let outputX = startX * scale
                // 修复Y坐标：CGContext坐标系原点在左下角，需要翻转Y坐标
                let outputY = (inputHeight - startY - actualTileHeight) * scale

                // 绘制到输出图像
                outputContext.draw(validCGImage, in: CGRect(
                    x: outputX,
                    y: outputY,
                    width: validOutputWidth,
                    height: validOutputHeight
                ))



                // 更新进度
                DispatchQueue.main.async {
                    self.processingProgress = Float(tileIndex) / Float(totalTiles) * 0.8 + 0.1
                }
            }
        }

        // 创建最终输出图像
        guard let outputCGImage = outputContext.makeImage() else {
            throw SuperResolutionError.conversionFailed
        }

        let resultImage = UIImage(cgImage: outputCGImage)
        return resultImage
    }

    // 处理单个tile（输入应该已经是256x256）
    private func processTile(_ tileImage: UIImage, model: MLModel, inputName: String, outputName: String) throws -> UIImage {
        // 确保输入是256x256
        guard tileImage.size.width == 256 && tileImage.size.height == 256 else {
            throw SuperResolutionError.invalidImage
        }

        // 转换为MLMultiArray
        guard let multiArray = tileImage.toMLMultiArray() else {
            throw SuperResolutionError.conversionFailed
        }

        // 创建输入
        let input = try MLDictionaryFeatureProvider(dictionary: [inputName: MLFeatureValue(multiArray: multiArray)])

        // 执行推理
        let output = try model.prediction(from: input)

        // 获取输出
        guard let outputMultiArray = output.featureValue(for: outputName)?.multiArrayValue else {
            throw SuperResolutionError.predictionFailed
        }

        guard let resultImage = UIImage.fromMLMultiArray(outputMultiArray) else {
            throw SuperResolutionError.conversionFailed
        }

        return resultImage
    }



    private func postProcessImage(_ image: UIImage) -> UIImage {
        // 根据配置进行后处理
        return image
    }

    private func formatFileSize(_ image: UIImage) -> String {
        guard let data = image.pngData() else { return "未知" }
        let bytes = data.count

        if bytes < 1024 {
            return "\(bytes) B"
        } else if bytes < 1024 * 1024 {
            return String(format: "%.1f KB", Double(bytes) / 1024.0)
        } else {
            return String(format: "%.1f MB", Double(bytes) / (1024.0 * 1024.0))
        }
    }



    private func saveProcessedImage() {
        guard let image = processedImage else { return }

        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        alertMessage = "超分辨率图片已保存到相册"
        showAlert = true
    }

    private func saveImageToPhotoLibrary() {
        guard let image = processedImage else {
            alertMessage = "没有可保存的图片"
            showAlert = true
            return
        }

        // 使用Photos框架保存图片
        PHPhotoLibrary.requestAuthorization { status in
            if status == .authorized {
                PHPhotoLibrary.shared().performChanges({
                    PHAssetChangeRequest.creationRequestForAsset(from: image)
                }) { success, error in
                    DispatchQueue.main.async {
                        if success {
                            self.alertMessage = "超分辨率图片已成功保存到相册"
                        } else {
                            self.alertMessage = "保存失败: \(error?.localizedDescription ?? "未知错误")"
                        }
                        self.showAlert = true
                    }
                }
            } else {
                DispatchQueue.main.async {
                    self.alertMessage = "需要相册访问权限才能保存图片"
                    self.showAlert = true
                }
            }
        }
    }
}

// MARK: - 超分辨率错误类型
enum SuperResolutionError: Error, LocalizedError {
    case invalidImage
    case modelNotLoaded
    case conversionFailed
    case predictionFailed
    case mergeFailed

    var errorDescription: String? {
        switch self {
        case .invalidImage:
            return "无效的图像格式"
        case .modelNotLoaded:
            return "模型未加载"
        case .conversionFailed:
            return "图像格式转换失败"
        case .predictionFailed:
            return "AI推理失败"
        case .mergeFailed:
            return "图像合并失败"
        }
    }
}

// MARK: - UIImage扩展
extension UIImage {
    func toCVPixelBuffer() -> CVPixelBuffer? {
        // 模型期望固定的256x256输入尺寸
        let modelInputSize = 256

        // 将图像填充到256x256（保持比例）
        let paddedImage = self.paddedToSquare(targetSize: modelInputSize)
        guard let _ = paddedImage.cgImage else { return nil }

        // 创建RGB格式的CVPixelBuffer
        let attrs = [
            kCVPixelBufferCGImageCompatibilityKey: kCFBooleanTrue,
            kCVPixelBufferCGBitmapContextCompatibilityKey: kCFBooleanTrue,
            kCVPixelBufferIOSurfacePropertiesKey: [:]
        ] as CFDictionary

        var pixelBuffer: CVPixelBuffer?
        let status = CVPixelBufferCreate(
            kCFAllocatorDefault,
            modelInputSize,
            modelInputSize,
            kCVPixelFormatType_32BGRA,
            attrs,
            &pixelBuffer
        )

        guard status == kCVReturnSuccess, let buffer = pixelBuffer else {
            return nil
        }

        CVPixelBufferLockBaseAddress(buffer, CVPixelBufferLockFlags(rawValue: 0))
        defer { CVPixelBufferUnlockBaseAddress(buffer, CVPixelBufferLockFlags(rawValue: 0)) }

        let pixelData = CVPixelBufferGetBaseAddress(buffer)
        let rgbColorSpace = CGColorSpaceCreateDeviceRGB()

        guard let context = CGContext(
            data: pixelData,
            width: modelInputSize,
            height: modelInputSize,
            bitsPerComponent: 8,
            bytesPerRow: CVPixelBufferGetBytesPerRow(buffer),
            space: rgbColorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedFirst.rawValue | CGBitmapInfo.byteOrder32Little.rawValue
        ) else {
            return nil
        }

        // 绘制调整后的图像
        context.translateBy(x: 0, y: CGFloat(modelInputSize))
        context.scaleBy(x: 1.0, y: -1.0)

        UIGraphicsPushContext(context)
        paddedImage.draw(in: CGRect(x: 0, y: 0, width: modelInputSize, height: modelInputSize))
        UIGraphicsPopContext()

        return buffer
    }

    // 添加图像缩放方法
    func resized(to size: CGSize) -> UIImage {
        UIGraphicsBeginImageContextWithOptions(size, false, 1.0)
        defer { UIGraphicsEndImageContext() }
        draw(in: CGRect(origin: .zero, size: size))
        return UIGraphicsGetImageFromCurrentImageContext() ?? self
    }

    // 将图像填充到指定尺寸，直接在右侧和下侧填充（不保持比例）
    func paddedToSquare(targetSize: Int) -> UIImage {
        let targetCGSize = CGSize(width: targetSize, height: targetSize)

        // 如果已经是目标尺寸，直接返回
        if Int(size.width) == targetSize && Int(size.height) == targetSize {
            return self
        }

        // 创建目标尺寸的图像上下文（scale设置为1.0确保精确尺寸）
        UIGraphicsBeginImageContextWithOptions(targetCGSize, false, 1.0)
        defer { UIGraphicsEndImageContext() }

        // 填充黑色背景
        UIColor.black.setFill()
        UIRectFill(CGRect(origin: .zero, size: targetCGSize))

        // 将原图像绘制到左上角，保持原始尺寸
        // 右侧和下侧会自动用黑色填充
        let drawRect = CGRect(x: 0, y: 0, width: min(size.width, CGFloat(targetSize)), height: min(size.height, CGFloat(targetSize)))
        draw(in: drawRect)

        return UIGraphicsGetImageFromCurrentImageContext() ?? self
    }

    // 转换为MLMultiArray（模型期望的格式：1x3x256x256，float32，0-1范围）
    func toMLMultiArray() -> MLMultiArray? {
        // 模型转换时使用的固定尺寸是256x256
        let modelInputSize = 256

        // 将图像调整到256x256（使用填充方式保持比例）
        let paddedImage = self.paddedToSquare(targetSize: modelInputSize)
        guard let cgImage = paddedImage.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height

        // 确保尺寸正确
        guard width == modelInputSize && height == modelInputSize else {
            return nil
        }

        // 创建MLMultiArray：形状为[1, 3, 256, 256]
        guard let multiArray = try? MLMultiArray(shape: [1, 3, modelInputSize, modelInputSize] as [NSNumber], dataType: .float32) else {
            return nil
        }

        // 创建位图上下文来提取像素数据
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8

        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB) else { return nil }

        var pixelData = [UInt8](repeating: 0, count: width * height * bytesPerPixel)

        guard let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else {
            return nil
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        // 优化的像素数据转换 - 使用指针直接访问避免双重循环
        let totalPixels = width * height


        // 获取MLMultiArray的数据指针以提高性能
        let dataPointer = multiArray.dataPointer.bindMemory(to: Float.self, capacity: totalPixels * 3)

        // 控制并发度的像素转换 - 按行处理而不是按像素
        let processorCount = ProcessInfo.processInfo.processorCount
        let optimalThreads = min(processorCount, 4) // 限制最多4个线程
        let rowsPerThread = height / optimalThreads



        DispatchQueue.concurrentPerform(iterations: optimalThreads) { threadIndex in
            let startRow = threadIndex * rowsPerThread
            let endRow = (threadIndex == optimalThreads - 1) ? height : (threadIndex + 1) * rowsPerThread

            for y in startRow..<endRow {
                for x in 0..<width {
                    let pixelIndex = y * width + x
                    let rgbaIndex = pixelIndex * bytesPerPixel

                    // 提取RGB值并归一化到0-1范围
                    let r = Float(pixelData[rgbaIndex]) / 255.0
                    let g = Float(pixelData[rgbaIndex + 1]) / 255.0
                    let b = Float(pixelData[rgbaIndex + 2]) / 255.0

                    // 计算在MLMultiArray中的索引 (CHW格式: [C, H, W])
                    let rIndex = y * width + x                    // R通道
                    let gIndex = totalPixels + y * width + x      // G通道
                    let bIndex = 2 * totalPixels + y * width + x  // B通道

                    // 直接写入数据指针
                    dataPointer[rIndex] = r
                    dataPointer[gIndex] = g
                    dataPointer[bIndex] = b
                }
            }
        }

        return multiArray
    }


    // 从MLMultiArray创建UIImage（输出应该是1024x1024）
    static func fromMLMultiArray(_ multiArray: MLMultiArray, originalSize: CGSize? = nil) -> UIImage? {
        // 期望输出格式：[1, 3, height, width]
        guard multiArray.shape.count == 4,
              multiArray.shape[0].intValue == 1,
              multiArray.shape[1].intValue == 3 else {
            return nil
        }

        let outputHeight = multiArray.shape[2].intValue
        let outputWidth = multiArray.shape[3].intValue

        // 优化的像素数据转换 - 使用指针直接访问避免双重循环
        let totalPixels = outputWidth * outputHeight
        var pixelData = [UInt8](repeating: 0, count: totalPixels * 4) // RGBA

        // 获取MLMultiArray的数据指针以提高性能
        let dataPointer = multiArray.dataPointer.bindMemory(to: Float.self, capacity: totalPixels * 3)

        // 控制并发度的像素转换 - 按行处理
        let processorCount = ProcessInfo.processInfo.processorCount
        let optimalThreads = min(processorCount, 4) // 限制最多4个线程
        let rowsPerThread = outputHeight / optimalThreads



        DispatchQueue.concurrentPerform(iterations: optimalThreads) { threadIndex in
            let startRow = threadIndex * rowsPerThread
            let endRow = (threadIndex == optimalThreads - 1) ? outputHeight : (threadIndex + 1) * rowsPerThread

            for y in startRow..<endRow {
                for x in 0..<outputWidth {
                    let pixelIndex = y * outputWidth + x

                    // 计算在MLMultiArray中的索引 (CHW格式: [C, H, W])
                    let rIndex = y * outputWidth + x                    // R通道
                    let gIndex = totalPixels + y * outputWidth + x      // G通道
                    let bIndex = 2 * totalPixels + y * outputWidth + x  // B通道

                    // 获取RGB值
                    let r = dataPointer[rIndex]
                    let g = dataPointer[gIndex]
                    let b = dataPointer[bIndex]

                    // 转换为RGBA格式
                    let rgbaIndex = pixelIndex * 4
                    pixelData[rgbaIndex] = UInt8(max(0, min(1, r)) * 255)     // R
                    pixelData[rgbaIndex + 1] = UInt8(max(0, min(1, g)) * 255) // G
                    pixelData[rgbaIndex + 2] = UInt8(max(0, min(1, b)) * 255) // B
                    pixelData[rgbaIndex + 3] = 255                             // A
                }
            }
        }



        // 创建CGImage
        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB) else { return nil }

        let bitsPerComponent = 8
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * outputWidth

        guard let context = CGContext(
            data: &pixelData,
            width: outputWidth,
            height: outputHeight,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else {
            return nil
        }

        guard let cgImage = context.makeImage() else {
            return nil
        }

        let image = UIImage(cgImage: cgImage)
        return image
    }

    static func fromCVPixelBuffer(_ pixelBuffer: CVPixelBuffer) -> UIImage? {
        let ciImage = CIImage(cvPixelBuffer: pixelBuffer)
        let context = CIContext()

        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else {
            return nil
        }

        return UIImage(cgImage: cgImage)
    }

    func cropped(to rect: CGRect) -> UIImage? {
        guard let cgImage = cgImage?.cropping(to: rect) else { return nil }
        return UIImage(cgImage: cgImage, scale: scale, orientation: imageOrientation)
    }
}

// MARK: - 超分辨率滑块组件
struct SuperResolutionSlider: View {
    let title: String
    @Binding var value: CGFloat
    let range: ClosedRange<CGFloat>
    let step: CGFloat
    let unit: String

    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Text(title)
                    .font(.system(size: 13))
                    .foregroundColor(.black)
                Spacer()
                Text(String(format: "%.1f%@", value, unit))
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.blue)
            }
            .padding(.horizontal, 20)

            Slider(value: $value, in: range, step: step)
                .accentColor(.blue)
                .padding(.horizontal, 20)
        }
    }
}

// ImagePicker已在StyleTransferView.swift中定义，此处删除重复定义
