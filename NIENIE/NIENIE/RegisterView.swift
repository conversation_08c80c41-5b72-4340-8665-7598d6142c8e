import SwiftUI

struct RegisterView: View {
    @EnvironmentObject var userState: UserState
    @Environment(\.dismiss) var dismiss
    
    let phone: String
    @State private var username: String = ""
    @State private var selectedAvatar: String = "4-1"
    @State private var isLoading = false
    @State private var errorMessage: String? = nil
    @State private var navigateToHome = false
    @FocusState private var isUsernameFocused: Bool
    
    // 可选头像列表
    private let avatars = ["4-7", "4-2", "4-3", "4-4", "4-5", "4-6"]
    
    // 头像网格布局
    private let columns = [
        GridItem(.flexible()),
        GridItem(.flexible()),
        GridItem(.flexible())
    ]
    
    var body: some View {
        ZStack {
            // 背景色
            Color.white.ignoresSafeArea()
                .onTapGesture {
                    // 点击背景收起键盘
                    isUsernameFocused = false
                }
            
            VStack(alignment: .center) {
                Text("注册")
                    .font(Font.custom("PingFang SC", size: 24).weight(.bold))
                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                    .padding(.top, 20)
                
                // 错误信息
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                        .font(Font.custom("PingFang SC", size: 14))
                        .foregroundColor(.red)
                        .padding(.top, 10)
                }
                
                // 选择头像
                Text("选择头像")
                    .font(Font.custom("PingFang SC", size: 16))
                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.leading, 20)
                    .padding(.top, 30)
                
                // 头像网格
                LazyVGrid(columns: columns, spacing: 20) {
                    ForEach(avatars, id: \.self) { avatar in
                        Button(action: {
                            selectedAvatar = avatar
                        }) {
                            Image(avatar)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: 80, height: 80)
                                .clipShape(Circle())
                                .overlay(
                                    Circle()
                                        .stroke(selectedAvatar == avatar ? Color.blue : Color.clear, lineWidth: 3)
                                )
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)
                
                // 用户名输入
                VStack(alignment: .leading) {
                    Text("输入用户名")
                        .font(Font.custom("PingFang SC", size: 16))
                        .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                        .padding(.leading, 20)
                        .padding(.top, 20)
                    
                    ZStack {
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(height: 40)
                            .background(Color(red: 0.95, green: 0.95, blue: 0.95))
                            .cornerRadius(20)
                            .padding(.horizontal, 20)
                        
                        TextField("", text: $username)
                            .font(Font.custom("PingFang SC", size: 16))
                            .padding(.horizontal, 30)
                            .focused($isUsernameFocused)
                            .submitLabel(.done) // 设置键盘确认按钮
                            .onSubmit {
                                isUsernameFocused = false // 点击确认按钮收起键盘
                            }
                        
                        // 占位文本
                        if username.isEmpty {
                            HStack {
                                Text("请输入用户名")
                                    .font(Font.custom("PingFang SC", size: 16))
                                    .foregroundColor(Color.gray.opacity(0.5))
                                    .padding(.leading, 30)
                                Spacer()
                            }
                        }
                    }
                }
                
                // 注册按钮
                Button(action: {
                    isUsernameFocused = false // 点击注册按钮时也收起键盘
                    register()
                }) {
                    Text("注册")
                        .font(Font.custom("PingFang SC", size: 18).weight(.medium))
                        .foregroundColor(.white)
                        .frame(width: 200, height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(LinearGradient(
                                    stops: [
                                        Gradient.Stop(color: Color(red: 0, green: 0.72, blue: 0.75), location: 0.00),
                                        Gradient.Stop(color: Color(red: 1, green: 0.78, blue: 0), location: 0.87),
                                    ],
                                    startPoint: UnitPoint(x: -0.41, y: -5.81),
                                    endPoint: UnitPoint(x: 0.57, y: 3.66)
                                ))
                        )
                }
                .disabled(isLoading || username.isEmpty)
                .opacity((isLoading || username.isEmpty) ? 0.5 : 1.0)
                .padding(.top, 40)
                
                // 加载指示器
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.5)
                        .padding(.top, 20)
                }
                
                Spacer()
                
                // 导航链接
            }
            .navigationDestination(isPresented: $navigateToHome) {
                ContentView()
                    .toolbar(.hidden, for: .navigationBar)
            }
            .padding(.top, 60)
        }
        .navigationBarTitleDisplayMode(.inline)
        .toolbar(.hidden, for: .navigationBar)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(.black)
                        .imageScale(.large)
                }
            }
        }
    }
    
    private func register() {
        guard !username.isEmpty else {
            errorMessage = "请输入用户名"
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        APIService.shared.register(phone: phone, username: username, avatar: selectedAvatar) { result in
            DispatchQueue.main.async {
                isLoading = false
                
                switch result {
                case .success(let userData):
                    // 保存用户数据
                    userState.login(
                        userId: userData.userId,
                        username: userData.username,
                        avatar: userData.avatar, // 使用后端返回的avatar
                        token: userData.token,
                        balance: userData.balance
                    )
                    
                    // 跳转到首页
                    navigateToHome = true
                    
                case .failure(let error):
                    errorMessage = error.localizedDescription
                }
            }
        }
    }
}

struct RegisterView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            RegisterView(phone: "13800138000")
                .environmentObject(UserState())
        }
    }
}
