import Foundation

/// 管理每个会话的最后一条消息ID的本地存储
class LastMessageIDManager {
    static let shared = LastMessageIDManager()
    
    private let userDefaults = UserDefaults.standard
    private let keyPrefix = "last_message_id_"
    
    private init() {}
    
    /// 获取指定会话的最后一条消息ID
    /// - Parameter conversationId: 会话ID
    /// - Returns: 最后一条消息ID，如果没有则返回0
    func getLastMessageID(for conversationId: String) -> Int {
        let key = keyPrefix + conversationId
        return userDefaults.integer(forKey: key)
    }
    
    /// 设置指定会话的最后一条消息ID
    /// - Parameters:
    ///   - messageId: 消息ID
    ///   - conversationId: 会话ID
    func setLastMessageID(_ messageId: Int, for conversationId: String) {
        let key = keyPrefix + conversationId
        userDefaults.set(messageId, forKey: key)
        userDefaults.synchronize()
    }
    
    /// 更新指定会话的最后一条消息ID（只有当新ID更大时才更新）
    /// - Parameters:
    ///   - messageId: 新的消息ID
    ///   - conversationId: 会话ID
    /// - Returns: 是否进行了更新
    @discardableResult
    func updateLastMessageID(_ messageId: Int, for conversationId: String) -> Bool {
        let currentId = getLastMessageID(for: conversationId)
        if messageId > currentId {
            setLastMessageID(messageId, for: conversationId)
            return true
        }
        return false
    }
    
    /// 批量更新多个消息的最后ID
    /// - Parameters:
    ///   - messages: 消息列表
    ///   - conversationId: 会话ID
    /// - Returns: 最终的最后消息ID
    @discardableResult
    func updateLastMessageIDFromMessages(_ messages: [Message], for conversationId: String) -> Int {
        guard !messages.isEmpty else {
            return getLastMessageID(for: conversationId)
        }
        
        // 找到最大的消息ID
        let maxMessageId = messages.map { $0.message_id }.max() ?? 0
        updateLastMessageID(maxMessageId, for: conversationId)
        return maxMessageId
    }
    
    /// 清除指定会话的最后消息ID记录
    /// - Parameter conversationId: 会话ID
    func clearLastMessageID(for conversationId: String) {
        let key = keyPrefix + conversationId
        userDefaults.removeObject(forKey: key)
        userDefaults.synchronize()
    }
    
    /// 获取所有会话的最后消息ID记录
    /// - Returns: 会话ID到最后消息ID的映射
    func getAllLastMessageIDs() -> [String: Int] {
        var result: [String: Int] = [:]
        
        for (key, value) in userDefaults.dictionaryRepresentation() {
            if key.hasPrefix(keyPrefix), let messageId = value as? Int {
                let conversationId = String(key.dropFirst(keyPrefix.count))
                result[conversationId] = messageId
            }
        }
        
        return result
    }
    
    /// 清除所有会话的最后消息ID记录
    func clearAllLastMessageIDs() {
        let allKeys = getAllLastMessageIDs().keys
        for conversationId in allKeys {
            clearLastMessageID(for: conversationId)
        }
    }

    // MARK: - 已读消息ID管理
    private let readKeyPrefix = "read_message_id_"

    /// 获取指定会话的已读到的最后消息ID
    /// - Parameter conversationId: 会话ID
    /// - Returns: 已读到的最后消息ID，如果没有则返回0
    func getReadMessageID(for conversationId: String) -> Int {
        let key = readKeyPrefix + conversationId
        return userDefaults.integer(forKey: key)
    }

    /// 设置指定会话的已读到的最后消息ID
    /// - Parameters:
    ///   - messageId: 已读到的消息ID
    ///   - conversationId: 会话ID
    func setReadMessageID(_ messageId: Int, for conversationId: String) {
        let key = readKeyPrefix + conversationId
        userDefaults.set(messageId, forKey: key)
        userDefaults.synchronize()
    }

    /// 更新指定会话的已读消息ID（只有当新ID更大时才更新）
    /// - Parameters:
    ///   - messageId: 新的已读消息ID
    ///   - conversationId: 会话ID
    /// - Returns: 是否进行了更新
    @discardableResult
    func updateReadMessageID(_ messageId: Int, for conversationId: String) -> Bool {
        let currentId = getReadMessageID(for: conversationId)
        if messageId > currentId {
            setReadMessageID(messageId, for: conversationId)
            return true
        }
        return false
    }

    /// 获取所有会话的已读消息ID记录
    /// - Returns: 会话ID到已读消息ID的映射
    func getAllReadMessageIDs() -> [String: Int] {
        var result: [String: Int] = [:]

        for (key, value) in userDefaults.dictionaryRepresentation() {
            if key.hasPrefix(readKeyPrefix), let messageId = value as? Int {
                let conversationId = String(key.dropFirst(readKeyPrefix.count))
                result[conversationId] = messageId
            }
        }

        return result
    }

    /// 清除指定会话的已读消息ID记录
    /// - Parameter conversationId: 会话ID
    func clearReadMessageID(for conversationId: String) {
        let key = readKeyPrefix + conversationId
        userDefaults.removeObject(forKey: key)
        userDefaults.synchronize()
    }
}

// MARK: - 调试和日志功能
extension LastMessageIDManager {
    /// 打印指定会话的最后消息ID（用于调试）
    func printLastMessageID(for conversationId: String) {
        let messageId = getLastMessageID(for: conversationId)
        print("会话 \(conversationId) 的最后消息ID: \(messageId)")
    }
    
    /// 打印所有会话的最后消息ID（用于调试）
    func printAllLastMessageIDs() {
        let allIDs = getAllLastMessageIDs()
        print("所有会话的最后消息ID:")
        for (conversationId, messageId) in allIDs {
            print("  \(conversationId): \(messageId)")
        }
    }
}
