{"identifier": "B2D1D8C3", "nonRenewingSubscriptions": [], "products": [{"displayPrice": "3", "familyShareable": false, "internalID": "1000000001", "localizations": [{"description": "充值10个捏币", "displayName": "10捏币", "locale": "zh_CN"}, {"description": "Purchase 10 Nie Coins", "displayName": "10 Nie Coins", "locale": "en_US"}], "productID": "com.nienie.nn10", "referenceName": "10捏币", "type": "Consumable"}, {"displayPrice": "8", "familyShareable": false, "internalID": "1000000002", "localizations": [{"description": "充值30个捏币", "displayName": "30捏币", "locale": "zh_CN"}, {"description": "Purchase 30 Nie Coins", "displayName": "30 Nie Coins", "locale": "en_US"}], "productID": "com.nienie.nn30", "referenceName": "30捏币", "type": "Consumable"}, {"displayPrice": "12", "familyShareable": false, "internalID": "1000000003", "localizations": [{"description": "充值50个捏币", "displayName": "50捏币", "locale": "zh_CN"}, {"description": "Purchase 50 Nie Coins", "displayName": "50 Nie Coins", "locale": "en_US"}], "productID": "com.nienie.nn50", "referenceName": "50捏币", "type": "Consumable"}, {"displayPrice": "22", "familyShareable": false, "internalID": "1000000004", "localizations": [{"description": "充值100个捏币", "displayName": "100捏币", "locale": "zh_CN"}, {"description": "Purchase 100 Nie Coins", "displayName": "100 Nie Coins", "locale": "en_US"}], "productID": "com.nienie.nn100", "referenceName": "100捏币", "type": "Consumable"}, {"displayPrice": "40", "familyShareable": false, "internalID": "1000000005", "localizations": [{"description": "充值200个捏币", "displayName": "200捏币", "locale": "zh_CN"}, {"description": "Purchase 200 Nie Coins", "displayName": "200 Nie Coins", "locale": "en_US"}], "productID": "com.nienie.nn200", "referenceName": "200捏币", "type": "Consumable"}, {"displayPrice": "69", "familyShareable": false, "internalID": "1000000006", "localizations": [{"description": "充值500个捏币", "displayName": "500捏币", "locale": "zh_CN"}, {"description": "Purchase 500 Nie Coins", "displayName": "500 Nie Coins", "locale": "en_US"}], "productID": "com.nienie.nn500", "referenceName": "500捏币", "type": "Consumable"}], "settings": {"_applicationInternalID": "com.nienie.app", "_developerTeamID": "YOURTEAMID", "_lastSynchronizedDate": 706996654.019948}, "subscriptionGroups": [], "version": {"major": 2, "minor": 0}}