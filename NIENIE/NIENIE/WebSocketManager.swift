import Foundation
import Network

class WebSocketManager: ObservableObject {
    static let shared = WebSocketManager()
    
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?
    private let baseURL = "wss://sorealhuman.com:8888/ws/messages"
    
    @Published var isConnected = false
    @Published var connectionStatus = "未连接"
    
    private var userId: Int = 0
    private var reconnectTimer: Timer?
    private var heartbeatTimer: Timer?
    private var shouldReconnect = true
    
    private init() {
        setupURLSession()
    }
    
    private func setupURLSession() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60
        urlSession = URLSession(configuration: config)
    }
    
    func connect(userId: Int) {
        self.userId = userId
        self.shouldReconnect = true

        guard let url = URL(string: "\(baseURL)/\(userId)") else {
            print("无效的WebSocket URL")
            return
        }

        disconnect() // 先断开现有连接

        webSocketTask = urlSession?.webSocketTask(with: url)
        webSocketTask?.resume()

        DispatchQueue.main.async {
            self.isConnected = true
            self.connectionStatus = "连接中..."
        }

        // 开始监听消息
        receiveMessage()

        // 启动心跳
        startHeartbeat()

    }

    func forceReconnect() {
        if userId > 0 {
            connect(userId: userId)
        }
    }
    
    func disconnect() {
        shouldReconnect = false
        
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        
        stopHeartbeat()
        stopReconnectTimer()
        
        DispatchQueue.main.async {
            self.isConnected = false
            self.connectionStatus = "已断开"
        }
        
    }
    
    private func receiveMessage() {
        webSocketTask?.receive { [weak self] result in
            switch result {
            case .success(let message):
                switch message {
                case .string(let text):
                    self?.handleMessage(text)
                case .data(let data):
                    if let text = String(data: data, encoding: .utf8) {
                        self?.handleMessage(text)
                    }
                @unknown default:
                    break
                }
                
                // 继续监听下一条消息
                self?.receiveMessage()
                
            case .failure(let error):
                print("WebSocket接收消息失败: \(error)")
                self?.handleConnectionError()
            }
        }
    }
    
    private func handleMessage(_ text: String) {
        
        guard let data = text.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let type = json["type"] as? String else {
            return
        }
        
        DispatchQueue.main.async {
            switch type {
            case "new_message":
                self.handleNewMessage(json)
            case "status_updated":
                self.connectionStatus = "已连接"
            case "pong":
                // 心跳响应
                break
            case "error":
                if let message = json["message"] as? String {
                    print("WebSocket错误: \(message)")
                }
            default:
                print("未知消息类型: \(type)")
            }
        }
    }
    
    private func handleNewMessage(_ json: [String: Any]) {
        guard let messageData = json["data"] as? [String: Any] else {
            print("无效的消息数据")
            return
        }
        
        // 发送本地通知，通知UI更新
        NotificationCenter.default.post(
            name: NSNotification.Name("NewMessageReceived"),
            object: nil,
            userInfo: messageData
        )
    }
    
    private func handleConnectionError() {
        DispatchQueue.main.async {
            self.isConnected = false
            self.connectionStatus = "连接失败"
        }
        
        // 如果需要重连，启动重连定时器
        if shouldReconnect {
            startReconnectTimer()
        }
    }
    
    private func startReconnectTimer() {
        stopReconnectTimer()
        
        reconnectTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: false) { [weak self] _ in
            guard let self = self, self.shouldReconnect else { return }
            self.connect(userId: self.userId)
        }
    }
    
    private func stopReconnectTimer() {
        reconnectTimer?.invalidate()
        reconnectTimer = nil
    }
    
    private func startHeartbeat() {
        stopHeartbeat()
        
        heartbeatTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.sendHeartbeat()
        }
    }
    
    private func stopHeartbeat() {
        heartbeatTimer?.invalidate()
        heartbeatTimer = nil
    }
    
    private func sendHeartbeat() {
        let heartbeat: [String: Any] = [
            "type": "ping",
            "timestamp": Int(Date().timeIntervalSince1970)
        ]

        sendMessage(heartbeat)
    }
    
    func updatePageStatus(page: String, conversationId: String = "") {
        let statusMessage: [String: Any] = [
            "type": "page_status",
            "page": page,
            "conversation_id": conversationId
        ]

        sendMessage(statusMessage)
    }
    
    private func sendMessage(_ message: [String: Any]) {
        guard let data = try? JSONSerialization.data(withJSONObject: message),
              let text = String(data: data, encoding: .utf8) else {
            return
        }
        
        webSocketTask?.send(.string(text)) { error in
            if let error = error {
                print("发送WebSocket消息失败: \(error)")
            }
        }
    }
    
    deinit {
        disconnect()
    }
}
