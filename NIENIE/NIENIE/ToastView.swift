import SwiftUI

struct ToastView: View {
    let message: String
    @Binding var isShowing: Bool
    let duration: Double
    
    var body: some View {
        VStack {
            Text(message)
                .font(Font.custom("PingFang SC", size: 14))
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.black.opacity(0.7))
                )
                .padding(.bottom, 50)
        }
        .transition(.move(edge: .bottom).combined(with: .opacity))
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
                withAnimation {
                    isShowing = false
                }
            }
        }
    }
}

// 可以在任何视图中使用的Toast修饰器
struct ToastModifier: ViewModifier {
    @Binding var isShowing: Bool
    let message: String
    let duration: Double
    
    func body(content: Content) -> some View {
        ZStack {
            content
            
            if isShowing {
                VStack {
                    Spacer()
                    ToastView(message: message, isShowing: $isShowing, duration: duration)
                }
                .transition(.opacity)
                .animation(.easeInOut, value: isShowing)
                .zIndex(999)
            }
        }
    }
}

extension View {
    func toast(isShowing: Binding<Bool>, message: String, duration: Double = 2.0) -> some View {
        self.modifier(ToastModifier(isShowing: isShowing, message: message, duration: duration))
    }
} 