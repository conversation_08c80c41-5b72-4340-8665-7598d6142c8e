import SwiftUI

// 反馈类型枚举
enum FeedbackType: String, CaseIterable {
    case dislikePost = "不喜欢该笔记"
    case advertisement = "广告"
    case tooSimilar = "相似过多"
    case adultContent = "色情低俗"
    case inappropriateContent = "内容不适"
    case clickbait = "夸张博眼球"
    
    // 获取对应的后端反馈类型
    var apiValue: String {
        switch self {
        case .dislikePost:
            return "" // 本地处理，不发送到后端
        case .advertisement:
            return "广告"
        case .tooSimilar:
            return "相似过多"
        case .adultContent:
            return "色情低俗"
        case .inappropriateContent:
            return "内容不适"
        case .clickbait:
            return "夸张博眼球"
        }
    }
    
    // 获取图标名称
    var iconName: String {
        switch self {
        case .dislikePost:
            return "心碎" // 使用心碎图标
        case .advertisement:
            return "广告" // 使用广告图标
        case .tooSimilar:
            return "相似" // 使用相似图标
        case .adultContent:
            return "色情" // 使用色情图标
        case .inappropriateContent:
            return "不适" // 使用不适图标
        case .clickbait:
            return "博眼球" // 使用博眼球图标
        }
    }
    
    // 是否属于不感兴趣类别
    var isDislikeCategory: Bool {
        return self == .dislikePost
    }
    
    // 是否属于内容反馈类别
    var isContentFeedback: Bool {
        return !isDislikeCategory
    }
}

struct FeedbackMenuView: View {
    let image: ImageData
    @Binding var isPresented: Bool
    @EnvironmentObject var userState: UserState
    var onRemove: () -> Void
    @State private var showToast = false
    @State private var toastMessage = ""
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.4)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    isPresented = false
                }
            
            VStack(spacing: 0) {
                // 标题区域 - 不感兴趣
                Text("不感兴趣")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 10)
                
                // 不感兴趣选项
                HStack(spacing: 0) {
                    Button(action: {
                        handleFeedback(.dislikePost)
                    }) {
                        VStack {
                            Text("不喜欢该笔记")
                                .font(.system(size: 14))
                                .foregroundColor(.black)
                                .padding(.horizontal, 10)
                                .multilineTextAlignment(.center)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 15)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(10)
                    }
                    .padding(.horizontal, 10)
                    
                    // 空白占位
                    Spacer()
                        .frame(maxWidth: .infinity)
                        .padding(.horizontal, 10)
                }
                .padding(.horizontal, 10)
                .padding(.bottom, 20)
                
                // 标题区域 - 内容反馈
                Text("内容反馈")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
                    .padding(.bottom, 10)
                
                // 内容反馈选项 - 第一行
                HStack(spacing: 0) {
                    Button(action: {
                        handleFeedback(.advertisement)
                    }) {
                        VStack {
                            Text("广告")
                                .font(.system(size: 14))
                                .foregroundColor(.black)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 15)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(10)
                    }
                    .padding(.horizontal, 10)
                    
                    Button(action: {
                        handleFeedback(.tooSimilar)
                    }) {
                        VStack {
                            Text("相似过多")
                                .font(.system(size: 14))
                                .foregroundColor(.black)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 15)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(10)
                    }
                    .padding(.horizontal, 10)
                }
                .padding(.horizontal, 10)
                .padding(.bottom, 10)
                
                // 内容反馈选项 - 第二行
                HStack(spacing: 0) {
                    Button(action: {
                        handleFeedback(.adultContent)
                    }) {
                        VStack {
                            Text("色情低俗")
                                .font(.system(size: 14))
                                .foregroundColor(.black)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 15)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(10)
                    }
                    .padding(.horizontal, 10)
                    
                    Button(action: {
                        handleFeedback(.inappropriateContent)
                    }) {
                        VStack {
                            Text("内容不适")
                                .font(.system(size: 14))
                                .foregroundColor(.black)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 15)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(10)
                    }
                    .padding(.horizontal, 10)
                }
                .padding(.horizontal, 10)
                .padding(.bottom, 10)
                
                // 内容反馈选项 - 第三行
                HStack(spacing: 0) {
                    Button(action: {
                        handleFeedback(.clickbait)
                    }) {
                        VStack {
                            Text("夸张博眼球")
                                .font(.system(size: 14))
                                .foregroundColor(.black)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 15)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(10)
                    }
                    .padding(.horizontal, 10)
                    
                    // 空白占位
                    Spacer()
                        .frame(maxWidth: .infinity)
                        .padding(.horizontal, 10)
                }
                .padding(.horizontal, 10)
                .padding(.bottom, 20)
            }
            .background(Color.white)
            .cornerRadius(15)
            .padding(.horizontal, 20)
            
            // 显示Toast提示
            if showToast {
                VStack {
                    Spacer()
                    Text(toastMessage)
                        .foregroundColor(.white)
                        .padding(.vertical, 10)
                        .padding(.horizontal, 20)
                        .background(Color.black.opacity(0.7))
                        .cornerRadius(20)
                        .padding(.bottom, 100)
                }
                .transition(.opacity)
                .zIndex(100)
            }
        }
    }
    
    // 处理反馈
    private func handleFeedback(_ type: FeedbackType) {
        switch type {
        case .dislikePost:
            // 本地存储不喜欢的图片
            userState.addDislikedImage(imageId: image.imageId)
            showToast(message: "反馈成功，将为您优化结果")
            
        case .advertisement, .tooSimilar, .adultContent, .inappropriateContent, .clickbait:
            // 发送内容反馈到后端
            APIService.shared.submitFeedback(
                userId: userState.userId,
                imageId: image.imageId,
                feedbackType: type.apiValue,
                imagePath: image.storagePath
            ) { result in
                DispatchQueue.main.async {
                    switch result {
                    case .success:
                        showToast(message: "反馈成功，将为您优化结果")
                    case .failure:
                        showToast(message: "反馈失败，请稍后再试")
                    }
                }
            }
        }
    }
    
    // 显示Toast提示
    private func showToast(message: String) {
        toastMessage = message
        withAnimation {
            showToast = true
        }
        
        // 1秒后关闭Toast并移除卡片
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            withAnimation {
                showToast = false
            }
            
            // 关闭菜单并移除卡片
            isPresented = false
            onRemove()
        }
    }
} 