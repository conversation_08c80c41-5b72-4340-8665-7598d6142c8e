import Foundation
import UIKit
// 用户数据模型
struct UserData {
    let userId: Int
    let username: String
    let avatar: String
    let token: String
    var balance: Int? = nil
}

// 图片数据模型
struct ImageData: Identifiable {
    let id = UUID()
    let imageId: Int
    let userId: Int
    let username: String
    let avatar: String
    let storagePath: String
    var likeCount: Int
    let createdAt: String
    var liked: Bool
    let title: String?
    let content: String?
    let auditStatus: Int?  // 审核状态：0-审核中，1-通过，2-不通过
    
    // 获取图片URL
    var imageURL: URL {
        APIService.shared.getImageURL(filename: storagePath)
    }
    
    // 默认初始化方法，liked默认为false
    init(imageId: Int, userId: Int, username: String, avatar: String, storagePath: String, likeCount: Int, createdAt: String, liked: Bool = false, title: String? = nil, content: String? = nil, auditStatus: Int? = nil) {
        self.imageId = imageId
        self.userId = userId
        self.username = username
        self.avatar = avatar
        self.storagePath = storagePath
        self.likeCount = likeCount
        self.createdAt = createdAt
        self.liked = liked
        self.title = title
        self.content = content
        self.auditStatus = auditStatus
    }
}

// 评论数据模型 - 修改为 Class, 遵循 ObservableObject
class CommentData: ObservableObject, Identifiable, Equatable {
    let commentId: Int
    let imageId: Int
    let userId: Int
    let username: String
    let avatar: String
    let parentCommentId: Int?
    let content: String
    let createdAt: String
    @Published var replies: [CommentData]?
    
    // Identifiable 协议要求
    var id: Int { commentId }
    
    // 初始化方法
    init(commentId: Int, imageId: Int, userId: Int, username: String, avatar: String, parentCommentId: Int?, content: String, createdAt: String, replies: [CommentData]? = nil) {
        self.commentId = commentId
        self.imageId = imageId
        self.userId = userId
        self.username = username
        self.avatar = avatar
        self.parentCommentId = parentCommentId
        self.content = content
        self.createdAt = createdAt
        self.replies = replies
    }
    
    // 实现Equatable协议，基于commentId比较
    static func == (lhs: CommentData, rhs: CommentData) -> Bool {
        lhs.commentId == rhs.commentId
    }
}

// 模板数据模型
struct TemplateData: Identifiable {
    let id = UUID()
    let filename: String
    let path: String

    // 获取低分辨率模板图片URL
    var lowResURL: URL {
        APIService.shared.getTemplateLowResURL(filename: filename)
    }

    // 获取高分辨率模板图片URL
    var highResURL: URL {
        APIService.shared.getTemplateHighResURL(filename: filename)
    }
}

// 广告图片数据模型
struct AdImageData: Identifiable {
    let id = UUID()
    let filename: String

    // 获取广告图片URL
    var imageURL: URL {
        APIService.shared.getRecommendImageURL(filename: filename)
    }
}

class APIService {
    static let shared = APIService()
    let baseURL = "https://sorealhuman.com:8888"
    
    // 私有初始化方法，确保单例模式
    private init() {}
    
    // 发送验证码
    func sendVerificationCode(phone: String, completion: @escaping (Result<Bool, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/auth/send_code")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let body: [String: Any] = ["phone": phone]
        request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let isSuccess = json["success"] as? Bool {
                    completion(.success(isSuccess))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "解析响应失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 验证码验证
    func verifyCode(phone: String, code: String, completion: @escaping (Result<Bool, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/auth/verify_code")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let body: [String: Any] = [
            "phone": phone,
            "code": code
        ]
        request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let isSuccess = json["success"] as? Bool {
                    completion(.success(isSuccess))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "验证码错误，请重新输入"])))
                }
            } catch {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "验证码错误，请重新输入"])))
            }
        }.resume()
    }
    
    // 用户注册
    func register(phone: String, username: String, avatar: String, completion: @escaping (Result<UserData, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/auth/register")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let body: [String: Any] = [
            "phone": phone,
            "username": username,
            "avatar": avatar
        ]
        request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    if let success = json["success"] as? Bool, success {
                        if let userId = json["user_id"] as? Int,
                           let username = json["username"] as? String,
                           let token = json["token"] as? String {
                            
                            var userData = UserData(userId: userId, username: username, avatar: avatar, token: token)
                            userData.balance = json["balance"] as? Int
                                
                            
                            completion(.success(userData))
                        } else {
                            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "解析响应失败"])))
                        }
                    } else {
                        // 检查是否有错误消息
                        if let message = json["message"] as? String {
                            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: message])))
                        } else if let detail = json["detail"] as? String {
                            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: detail])))
                        } else {
                            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "注册失败"])))
                        }
                    }
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "解析响应失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 用户登录
    func login(phone: String, completion: @escaping (Result<UserData, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/auth/login")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let body: [String: Any] = ["phone": phone]
        request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let _ = json["success"] as? Bool,
                   let userId = json["user_id"] as? Int,
                   let username = json["username"] as? String,
                   let avatar = json["avatar"] as? String,
                   let token = json["token"] as? String {
                    
                    var userData = UserData(userId: userId, username: username, avatar: avatar, token: token)
                    
                   
                    userData.balance = json["balance"] as? Int
                        
                   
                    
                    completion(.success(userData))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "用户不存在"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 注销账户
    func deleteAccount(userId: Int, completion: @escaping (Result<Bool, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/auth/delete_account")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let body: [String: Any] = ["user_id": userId]
        request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let success = json["success"] as? Bool {
                    completion(.success(success))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "解析响应失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 验证令牌
    func validateToken(userId: Int, token: String, completion: @escaping (Result<UserData, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/auth/validate_token")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let body: [String: Any] = [
            "user_id": userId,
            "token": token
        ]
        request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let _ = json["valid"] as? Bool,
                   let userId = json["user_id"] as? Int,
                   let username = json["username"] as? String {
                    
                    // 获取头像和余额（如果有）
                    let avatar = json["avatar"] as? String ?? ""
                    
                    // 创建UserData对象
                    var userData = UserData(userId: userId, username: username, avatar: avatar, token: token)
                    userData.balance = json["balance"] as? Int
                        
                    
                    completion(.success(userData))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "令牌无效"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 获取用户余额
    func getUserBalance(userId: Int, completion: @escaping (Result<Int, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/users/\(userId)/balance")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        

        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
              
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
             
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                let jsonObject = try JSONSerialization.jsonObject(with: data)
             
                
                if let json = jsonObject as? [String: Any],
                   let success = json["success"] as? Bool, success,
                  
                  
                   let balance = json["balance"] as? Int {
            
                    completion(.success(balance))
                } else {
             
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "获取余额失败"])))
                }
            } catch {
            
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 获取首页图片
    func getHomeImages(page: Int = 1, sortBy: String = "rank", categoryId: Int = -1, completion: @escaping (Result<[ImageData], Error>) -> Void) {
        let url = URL(string: "\(baseURL)/images/home?page=\(page)&user_id=\(UserState.shared.userId)&sort_by=\(sortBy)&category_id=\(categoryId)")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let _ = json["success"] as? Bool,
                   let imagesData = json["images"] as? [[String: Any]] {
                    
                    var images: [ImageData] = []
                    for imageJson in imagesData {
                        if let imageId = imageJson["image_id"] as? Int,
                           let userId = imageJson["user_id"] as? Int,
                           let username = imageJson["username"] as? String,
                           let avatar = imageJson["avatar"] as? String,
                           let storagePath = imageJson["storage_path"] as? String,
                           let likeCount = imageJson["like_count"] as? Int,
                           let createdAt = imageJson["created_at"] as? String {
                            
                            let liked = imageJson["liked"] as? Bool ?? false
                            let title = imageJson["title"] as? String
                            let content = imageJson["content"] as? String
                            let auditStatus = imageJson["audit_status"] as? Int
                            
                            let image = ImageData(imageId: imageId, userId: userId, username: username, avatar: avatar, storagePath: storagePath, likeCount: likeCount, createdAt: createdAt, liked: liked, title: title, content: content, auditStatus: auditStatus)
                            images.append(image)
                        }
                    }
                    
                    completion(.success(images))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "获取图片失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 获取用户上传的图片
    func getUserImages(userId: Int, skip: Int = 0, limit: Int = 20, completion: @escaping (Result<[ImageData], Error>) -> Void) {
        let url = URL(string: "\(baseURL)/users/\(userId)/images?skip=\(skip)&limit=\(limit)")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let _ = json["success"] as? Bool,
                   let imagesData = json["images"] as? [[String: Any]] {
                    
                    var images: [ImageData] = []
                    for imageJson in imagesData {
                        if let imageId = imageJson["image_id"] as? Int,
                           let userId = imageJson["user_id"] as? Int,
                           let username = imageJson["username"] as? String,
                           let avatar = imageJson["avatar"] as? String,
                           let storagePath = imageJson["storage_path"] as? String,
                           let likeCount = imageJson["like_count"] as? Int,
                           let createdAt = imageJson["created_at"] as? String {
                            
                            let liked = imageJson["liked"] as? Bool ?? false
                            let title = imageJson["title"] as? String
                            let content = imageJson["content"] as? String
                            let auditStatus = imageJson["audit_status"] as? Int
                            
                            let image = ImageData(imageId: imageId, userId: userId, username: username, avatar: avatar, storagePath: storagePath, likeCount: likeCount, createdAt: createdAt, liked: liked, title: title, content: content, auditStatus: auditStatus)
                            images.append(image)
                        }
                    }
                    
                    completion(.success(images))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "获取图片失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 获取图片文件URL
    func getImageURL(filename: String, lowResolution: Bool = false, isForeground: Bool = false) -> URL {
        if lowResolution {
            return URL(string: "\(baseURL)/images/file/low/\(filename)")!
        } else if isForeground {
            // 前景图实际上是背景图
            // 检查文件名是否已经包含bg路径
            if filename.hasPrefix("bg/") {
                return URL(string: "\(baseURL)/images/file/\(filename)")!
            } else {
                return URL(string: "\(baseURL)/images/file/bg/\(filename)")!
            }
        } else {
            // 普通图片是output目录下的图片
            // 检查文件名是否已经包含output路径
            if filename.hasPrefix("output/") {
                return URL(string: "\(baseURL)/images/file/\(filename)")!
            } else {
                return URL(string: "\(baseURL)/images/file/output/\(filename)")!
            }
        }
    }
    
    // 点赞或取消点赞图片
    func likeImage(imageId: Int, userId: Int, completion: @escaping (Result<(Int, Bool), Error>) -> Void) {
        
        
        let url = URL(string: "\(baseURL)/images/\(imageId)/like?user_id=\(userId)")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    
                    if let _ = json["success"] as? Bool,
                       let likeCount = json["like_count"] as? Int {
                        
                        
                        
                        let liked = json["liked"] as? Bool ?? false
                        completion(.success((likeCount, liked)))
                    } else {
                        completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "点赞操作失败"])))
                    }
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "点赞操作失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 删除图片
    func deleteImage(imageId: Int, userId: Int, completion: @escaping (Result<Bool, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/images/\(imageId)?user_id=\(userId)")!
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let success = json["success"] as? Bool {
                    completion(.success(success))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "删除图片失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 获取图片详情
    func getImageDetail(imageId: Int, userId: Int? = nil, completion: @escaping (Result<(ImageData, String), Error>) -> Void) {
        var urlString = "\(baseURL)/images/\(imageId)"
        if let userId = userId {
            urlString += "?user_id=\(userId)"
        }
        let url = URL(string: urlString)!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let _ = json["success"] as? Bool,
                   let imageJson = json["image"] as? [String: Any],
                   let foregroundJson = json["foreground"] as? [String: Any],
                   let imageId = imageJson["image_id"] as? Int,
                   let userId = imageJson["user_id"] as? Int,
                   let username = imageJson["username"] as? String,
                   let avatar = imageJson["avatar"] as? String,
                   let storagePath = imageJson["storage_path"] as? String,
                   let likeCount = imageJson["like_count"] as? Int,
                   let createdAt = imageJson["created_at"] as? String,
                   let foregroundPath = foregroundJson["storage_path"] as? String {
                    
                    let liked = imageJson["liked"] as? Bool ?? false
                    let title = imageJson["title"] as? String
                    let content = imageJson["content"] as? String
                    let auditStatus = imageJson["audit_status"] as? Int
                    
                    let image = ImageData(imageId: imageId, userId: userId, username: username, avatar: avatar, storagePath: storagePath, likeCount: likeCount, createdAt: createdAt, liked: liked, title: title, content: content, auditStatus: auditStatus)
                    completion(.success((image, foregroundPath)))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "获取图片详情失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 获取用户喜欢的图片
    func getUserLikedImages(userId: Int, skip: Int = 0, limit: Int = 20, completion: @escaping (Result<[ImageData], Error>) -> Void) {
        let url = URL(string: "\(baseURL)/users/\(userId)/liked_images?skip=\(skip)&limit=\(limit)")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let _ = json["success"] as? Bool,
                   let imagesData = json["images"] as? [[String: Any]] {
                    
                    var images: [ImageData] = []
                    for imageJson in imagesData {
                        if let imageId = imageJson["image_id"] as? Int,
                           let userId = imageJson["user_id"] as? Int,
                           let username = imageJson["username"] as? String,
                           let avatar = imageJson["avatar"] as? String,
                           let storagePath = imageJson["storage_path"] as? String,
                           let likeCount = imageJson["like_count"] as? Int,
                           let createdAt = imageJson["created_at"] as? String {
                            
                            let liked = imageJson["liked"] as? Bool ?? true  // 默认为已点赞
                            let title = imageJson["title"] as? String
                            let content = imageJson["content"] as? String
                            let auditStatus = imageJson["audit_status"] as? Int
                            
                            let image = ImageData(imageId: imageId, userId: userId, username: username, avatar: avatar, storagePath: storagePath, likeCount: likeCount, createdAt: createdAt, liked: liked, title: title, content: content, auditStatus: auditStatus)
                            images.append(image)
                        }
                    }
                    
                    completion(.success(images))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "获取喜欢的图片失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 提交图像处理任务
    func submitImageProcessingTask(userId: Int, foregroundImage: UIImage, backgroundImage: UIImage, X: Int, Y: Int, Width: Int, Height: Int, Angle: Int, completion: @escaping (Result<String, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/images/submit_task")!
        
        // 创建multipart/form-data请求
        let boundary = UUID().uuidString
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        // 转换图片数据 - 前景图使用JPEG，背景图(可能是模糊的)使用PNG以保留模糊效果
        guard let fgImageData = foregroundImage.jpegData(compressionQuality: 1.0),
              let bgImageData = backgroundImage.heicData() else {
            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "图片转换失败"])))
            return
        }
        // 转换图片数据 - 前景图使用JPEG，背景图使用WebP以保留模糊效果并减小体积
        // guard let fgImageData = foregroundImage.jpegData(compressionQuality: 1.0),
        //       let bgImageData = SDImageWebPCoder.shared.encodedData(with: backgroundImage, format: .webP, options: nil) else {
        //     completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "图片转换失败"])))
        //     return
        // }
        // 创建表单数据
        var body = Data()
        
        // 添加用户ID
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"user_id\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(userId)\r\n".data(using: .utf8)!)
        
        // 添加前景图
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"fg_image\"; filename=\"foreground.jpg\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: image/jpeg\r\n\r\n".data(using: .utf8)!)
        body.append(fgImageData)
        body.append("\r\n".data(using: .utf8)!)
        
        // 添加背景图 - 使用PNG格式保留模糊效果
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"bg_image\"; filename=\"background.heic\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: image/heic\r\n\r\n".data(using: .utf8)!)
        body.append(bgImageData)
        body.append("\r\n".data(using: .utf8)!)
        
        // 添加坐标X
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"X\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(X)\r\n".data(using: .utf8)!)
        
        // 添加坐标Y
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"Y\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(Y)\r\n".data(using: .utf8)!)
        
        // 添加宽度
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"Width\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(Width)\r\n".data(using: .utf8)!)
        
        // 添加高度
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"Height\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(Height)\r\n".data(using: .utf8)!)
        
        // 添加旋转角度
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"Angle\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(Angle)\r\n".data(using: .utf8)!)
        
        // 结束边界
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
        
        // 设置请求体
        request.httpBody = body
        
        // 发送请求
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let _ = json["success"] as? Bool,
                   let taskId = json["task_id"] as? String {
                    completion(.success(taskId))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "解析响应失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 获取任务状态
    func getTaskStatus(taskId: String, completion: @escaping (Result<(String, String, String), Error>) -> Void) {
        let url = URL(string: "\(baseURL)/images/task_status/\(taskId)")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let _ = json["success"] as? Bool,
                   let status = json["status"] as? String {
                    
                    if status == "completed" {
                        if let outputPath = json["output_path"] as? String,
                           let bgPath = json["bg_path"] as? String {
                            completion(.success((status, outputPath, bgPath)))
                        } else {
                            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "输出路径解析失败"])))
                        }
                    } else {
                        completion(.success((status, "", "")))
                    }
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "解析响应失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 保存图片到数据库
    func saveImageToDatabase(userId: Int, taskId: String, saveToPublic: Bool, title: String? = nil, content: String? = nil, categoryId: Int = 4, enhancedImage: UIImage? = nil, completion: @escaping (Result<(Int, Int), Error>) -> Void) {
        let url = URL(string: "\(baseURL)/images/save_image")!
        
        // 创建multipart/form-data请求
        let boundary = UUID().uuidString
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        var body = Data()
        
        // 添加用户ID
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"user_id\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(userId)\r\n".data(using: .utf8)!)
        
        // 添加任务ID
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"task_id\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(taskId)\r\n".data(using: .utf8)!)
        
        // 添加是否保存到公共数据库
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"save_to_public\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(saveToPublic)\r\n".data(using: .utf8)!)
        
        // 添加标题（如果提供）
        if let title = title {
            body.append("--\(boundary)\r\n".data(using: .utf8)!)
            body.append("Content-Disposition: form-data; name=\"title\"\r\n\r\n".data(using: .utf8)!)
            body.append("\(title)\r\n".data(using: .utf8)!)
        }
        
        // 添加内容（如果提供）
        if let content = content {
            body.append("--\(boundary)\r\n".data(using: .utf8)!)
            body.append("Content-Disposition: form-data; name=\"content\"\r\n\r\n".data(using: .utf8)!)
            body.append("\(content)\r\n".data(using: .utf8)!)
        }

        // 添加类别ID
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"category_id\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(categoryId)\r\n".data(using: .utf8)!)

        // 添加图片（如果提供）
        if let enhancedImage = enhancedImage {
            if let imageData = enhancedImage.jpegData(compressionQuality: 0.9) {
                body.append("--\(boundary)\r\n".data(using: .utf8)!)
                body.append("Content-Disposition: form-data; name=\"image\"; filename=\"enhanced_image.jpg\"\r\n".data(using: .utf8)!)
                body.append("Content-Type: image/jpeg\r\n\r\n".data(using: .utf8)!)
                body.append(imageData)
                body.append("\r\n".data(using: .utf8)!)
            }
        }
        
        // 结束边界
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
        
        request.httpBody = body
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let _ = json["success"] as? Bool,
                   let imageId = json["image_id"] as? Int,
                   let reward = json["reward"] as? Int {
                    completion(.success((imageId, reward)))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "解析响应失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 获取图片评论
    func getImageComments(imageId: Int, userId: Int? = nil, completion: @escaping (Result<[CommentData], Error>) -> Void) {
        var urlString = "\(baseURL)/images/\(imageId)/comments"
        if let userId = userId {
            urlString += "?user_id=\(userId)"
        }
        
        guard let url = URL(string: urlString) else {
            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let success = json["success"] as? Bool, success,
                   let commentsData = json["comments"] as? [[String: Any]] {
                    
                    var comments: [CommentData] = []
                    
                    for commentJson in commentsData {
                        if let comment = self.parseCommentData(commentJson) {
                            comments.append(comment)
                        }
                    }
                    
                    completion(.success(comments))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "获取评论失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 添加评论
    func addComment(imageId: Int, userId: Int, content: String, parentCommentId: Int? = nil, completion: @escaping (Result<CommentData, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/images/\(imageId)/comments")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        var body: [String: Any] = [
            "user_id": userId,
            "content": content
        ]
        
        if let parentCommentId = parentCommentId {
            body["parent_comment_id"] = parentCommentId
        }
        
        request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let success = json["success"] as? Bool, success,
                   let commentJson = json["comment"] as? [String: Any],
                   let comment = self.parseCommentData(commentJson) {
                    
                    completion(.success(comment))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "添加评论失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 删除评论
    func deleteComment(commentId: Int, userId: Int, completion: @escaping (Result<Bool, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/images/comments/\(commentId)?user_id=\(userId)")!
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let success = json["success"] as? Bool {
                    
                    completion(.success(success))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "删除评论失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 解析评论数据
    private func parseCommentData(_ json: [String: Any]) -> CommentData? {
        guard let commentId = json["comment_id"] as? Int,
              let imageId = json["image_id"] as? Int,
              let userId = json["user_id"] as? Int,
              let username = json["username"] as? String,
              let avatar = json["avatar"] as? String,
              let content = json["content"] as? String,
              let createdAt = json["created_at"] as? String else {
            return nil
        }
        
        let parentCommentId = json["parent_comment_id"] as? Int
        
        var replies: [CommentData]? = nil
        
        // 解析回复
        if let repliesJson = json["replies"] as? [[String: Any]] {
            replies = []
            for replyJson in repliesJson {
                if let reply = parseCommentData(replyJson) {
                    replies?.append(reply)
                }
            }
        }
        
        return CommentData(
            commentId: commentId,
            imageId: imageId,
            userId: userId,
            username: username,
            avatar: avatar,
            parentCommentId: parentCommentId,
            content: content,
            createdAt: createdAt,
            replies: replies
        )
    }
    
    // 从URL加载图像数据
    func getImageData(from urlPath: String, completion: @escaping (Result<UIImage, Error>) -> Void) {
        // 判断是否是前景图（背景图路径）
        let isForeground = urlPath.contains("bg/")
        
        // 获取文件名
        let filename = URL(string: urlPath)?.lastPathComponent ?? urlPath
        
        // 使用现有的getImageURL函数构建完整URL
        let url = getImageURL(filename: filename, isForeground: isForeground)
        
        // 创建URL请求
        let request = URLRequest(url: url)
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            // 处理网络错误
            if let error = error {
                completion(.failure(error))
                return
            }
            
            // 检查是否有数据返回
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有图像数据"])))
                return
            }
            
            // 尝试转换为UIImage
            if let image = UIImage(data: data) {
                completion(.success(image))
            } else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法转换为图像"])))
            }
        }.resume()
    }
    
    // 获取模板图片URL（低分辨率）
    func getTemplateLowResURL(filename: String) -> URL {
        return URL(string: "\(baseURL)/images/file/template/low/\(filename)")!
    }
    
    // 获取模板图片URL（高分辨率）
    func getTemplateHighResURL(filename: String) -> URL {
        return URL(string: "\(baseURL)/images/file/template/high/\(filename)")!
    }
    
    // 获取模板图片列表
    func getTemplateImages(page: Int, limit: Int = 20, completion: @escaping (Result<[TemplateData], Error>) -> Void) {
        let url = URL(string: "\(baseURL)/images/templates?page=\(page)&limit=\(limit)")!
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let success = json["success"] as? Bool, success,
                   let templatesJson = json["templates"] as? [[String: Any]] {
                    
                    var templates: [TemplateData] = []
                    
                    for templateJson in templatesJson {
                        if let filename = templateJson["filename"] as? String,
                           let path = templateJson["path"] as? String {
                            
                            let template = TemplateData(filename: filename, path: path)
                            templates.append(template)
                        }
                    }
                    
                    completion(.success(templates))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "解析模板列表失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 获取模板图片数据
    func getTemplateImageData(from url: URL, completion: @escaping (Result<UIImage, Error>) -> Void) {
        URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有图片数据"])))
                return
            }
            
            if let image = UIImage(data: data) {
                completion(.success(image))
            } else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法创建图片"])))
            }
        }.resume()
    }
    
    // 验证App内购买并更新用户余额
    func verifyPurchase(userId: Int, productId: String, receiptData: String, completion: @escaping (Result<(Int, Int), Error>) -> Void) {
        let url = URL(string: "\(baseURL)/users/verify_purchase")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 构建请求体
        var body: [String: Any] = [
            "user_id": userId,
            "product_id": productId,
            "receipt_data": receiptData,
            "is_sandbox": true // 始终标记为沙盒环境，让后端自动检测
        ]
        
        // 尝试解析receiptData是否为JSON字符串
        if let jsonData = receiptData.data(using: .utf8),
           let jsonObject = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any] {
            // 如果是JSON字符串，将其内容合并到body中
            for (key, value) in jsonObject {
                body[key] = value
            }
        }
        
        request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        
        
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    
                    if let success = json["success"] as? Bool, success,
                       let balance = json["balance"] as? Int,
                       let coinAmount = json["coin_amount"] as? Int {
                        
                        completion(.success((balance, coinAmount)))
                    } else {
                        if let message = json["message"] as? String {
                            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: message])))
                        } else {
                            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "验证购买失败"])))
                        }
                    }
                } else {
                 
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "验证购买失败: 无法解析响应"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 提交内容反馈
    func submitFeedback(userId: Int, imageId: Int, feedbackType: String, imagePath: String? = nil, completion: @escaping (Result<Bool, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/auth/feedback")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        var body: [String: Any] = [
            "user_id": userId,
            "image_id": imageId,
            "feedback_type": feedbackType
        ]

        // 只有当imagePath不为nil且不为空时才添加到body中
        if let imagePath = imagePath, !imagePath.isEmpty {
            body["image_path"] = imagePath
        }
        
        request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let success = json["success"] as? Bool {
                    completion(.success(success))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "反馈提交失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 添加获取审核中图片的函数
    func getPendingAuditImages(userId: Int, completion: @escaping (Result<[String: Any], Error>) -> Void) {
        let urlString = "\(baseURL)/images/audit/pending/\(userId)"

        guard let url = URL(string: urlString) else {
            completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }

        URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }

            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }

            do {
                if let jsonResult = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                    completion(.success(jsonResult))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "解析审核中图片失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }

    // 获取推荐广告图片URL
    func getRecommendImageURL(filename: String) -> URL {
        return URL(string: "\(baseURL)/images/file/recommend/\(filename)")!
    }

    // 获取推荐广告图片列表
    func getRecommendImages(completion: @escaping (Result<[AdImageData], Error>) -> Void) {
        let url = URL(string: "\(baseURL)/images/recommend")!

        URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }

            guard let data = data else {
                completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有数据"])))
                return
            }

            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let success = json["success"] as? Bool,
                   success,
                   let imageFilenames = json["images"] as? [String] {

                    let adImages = imageFilenames.map { filename in
                        AdImageData(filename: filename)
                    }

                    completion(.success(adImages))
                } else {
                    completion(.failure(NSError(domain: "APIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "获取推荐图片失败"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
}

