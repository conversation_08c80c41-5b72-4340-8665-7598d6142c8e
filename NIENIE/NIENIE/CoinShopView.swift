import SwiftUI
import StoreKit

struct CoinShopView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userState: UserState
    @StateObject private var storeManager = StoreKitManager.shared
    
    // 购买选项
    let options = [
        (amount: 10, price: "¥3"),
        (amount: 30, price: "¥8"),
        (amount: 50, price: "¥12"),
        (amount: 100, price: "¥22"),
        (amount: 200, price: "¥40"),
        (amount: 500, price: "¥69")
    ]
    
    // 计算每行的索引
    private var firstRow: [Int] {
        return [0, 1, 2]
    }
    
    private var secondRow: [Int] {
        return [3, 4, 5]
    }
    
    @State private var selectedOption: Int? = nil
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    @State private var isLoading = false
    @State private var isProductsLoaded = false
    @State private var productPrices: [Int: String] = [:]
    
    // 检测是否是iPad
    private var isIPad: Bool {
        return UIDevice.current.userInterfaceIdiom == .pad
    }
    
    // 计算内容最大宽度
    private var contentMaxWidth: CGFloat {
        return isIPad ? 600 : UIScreen.main.bounds.width
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景颜色
                Color(red: 1.0, green: 0.98, blue: 0.77)
                    .ignoresSafeArea()
                
                // 使用ScrollView确保在小屏幕设备上可以滚动
                ScrollView {
                    // 使用VStack包裹所有内容，并居中
                    VStack(spacing: 0) {
                        // 顶部区域
                        ZStack(alignment: .topTrailing) {
                            VStack {
                                // 标题和返回按钮
                                HStack {
                                    Button(action: {
                                        presentationMode.wrappedValue.dismiss()
                                    }) {
                                        Image(systemName: "chevron.left")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(.black)
                                    }
                                    
                                    Spacer()
                                    
                                    Text("捏币商城")
                                        .font(Font.custom("PingFang SC", size: 20).weight(.medium))
                                        .foregroundColor(.black)
                                    
                                    Spacer()
                                    
                                    // 占位，保持标题居中
                                    Rectangle()
                                        .fill(Color.clear)
                                        .frame(width: 20, height: 20)
                                }
                                .padding(.horizontal, 16)
                                .padding(.top, 16)
                                
                                // 用户信息区域 - 与"我"页面保持一致
                                HStack(alignment: .top, spacing: 16) {
                                    // 用户头像 - 直接使用本地资源
                                    Image(userState.avatar)
                                        .resizable()
                                        .scaledToFill()
                                    .frame(width: 70, height: 70)
                                    .clipShape(Circle())
                                    .overlay(Circle().stroke(Color.white, lineWidth: 2))
                                    
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text(userState.username)
                                            .font(Font.custom("PingFang SC", size: 18).weight(.bold))
                                            .foregroundColor(.black)
                                        
                                        Text("用户ID: \(userState.userId)")
                                            .font(Font.custom("PingFang SC", size: 14))
                                            .foregroundColor(Color.gray)
                                            .padding(.top, 6)
                                        
                                        // 余额显示
                                        HStack(spacing: 4) {
                                            Text("账户余额:")
                                                .font(Font.custom("PingFang SC", size: 14))
                                                .foregroundColor(.gray)
                                            
                                            Text("\(userState.balance)")
                                                .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                                                .foregroundColor(.black)
                                            
                                            Text("捏币")
                                                .font(Font.custom("PingFang SC", size: 14))
                                                .foregroundColor(.gray)
                                        }
                                        .padding(.top, 4)
                                    }
                                    
                                    Spacer()
                                }
                                .padding(.horizontal, 16)
                                .padding(.top, 20)
                                .padding(.bottom, 20)
                            }
                            
                            // 右上角的河豚图标
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(width: 255.19138, height: 221.61357)
                                .background(
                                    Image("6-1")
                                        .resizable()
                                        .aspectRatio(contentMode: .fill)
                                        .frame(width: 255.19137573242188, height: 221.6135711669922)
                                        .clipped()
                                        .opacity(0.9)
                                )
                                .rotationEffect(Angle(degrees: -12.17))
                                .offset(x: 80, y: -50)
                        }
                        
                        // 充值金额选项 - 改为两行三列布局
                        VStack(spacing: 20) {
                            // 标题
                            HStack {
                                Text("请选择充值金额")
                                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                                    .foregroundColor(.black)
                                Spacer()
                            }
                            .padding(.horizontal, 20)
                            
                            // 第一行选项
                            HStack(spacing: 12) {
                                ForEach(firstRow, id: \.self) { index in
                                    CoinOptionSquare(
                                        amount: options[index].amount,
                                        price: getDisplayPrice(for: options[index].amount),
                                        isSelected: selectedOption == index,
                                        isLoading: !isProductsLoaded
                                    )
                                    .onTapGesture {
                                        withAnimation {
                                            selectedOption = index
                                        }
                                    }
                                }
                            }
                            .padding(.horizontal, 16)
                            
                            // 第二行选项
                            HStack(spacing: 12) {
                                ForEach(secondRow, id: \.self) { index in
                                    CoinOptionSquare(
                                        amount: options[index].amount,
                                        price: getDisplayPrice(for: options[index].amount),
                                        isSelected: selectedOption == index,
                                        isLoading: !isProductsLoaded
                                    )
                                    .onTapGesture {
                                        withAnimation {
                                            selectedOption = index
                                        }
                                    }
                                }
                            }
                            .padding(.horizontal, 16)
                        }
                        .padding(.top, 20)
                        
                        Spacer(minLength: 50)
                        
                        // 确认充值按钮
                        Button(action: {
                            purchaseSelectedOption()
                        }) {
                            ZStack {
                                Text("确认充值")
                                    .font(Font.custom("PingFang SC", size: 18).weight(.medium))
                                    .foregroundColor(.white)
                                    .frame(width: 167, height: 47)
                                    .background(
                                        Rectangle()
                                            .foregroundColor(.clear)
                                            .frame(width: 167, height: 47)
                                            .background(
                                                LinearGradient(
                                                    stops: [
                                                        Gradient.Stop(color: Color(red: 0.18, green: 0.8, blue: 0.62), location: 0.00),
                                                        Gradient.Stop(color: Color(red: 1, green: 0.78, blue: 0), location: 1.00),
                                                    ],
                                                    startPoint: UnitPoint(x: -0.94, y: -1.72),
                                                    endPoint: UnitPoint(x: 0.87, y: 1.36)
                                                )
                                            )
                                            .cornerRadius(23.5)
                                    )
                                    .padding(.bottom, 30)
                                
                                // 加载指示器
                                if isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .padding(.bottom, 30)
                                }
                            }
                        }
                        .disabled(selectedOption == nil || isLoading || storeManager.isPurchasing || !isProductsLoaded)
                        .frame(maxWidth: .infinity)
                        
                        // 添加产品加载指示器
                        if !isProductsLoaded && storeManager.products.isEmpty {
                            Text("正在加载商品信息...")
                                .font(Font.custom("PingFang SC", size: 14))
                                .foregroundColor(.gray)
                                .padding(.bottom, 10)
                        }
                    }
                    .frame(width: min(contentMaxWidth, geometry.size.width))
                    .padding(.horizontal, isIPad ? 20 : 0) // iPad模式下添加水平内边距
                    .frame(maxWidth: .infinity) // 使内容居中
                }
            }
        }
        .navigationBarHidden(true)
        .alert(isPresented: $showingAlert) {
            Alert(
                title: Text(alertTitle),
                message: Text(alertMessage),
                dismissButton: .default(Text("确定"))
            )
        }
        .onAppear {
            // 从服务器获取最新余额
            userState.fetchBalanceFromServer()
            
            // 检查产品是否已加载
            checkProductsLoaded()
            
            // 加载产品价格
            loadProductPrices()
        }
    }
    
    // 加载产品价格
    private func loadProductPrices() {
        // 如果产品已加载，直接设置价格
        if !storeManager.products.isEmpty {
            updateProductPrices()
            return
        }
        
        // 设置一个定时器，每秒检查一次产品是否加载完成
        let timer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { timer in
            if !storeManager.products.isEmpty {
                updateProductPrices()
                timer.invalidate()
            }
        }
        
        // 设置一个超时，如果10秒后还没有加载完成，也停止定时器
        DispatchQueue.main.asyncAfter(deadline: .now() + 10) {
            timer.invalidate()
        }
    }
    
    // 更新产品价格
    private func updateProductPrices() {
        for product in storeManager.products {
            if let amount = extractAmountFromProductId(product.id) {
                productPrices[amount] = product.displayPrice
            }
        }
        isProductsLoaded = true
    }
    
    // 从产品ID中提取金额
    private func extractAmountFromProductId(_ productId: String) -> Int? {
        // 产品ID格式: com.nienie.nn10, com.nienie.nn30, ...
        let prefix = "com.nienie.nn"
        if productId.hasPrefix(prefix) {
            let amountString = productId.dropFirst(prefix.count)
            return Int(amountString)
        }
        return nil
    }
    
    // 检查产品是否已加载
    private func checkProductsLoaded() {
        // 如果产品已加载，直接设置状态
        if !storeManager.products.isEmpty {
            isProductsLoaded = true
            return
        }
        
        // 设置一个定时器，每秒检查一次产品是否加载完成
        let timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
            if !storeManager.products.isEmpty {
                isProductsLoaded = true
                updateProductPrices()
                timer.invalidate()
            }
        }
        
        // 设置一个超时，如果10秒后还没有加载完成，也停止定时器
        DispatchQueue.main.asyncAfter(deadline: .now() + 10) {
            timer.invalidate()
            if storeManager.products.isEmpty {
                // 如果产品仍未加载，可以考虑显示一个提示或重试按钮
                print("产品加载超时")
            }
        }
    }
    
    // 获取显示价格
    private func getDisplayPrice(for amount: Int) -> String {
        // 首先尝试从缓存的价格中获取
        if let price = productPrices[amount] {
            return formatPriceWithRMBSymbol(price)
        }
        
        // 如果缓存中没有，尝试从StoreKit获取
        if let product = storeManager.getProductForCoinAmount(amount) {
            let price = product.displayPrice
            productPrices[amount] = price
            return formatPriceWithRMBSymbol(price)
        }
        
        // 如果StoreKit还未加载完成，返回加载中提示
        if !isProductsLoaded {
            return "加载中..."
        }
        
        // 最后才使用默认价格
        return formatPriceWithRMBSymbol(getDefaultPrice(for: amount))
    }
    
    // 获取默认价格
    private func getDefaultPrice(for amount: Int) -> String {
        switch amount {
        case 10: return "¥3"
        case 30: return "¥8"
        case 50: return "¥12"
        case 100: return "¥22"
        case 200: return "¥40"
        case 500: return "¥69"
        default: return ""
        }
    }
    
    // 格式化价格，确保使用人民币符号
    private func formatPriceWithRMBSymbol(_ price: String) -> String {
        // 如果价格已经包含¥符号，直接返回
        if price.contains("¥") {
            return price
        }
        
        // 如果价格包含$符号，替换为¥
        if price.contains("$") {
            return price.replacingOccurrences(of: "$", with: "¥")
        }
        
        // 如果没有货币符号，添加¥
        return "¥\(price)"
    }
    
    // 购买选中的选项
    private func purchaseSelectedOption() {
        guard let selectedIndex = selectedOption else { return }
        
        let amount = options[selectedIndex].amount
        
        // 查找对应的产品
        guard let product = storeManager.getProductForCoinAmount(amount) else {
            showAlert(title: "购买失败", message: "无法找到对应的产品，请稍后再试")
            return
        }
        
        // 设置加载状态
        isLoading = true
        
        // 执行购买
        storeManager.purchase(product: product, userId: userState.userId) { result in
            isLoading = false
            
            switch result {
            case .success(let (newBalance, coinAmount)):
                // 更新用户余额
                userState.updateBalance(newBalance: newBalance)
                
                // 显示成功提示
                showAlert(title: "充值成功", message: "成功充值 \(coinAmount) 捏币，当前余额: \(newBalance) 捏币")
                
            case .failure(let error):
                // 检查是否是用户取消购买
                let nsError = error as NSError
                if nsError.domain == "StoreKitManager" && 
                   nsError.code == 999 && 
                   nsError.userInfo[NSLocalizedDescriptionKey] as? String == "USER_CANCELLED" {
                    // 用户取消购买，不显示错误提示
                } else {
                    // 显示其他错误提示
                    showAlert(title: "充值失败", message: error.localizedDescription)
                }
            }
        }
    }
    
    // 显示提示框
    private func showAlert(title: String, message: String) {
        alertTitle = title
        alertMessage = message
        showingAlert = true
    }
}

// 方形捏币选项卡片
struct CoinOptionSquare: View {
    let amount: Int
    let price: String
    let isSelected: Bool
    let isLoading: Bool
    
    var body: some View {
        VStack(spacing: 8) {
            // 金币图标与数量
            HStack(spacing: 4) {
                Image("m-2")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 24, height: 24)
                
                Text("\(amount)")
                    .font(Font.custom("PingFang SC", size: 18).weight(.bold))
                    .foregroundColor(.black)
            }
            
            // 价格
            if isLoading && price == "加载中..." {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
                    .scaleEffect(0.7)
            } else {
                Text(price)
                    .font(Font.custom("PingFang SC", size: 16))
                    .foregroundColor(.gray)
            }
        }
        .padding(14)
        .frame(maxWidth: .infinity)
        .aspectRatio(1.0, contentMode: .fill)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.9))
                .shadow(color: isSelected ? Color.green.opacity(0.4) : Color.black.opacity(0.05),
                        radius: isSelected ? 8 : 4,
                        x: 0, y: 2)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(isSelected ? Color.green.opacity(0.8) : Color.clear, lineWidth: 2)
        )
    }
}

struct CoinShopView_Previews: PreviewProvider {
    static var previews: some View {
        CoinShopView()
            .environmentObject(UserState())
    }
} 