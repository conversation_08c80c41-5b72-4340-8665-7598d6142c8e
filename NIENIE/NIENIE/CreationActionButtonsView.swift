import SwiftUI

/// 创作界面底部按钮组件
struct CreationActionButtonsView: View {
    // 必要的参数
    var processingStatus: String
    var outputImage: UIImage?
    var taskId: String
    var isImageEnhanced: Bool
    var imageHelper: ImageSaveHelper
    
    // 回调函数
    var onPublish: () -> Void
    var onGoToHome: () -> Void
    var onLightEnhance: () -> Void
    
    // 状态变量
    @Binding var showAlert: Bool
    @Binding var alertMessage: String
    
    var body: some View {
        VStack(spacing: 20) {
            // 捏币奖励提示
            HStack {
                Image("4-1")
                    .resizable()
                    .frame(width: 24, height: 24)
                
                Text("发布将获得捏币奖励哦～")
                    .font(Font.custom("PingFang SC", size: 16))
                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                
                Spacer()
            }
            .padding(.horizontal, 20)
            
            // 人物补光按钮
            Button(action: {
                onLightEnhance()
            }) {
                HStack(spacing: 5) {
                    Image(systemName: "sun.max")
                        .font(.system(size: 16))
                    Text("人物补光")
                        .font(Font.custom("PingFang SC", size: 15))
                }
                .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                .frame(maxWidth: .infinity)
                .frame(height: 33)
                .background(
                    Rectangle()
                        .foregroundColor(.clear)
                        .background(.white)
                        .cornerRadius(30)
                        .overlay(
                            RoundedRectangle(cornerRadius: 30)
                                .inset(by: 0.25)
                                .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 0.5)
                        )
                )
            }
            .disabled(processingStatus != "completed")
            .opacity(processingStatus == "completed" ? 1.0 : 0.5)
            .padding(.horizontal, 20)
            
            // 按钮区域
            HStack(spacing: 20) {
                // 发布按钮
                Button(action: {
                    onPublish()
                }) {
                    HStack(spacing: 5) {
                        Image("w-1")
                            .resizable()
                            .frame(width: 20, height: 20)
                        Text("发布")
                            .font(Font.custom("PingFang SC", size: 15))
                    }
                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                    .frame(width: 91, height: 33)
                    .background(
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(width: 91, height: 33)
                            .background(.white)
                            .cornerRadius(30)
                            .overlay(
                                RoundedRectangle(cornerRadius: 30)
                                    .inset(by: 0.25)
                                    .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 0.5)
                            )
                    )
                }
                .disabled(processingStatus != "completed")
                .opacity(processingStatus == "completed" ? 1.0 : 0.5)
                
                // 保存按钮
                Button(action: {
                    guard processingStatus == "completed", let image = outputImage else { return }
                    
                    // 将图片转换为JPEG格式
                    if let jpegData = image.jpegData(compressionQuality: 1.0),
                       let jpegImage = UIImage(data: jpegData) {
                        // 保存JPEG格式图片
                        imageHelper.saveImageToAlbum(image: jpegImage)
                    } else {
                        // 保存原始图片
                        imageHelper.saveImageToAlbum(image: image)
                    }
                }) {
                    HStack(spacing: 5) {
                        Image("w-2")
                            .resizable()
                            .frame(width: 20, height: 20)
                        Text("保存")
                            .font(Font.custom("PingFang SC", size: 15))
                    }
                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                    .frame(width: 91, height: 33)
                    .background(
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(width: 91, height: 33)
                            .background(.white)
                            .cornerRadius(30)
                            .overlay(
                                RoundedRectangle(cornerRadius: 30)
                                    .inset(by: 0.25)
                                    .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 0.5)
                            )
                    )
                }
                .disabled(processingStatus != "completed")
                .opacity(processingStatus == "completed" ? 1.0 : 0.5)
                
                // 首页按钮
                Button(action: {
                    onGoToHome()
                }) {
                    HStack(spacing: 5) {
                        Image("w-3")
                            .resizable()
                            .frame(width: 20, height: 20)
                        Text("首页")
                            .font(Font.custom("PingFang SC", size: 15))
                    }
                    .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                    .frame(width: 91, height: 33)
                    .background(
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(width: 91, height: 33)
                            .background(.white)
                            .cornerRadius(30)
                            .overlay(
                                RoundedRectangle(cornerRadius: 30)
                                    .inset(by: 0.25)
                                    .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 0.5)
                            )
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 20)
        }
        .padding(.top, 20)
        .background(Color.white)
    }
}

#Preview {
    // 预览示例
    CreationActionButtonsView(
        processingStatus: "completed",
        outputImage: UIImage(systemName: "photo"),
        taskId: "sample",
        isImageEnhanced: false,
        imageHelper: ImageSaveHelper(),
        onPublish: {},
        onGoToHome: {},
        onLightEnhance: {},
        showAlert: .constant(false),
        alertMessage: .constant("")
    )
} 