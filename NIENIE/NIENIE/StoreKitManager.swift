import Foundation
import StoreKit
class StoreKitManager: NSObject, ObservableObject {
    static let shared = StoreKitManager()
    
    @Published var products: [Product] = []
    @Published var purchasingProduct: Product? = nil
    @Published var isPurchasing: Bool = false
    
    private let productIDs: Set<String> = [
        "com.nienie.nn10", "com.nienie.nn30", "com.nienie.nn50",
        "com.nienie.nn100", "com.nienie.nn200", "com.nienie.nn500"
    ]
    
    private override init() {
        super.init()
        Task {
            await loadProducts()
        }
    }
    
    @MainActor
    func loadProducts() async {
        do {
            products = try await Product.products(for: productIDs)
            products.sort { (product1, product2) -> Bool in
                // 根据价格排序
                return product1.price < product2.price
            }
        } catch {
            print("加载产品失败: \(error.localizedDescription)")
        }
    }
    
    func purchase(product: Product, userId: Int, completion: @escaping (Result<(Int, Int), Error>) -> Void) {
        Task {
            do {
                // 设置正在购买状态
                await MainActor.run {
                    self.purchasingProduct = product
                    self.isPurchasing = true
                }
                
                // 购买产品
                let result = try await product.purchase()
                
                switch result {
                case .success(let verificationResult):
                    switch verificationResult {
                    case .verified(let transaction):
                        // 购买成功，验证购买并更新余额
                        await MainActor.run {
                            self.verifyPurchaseWithServer(
                                userId: userId,
                                productId: product.id,
                                transaction: transaction,
                                completion: completion
                            )
                        }
                        
                        // 完成交易
                        await transaction.finish()
                        
                    case .unverified(_, let error):
                        // 购买未验证
                        await MainActor.run {
                            self.isPurchasing = false
                            self.purchasingProduct = nil
                            completion(.failure(error))
                        }
                    }
                    
                case .userCancelled:
                    // 用户取消购买，静默处理，不显示错误提示
                    await MainActor.run {
                        self.isPurchasing = false
                        self.purchasingProduct = nil
                        // 通知调用者用户取消了购买，但使用特殊的错误码，让调用者知道是取消操作
                        completion(.failure(NSError(domain: "StoreKitManager", code: 999, userInfo: [NSLocalizedDescriptionKey: "USER_CANCELLED"])))
                    }
                    
                case .pending:
                    // 购买待处理
                    await MainActor.run {
                        self.isPurchasing = false
                        self.purchasingProduct = nil
                        completion(.failure(NSError(domain: "StoreKitManager", code: 2, userInfo: [NSLocalizedDescriptionKey: "购买待处理"])))
                    }
                    
                @unknown default:
                    // 未知状态
                    await MainActor.run {
                        self.isPurchasing = false
                        self.purchasingProduct = nil
                        completion(.failure(NSError(domain: "StoreKitManager", code: 3, userInfo: [NSLocalizedDescriptionKey: "未知购买状态"])))
                    }
                }
                
            } catch {
                // 购买过程中发生错误
                await MainActor.run {
                    self.isPurchasing = false
                    self.purchasingProduct = nil
                    completion(.failure(error))
                }
            }
        }
    }
    
    private func verifyPurchaseWithServer(userId: Int, productId: String, transaction: StoreKit.Transaction, completion: @escaping (Result<(Int, Int), Error>) -> Void) {
        // 获取交易ID和相关信息
        let transactionId = transaction.id
        let originalTransactionId = transaction.originalID
        
        // 创建包含交易信息的字典
        let transactionInfo: [String: String] = [
            "transaction_id": "\(transactionId)",
            "original_transaction_id": "\(originalTransactionId)",
            "product_id": productId
        ]
        
        do {
            // 将交易信息转换为JSON字符串
            let jsonData = try JSONSerialization.data(withJSONObject: transactionInfo)
            let transactionInfoString = String(data: jsonData, encoding: .utf8) ?? "{}"
            
            
            // 调用API验证购买
            APIService.shared.verifyPurchase(userId: userId, productId: productId, receiptData: transactionInfoString) { result in
                DispatchQueue.main.async {
                    self.isPurchasing = false
                    self.purchasingProduct = nil
                    
                    switch result {
                    case .success:
                        completion(result)
                    case .failure(_):
                        
                        // 如果验证失败，尝试使用简化验证（仅发送交易ID）
                        let simpleInfo = ["transaction_id": "\(transactionId)"]
                        if let simpleData = try? JSONSerialization.data(withJSONObject: simpleInfo),
                           let simpleString = String(data: simpleData, encoding: .utf8) {
                            
                            
                            APIService.shared.verifyPurchase(userId: userId, productId: productId, receiptData: simpleString) { retryResult in
                                DispatchQueue.main.async {
                                    completion(retryResult)
                                }
                            }
                        } else {
                            completion(result) // 如果无法创建简化信息，返回原始错误
                        }
                    }
                }
            }
        } catch {
            completion(.failure(NSError(domain: "StoreKitManager", code: 6, userInfo: [NSLocalizedDescriptionKey: "验证收据时出错: \(error.localizedDescription)"])))
        }
    }
    
    // 获取产品价格的格式化字符串
    func getPriceString(for product: Product) -> String {
        return product.displayPrice
    }
    
    // 根据产品ID获取产品
    func getProduct(for productId: String) -> Product? {
        return products.first { $0.id == productId }
    }
    
    // 获取特定捏币数量对应的产品
    func getProductForCoinAmount(_ amount: Int) -> Product? {
        let productId = "com.nienie.nn\(amount)"
        return getProduct(for: productId)
    }
} 
