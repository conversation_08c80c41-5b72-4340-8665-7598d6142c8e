import SwiftUI
import Combine
import Foundation

struct ContentView: View {
    @EnvironmentObject var userState: UserState
    @State private var selectedTabIndex = 0
    @State private var selectedContentTab = 0  // 0: 排行, 1: 最新
    @State private var images: [ImageData] = []
    @State private var isLoading = false
    @State private var errorMessage: String? = nil
    @State private var currentPage = 1
    @State private var isLoadingMore = false
    @State private var hasMoreImages = true
    @State private var showImagePicker = false
    @State private var showCamera = false
    @State private var showTemplateGallery = false
    @State private var selectedImage: UIImage?
    @State private var showAddImageAlert: Bool = false
    @State private var isRefreshing = false // 添加刷新状态
    @State private var tempImages: [ImageData] = [] // 临时存储刷新时的图片
    @State private var scrollToTop = false // 控制滚动到顶部的状态
    @State private var adImages: [AdImageData] = [] // 广告图片列表
    @State private var currentAdIndex = 0 // 当前广告图片索引
    @State private var isLoadingAds = false // 广告加载状态
    @State private var lastLoadMoreTime = Date(timeIntervalSince1970: 0) // 上次加载更多的时间

    // 功能模块状态变量
    @State private var showStickerView = false
    @State private var showDepthLightingView = false
    @State private var showSuperResolutionView = false
    @State private var showStyleTransferView = false

    // 手势状态跟踪
    @State private var dragStartLocation: CGPoint = .zero
    @State private var isDragging = false

    // 社区类别选择状态
    @State private var selectedCategory = -1 // -1表示全部类别

    // 添加全局反馈菜单状态
    @State private var showGlobalFeedbackMenu = false
    @State private var feedbackImage: ImageData? = nil
    @State private var imageToRemove: Int? = nil
    
    var body: some View {
        NavigationStack {
            GeometryReader { geometry in
                ZStack(alignment: .top) {
                    // 1. 主背景 - 根据选择的标签动态变化
                    // 移除个人中心页的背景设置，让ProfileView自己管理背景
                    if userState.selectedTab != 2 { // 非个人中心页面
                        Color.white
                            .ignoresSafeArea()
                    }
                    
                    // 2. 图片网格
                    TabView(selection: $userState.selectedTab) {
                        // 首页内容
                        VStack(spacing: 0) {
                            // 广告位
                            if !adImages.isEmpty {
                                TabView(selection: $currentAdIndex) {
                                    ForEach(0..<adImages.count, id: \.self) { index in
                                        AsyncImage(url: adImages[index].imageURL) { image in
                                            image
                                                .resizable()
                                                .aspectRatio(contentMode: .fill)
                                                .clipped()
                                        } placeholder: {
                                            Rectangle()
                                                .fill(Color.gray.opacity(0.3))
                                                .overlay(
                                                    ProgressView()
                                                        .progressViewStyle(CircularProgressViewStyle())
                                                )
                                        }
                                        .tag(index)
                                    }
                                }
                                .frame(height: 120)
                                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
                                .ignoresSafeArea(edges: .all)
                            }

                            // 功能模块横向滚动行
                            FunctionScrollView(isDragging: $isDragging, userState: userState, showStickerView: $showStickerView, showDepthLightingView: $showDepthLightingView, showSuperResolutionView: $showSuperResolutionView, showStyleTransferView: $showStyleTransferView)

                            // 社区类别选择滚动行
                            CategoryScrollView(selectedCategory: $selectedCategory, isDragging: $isDragging) { categoryId in
                                // 类别改变时重新加载图片
                                withAnimation {
                                    images = []
                                    currentPage = 1
                                    hasMoreImages = true
                                    lastLoadMoreTime = Date(timeIntervalSince1970: 0)
                                    scrollToTop = true
                                    loadHomeImages(page: 1, resetImages: true, sortBy: selectedContentTab == 0 ? "rank" : "time", categoryId: categoryId)
                                }
                            }

                            // 排行和最新选择区域
                            HStack(spacing: 0) {
                                Button(action: {
                                    if selectedContentTab != 0 {
                                        withAnimation {
                                            selectedContentTab = 0
                                            images = []  // 立即清空图片列表
                                            currentPage = 1
                                            hasMoreImages = true
                                            lastLoadMoreTime = Date(timeIntervalSince1970: 0) // 重置加载时间
                                            scrollToTop = true // 标记需要滚动到顶部
                                            loadHomeImages(page: 1, resetImages: true, sortBy: "rank", categoryId: selectedCategory)
                                        }
                                    } else {
                                        // 点击当前选中的"排行"标签时，刷新内容
                                        scrollToTop = true // 标记需要滚动到顶部
                                        refreshContent(sortBy: "rank")
                                    }
                                }) {
                                    VStack(spacing: 8) {
                                        if isRefreshing && selectedContentTab == 0 {
                                            // 刷新动画
                                            RefreshAnimationView()
                                                .frame(height: 20)
                                        } else {
                                            Text("排行")
                                                .font(Font.custom("PingFang SC", size: 16).weight(.bold))
                                                .foregroundColor(selectedContentTab == 0 ? .black : .gray)
                                        }
                                        
                                        if selectedContentTab == 0 {
                                            Capsule()
                                                .frame(height: 3)
                                                .foregroundColor(.blue)
                                                .transition(.opacity)
                                        }
                                    }
                                    .frame(maxWidth: .infinity)
                                }
                                
                                Button(action: {
                                    if selectedContentTab != 1 {
                                        withAnimation {
                                            selectedContentTab = 1
                                            images = []  // 立即清空图片列表
                                            currentPage = 1
                                            hasMoreImages = true
                                            lastLoadMoreTime = Date(timeIntervalSince1970: 0) // 重置加载时间
                                            scrollToTop = true // 标记需要滚动到顶部
                                            loadHomeImages(page: 1, resetImages: true, sortBy: "time", categoryId: selectedCategory)
                                        }
                                    } else {
                                        // 点击当前选中的"最新"标签时，刷新内容
                                        scrollToTop = true // 标记需要滚动到顶部
                                        refreshContent(sortBy: "time")
                                    }
                                }) {
                                    VStack(spacing: 8) {
                                        if isRefreshing && selectedContentTab == 1 {
                                            // 刷新动画
                                            RefreshAnimationView()
                                                .frame(height: 20)
                                        } else {
                                            Text("最新")
                                                .font(Font.custom("PingFang SC", size: 16).weight(.bold))
                                                .foregroundColor(selectedContentTab == 1 ? .black : .gray)
                                        }
                                        
                                        if selectedContentTab == 1 {
                                            Capsule()
                                                .frame(height: 3)
                                                .foregroundColor(.blue)
                                                .transition(.opacity)
                                        }
                                    }
                                    .frame(maxWidth: .infinity)
                                }
                            }
                            .padding(.top, 16) // 适当的顶部间距
                            .padding(.horizontal, 40)
                            
                            // 内容显示区域
                            ZStack {
                                if isLoading && images.isEmpty {
                                    VStack {
                                        Spacer()
                                        ProgressView("加载中...")
                                            .progressViewStyle(CircularProgressViewStyle())
                                            .scaleEffect(1.5)
                                        Spacer()
                                    }
                                } else if let error = errorMessage {
                                    VStack {
                                        Spacer()
                                        Text(error)
                                            .font(Font.custom("PingFang SC", size: 16))
                                            .foregroundColor(.red)
                                        
                                        Button(action: {
                                            loadHomeImages(page: 1, resetImages: true, sortBy: selectedContentTab == 0 ? "rank" : "time", categoryId: selectedCategory)
                                        }) {
                                            Text("重新加载")
                                                .font(Font.custom("PingFang SC", size: 16))
                                                .foregroundColor(.white)
                                                .padding(.vertical, 10)
                                                .padding(.horizontal, 20)
                                                .background(Color.blue)
                                                .cornerRadius(10)
                                        }
                                        .padding(.top, 10)
                                        Spacer()
                                    }
                                } else {
                                    ScrollViewReader { scrollViewProxy in
                                        ScrollView {
                                            // 添加一个顶部锚点视图
                                            VStack {
                                                Rectangle()
                                                    .frame(width: 0, height: 0)
                                                    .id("scrollToTop") // 添加顶部锚点ID
                                                
                                                HStack(alignment: .top, spacing: 15) {
                                                    // 第一列 - 高度模式: 1.3, 1, 1.3, 1...
                                                    VStack(spacing: 15) {
                                                        ForEach(0..<(images.count + 1) / 2, id: \.self) { index in
                                                            let imageIndex = index * 2
                                                            if imageIndex < images.count {
                                                                let heightMultiplier = index % 2 == 0 ? 1.3 : 1.0
                                                                let image = images[imageIndex]
                                                                ImageCard(
                                                                    image: image,
                                                                    geometry: geometry,
                                                                    heightMultiplier: heightMultiplier,
                                                                    onShowFeedback: { image in
                                                                        // 显示全局反馈菜单
                                                                        feedbackImage = image
                                                                        showGlobalFeedbackMenu = true
                                                                        imageToRemove = imageIndex
                                                                    }
                                                                )
                                                                .environmentObject(userState)
                                                                .id("\(image.imageId)-\(selectedContentTab)") // 移除liked和likeCount，避免点赞操作触发重建
                                                            }
                                                        }
                                                    }
                                                    .frame(width: (geometry.size.width - 30) / 2)
                                                    
                                                    // 第二列 - 高度模式: 1, 1.3, 1, 1.3...
                                                    VStack(spacing: 15) {
                                                        ForEach(0..<images.count / 2, id: \.self) { index in
                                                            let imageIndex = index * 2 + 1
                                                            if imageIndex < images.count {
                                                                let heightMultiplier = index % 2 == 0 ? 1.0 : 1.3
                                                                let image = images[imageIndex]
                                                                ImageCard(
                                                                    image: image,
                                                                    geometry: geometry,
                                                                    heightMultiplier: heightMultiplier,
                                                                    onShowFeedback: { image in
                                                                        // 显示全局反馈菜单
                                                                        feedbackImage = image
                                                                        showGlobalFeedbackMenu = true
                                                                        imageToRemove = imageIndex
                                                                    }
                                                                )
                                                                .environmentObject(userState)
                                                                .id("\(image.imageId)-\(selectedContentTab)") // 移除liked和likeCount，避免点赞操作触发重建
                                                            }
                                                        }
                                                    }
                                                    .frame(width: (geometry.size.width - 30) / 2)
                                                }
                                                .padding(.horizontal, 15)
                                                .padding(.bottom, 70) // 为底部导航栏留出空间
                                            }
                                            
                                                                                            // 加载更多指示器
                                                if hasMoreImages {
                                                    HStack {
                                                        Spacer()
                                                        if isLoadingMore {
                                                            ProgressView("加载更多...")
                                                        } else {
                                                            Text("上拉加载更多")
                                                                .font(.caption)
                                                                .foregroundColor(.gray)
                                                                .id("loadMoreTrigger") // 添加一个唯一ID方便识别
                                                        }
                                                        Spacer()
                                                    }
                                                    .frame(height: 50)
                                                    .padding()
                                                    // 移除自动加载的onAppear
                                                    // 添加一个GeometryReader来检测这个视图是否出现在屏幕上
                                                    .background(
                                                        GeometryReader { loadMoreGeometry -> Color in
                                                            // 计算这个视图相对于ScrollView的位置
                                                            let offset = loadMoreGeometry.frame(in: .global).minY
                                                            
                                                            // 只有当用户主动上拉到足够位置时才触发加载更多
                                                            DispatchQueue.main.async {
                                                                // 当视图接近屏幕底部且用户正在上拉时才加载更多
                                                                if offset < UIScreen.main.bounds.height * 0.9 && !isLoadingMore && hasMoreImages {
                                                                    loadMoreHomeImagesWithDebounce()
                                                                }
                                                            }
                                                            
                                                            return Color.clear
                                                        }
                                                    )
                                            } else if !images.isEmpty {
                                                HStack {
                                                    Spacer()
                                                    Text("没有更多内容了")
                                                        .font(.caption)
                                                        .foregroundColor(.gray)
                                                    Spacer()
                                                }
                                                .frame(height: 50)
                                                .padding()
                                            }
                                        }
                                        .onChange(of: scrollToTop) { oldValue, newValue in
                                            if newValue {
                                                // 当数据加载完成后，滚动到顶部
                                                withAnimation {
                                                    scrollViewProxy.scrollTo("scrollToTop", anchor: .top)
                                                }
                                                // 重置状态
                                                scrollToTop = false
                                            }
                                        }
                                        // 添加下拉刷新功能
                                        .refreshable {
                                            // 使用 withCheckedContinuation 桥接旧的完成回调模式
                                            await withCheckedContinuation { continuation in
                                                // 根据当前选中的标签页选择排序方式
                                                let sortBy = selectedContentTab == 0 ? "rank" : "time"
                                                
                                                // 设置刷新状态为true，触发动画
                                                isRefreshing = true
                                                // 保存当前图片列表，以便在刷新完成前保持显示
                                                tempImages = images
                                                
                                                // 重置加载状态
                                                lastLoadMoreTime = Date(timeIntervalSince1970: 0)
                                                
                                                // 调用API获取新数据
                                                APIService.shared.getHomeImages(page: 1, sortBy: sortBy) { result in
                                                    DispatchQueue.main.async {
                                                        isRefreshing = false
                                                        
                                                        switch result {
                                                        case .success(let fetchedImages):
                                                            // 过滤掉用户不感兴趣的内容
                                                            let filteredImages = fetchedImages.filter { image in
                                                                !self.userState.shouldFilterContent(imageId: image.imageId, username: image.username)
                                                            }
                                                            
                                                            // 刷新完成后更新图片列表
                                                            self.images = filteredImages
                                                            self.currentPage = 1
                                                            self.hasMoreImages = fetchedImages.count >= 20
                                                            
                                                            // 数据加载完成后，触发滚动到顶部
                                                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                                                self.scrollToTop = true
                                                            }
                                                            
                                                        case .failure(let error):
                                                            // 刷新失败时恢复原来的图片列表
                                                            self.images = self.tempImages
                                                            self.errorMessage = "刷新失败: \(error.localizedDescription)"
                                                        }
                                                        
                                                        // 完成刷新操作，恢复异步流程
                                                        continuation.resume()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        .onAppear {

                            if images.isEmpty {
                                loadHomeImages(page: 1, resetImages: true, sortBy: selectedContentTab == 0 ? "rank" : "time", categoryId: selectedCategory)
                            }

                            // 首次进入时加载广告图片
                            if adImages.isEmpty {
                                loadAdImages()
                            }
                            // 不再自动加载下一页
                            
                            // 添加通知观察者
                            NotificationCenter.default.addObserver(
                                forName: NSNotification.Name("RefreshHomeImages"),
                                object: nil,
                                queue: .main
                            ) { notification in
                                // 检查是否有特定图片更新
                                if let userInfo = notification.userInfo,
                                   let imageId = userInfo["imageId"] as? Int,
                                   let liked = userInfo["liked"] as? Bool,
                                   let likeCount = userInfo["likeCount"] as? Int {
                                    
                                    
                                    // 只更新特定图片的点赞状态和计数，不重新加载图片
                                    for (index, image) in self.images.enumerated() {
                                        if image.imageId == imageId {
                                            
                                            // 创建更新后的图片数据，保留原始storagePath
                                            var updatedImage = self.images[index]
                                            updatedImage.liked = liked
                                            updatedImage.likeCount = likeCount
                                            
                                            // 更新数组中的图片
                                            self.images[index] = updatedImage
                                            break
                                        }
                                    }
                                } else {
                                    // 如果没有特定图片信息，则刷新整个列表
                                    self.images = []
                                    self.currentPage = 1
                                    self.hasMoreImages = true
                                    loadHomeImages(page: 1, resetImages: true, sortBy: selectedContentTab == 0 ? "rank" : "time", categoryId: selectedCategory)
                                }
                            }
                        }
                        .onDisappear {
                            
                            // 移除通知观察者
                            NotificationCenter.default.removeObserver(self)
                        }
                        .tag(0)
                        
                        // 推荐页面
                        RecommendView()
                            .environmentObject(userState)
                            .tag(1)
                        
                        ProfileView()
                            .environmentObject(userState)
                            .tag(2)
                    }
                    
                    // 3. 底部导航栏
                    VStack {
                        Spacer()
                        BottomNavigationBar(selectedTabIndex: $selectedTabIndex, geometry: geometry)
                            .environmentObject(userState)
                    }
                    
                    // 3. 图片选择弹窗
                    if userState.showAICreateSheet {
                        ImageSourcePickerSheet(
                            isPresented: $userState.showAICreateSheet,
                            onTemplateSelected: {
                                // 打开模版画廊
                                showTemplateGallery = true
                            },
                            onCameraSelected: {
                                showCamera = true
                            },
                            onPhotoLibrarySelected: {
                                showImagePicker = true
                            },
                            showTemplateOption: true
                        )
                    }
                    
                    // 悬浮球视图
                    if userState.showFloatingBall {
                        FloatingBallView()
                            .environmentObject(userState)
                            .zIndex(100) // 确保悬浮球始终位于最上层
                    }
                    
                    // 全局反馈菜单
                    if showGlobalFeedbackMenu, let image = feedbackImage {
                        FeedbackMenuView(
                            image: image,
                            isPresented: $showGlobalFeedbackMenu,
                            onRemove: {
                                // 移除图片
                                if let indexToRemove = imageToRemove {
                                    _ = withAnimation {
                                        images.remove(at: indexToRemove)
                                    }
                                    imageToRemove = nil
                                }
                            }
                        )
                        .environmentObject(userState)
                        .zIndex(200) // 确保反馈菜单在最顶层
                    }
                }
                .ignoresSafeArea(edges: .bottom)
            }
        }
        .toolbar(.hidden, for: .navigationBar)
        .fullScreenCover(isPresented: $showImagePicker) {
            ImagePickerView(sourceType: .photoLibrary) { image in
                if let image = image {
                    self.selectedImage = image
                    handleSelectedImage()
                }
            }
        }
        .fullScreenCover(isPresented: $showCamera) {
            ImagePickerView(sourceType: .camera) { image in
                if let image = image {
                    self.selectedImage = image
                    handleSelectedImage()
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("SwitchToHomeTab"))) { _ in
            userState.selectedTab = 0  // 切换到社区标签（索引为0）
        }
        .onAppear {
            // 确保初始状态同步
            selectedTabIndex = userState.selectedTab
        }
        .onChange(of: userState.selectedTab) { oldValue, newValue in
            // 当userState.selectedTab变化时，同步更新selectedTabIndex
            selectedTabIndex = newValue
        }
        .onChange(of: selectedTabIndex) { oldValue, newValue in
            // 当selectedTabIndex变化时，同步更新userState.selectedTab
            userState.selectedTab = newValue
        }
        // 功能模块导航链接
        .fullScreenCover(isPresented: $showStickerView) {
            StickerCreationView()
                .environmentObject(userState)
        }
        .fullScreenCover(isPresented: $showDepthLightingView) {
            DepthLightingView()
                .environmentObject(userState)
        }
        .fullScreenCover(isPresented: $showSuperResolutionView) {
            SuperResolutionView()
                .environmentObject(userState)
        }
        .fullScreenCover(isPresented: $showStyleTransferView) {
            StyleTransferView()
                .environmentObject(userState)
        }
        .fullScreenCover(isPresented: $showTemplateGallery) {
            TemplateGalleryView { selectedTemplate in
                // 关闭模版画廊
                showTemplateGallery = false
                // 关闭图片选择弹窗
                userState.showAICreateSheet = false
                // 处理选择的模版
                self.selectedImage = selectedTemplate
                handleSelectedImage()
            }
        }
    }
    
    // 添加刷新内容功能
    private func refreshContent(sortBy: String) {
        // 设置刷新状态为true，触发动画
        isRefreshing = true
        // 保存当前图片列表，以便在刷新完成前保持显示
        tempImages = images
        
        // 重置加载状态
        lastLoadMoreTime = Date(timeIntervalSince1970: 0)
        
        // 调用API获取新数据
        APIService.shared.getHomeImages(page: 1, sortBy: sortBy, categoryId: selectedCategory) { result in
            // 延迟1.5秒，让动画有足够时间显示
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                isRefreshing = false
                
                switch result {
                case .success(let fetchedImages):
                    
                    // 过滤掉用户不感兴趣的内容
                    let filteredImages = fetchedImages.filter { image in
                        !self.userState.shouldFilterContent(imageId: image.imageId, username: image.username)
                    }
                    
                    // 刷新完成后更新图片列表
                    self.images = filteredImages
                    self.currentPage = 1
                    self.hasMoreImages = fetchedImages.count >= 20
                    
                    // 数据加载完成后，触发滚动到顶部
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        self.scrollToTop = true
                    }
                    
                case .failure(let error):
                    // 刷新失败时恢复原来的图片列表
                    self.images = self.tempImages
                    self.errorMessage = "刷新失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    // 处理选择的图片
    private func handleSelectedImage() {
        guard let image = selectedImage else {
            return
        }
        
        
        // 关闭弹窗
        userState.showAICreateSheet = false
        
        // 确保没有其他视图正在展示
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            if rootViewController.presentedViewController != nil {
                rootViewController.dismiss(animated: false) {
                    self.presentImageEditView(image: image)
                }
                return
            }
        }
        
        // 显示图片编辑界面，使用主队列延迟处理，确保UI更新完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            presentImageEditView(image: image)
        }
    }
    
    // 直接以模态方式显示图片编辑界面
    private func presentImageEditView(image: UIImage) {
        let hostingController = UIHostingController(rootView: ImageEditView(image: image)
                                                   .environmentObject(userState))
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            hostingController.modalPresentationStyle = .fullScreen
            rootViewController.present(hostingController, animated: true) {
            }
        } 
    }
    
    private func loadHomeImages(page: Int = 1, resetImages: Bool = false, sortBy: String = "rank", categoryId: Int = -1) {
        if resetImages {
            isLoading = true
            errorMessage = nil
            // 确保在重置时立即清空images数组，防止显示旧数据
            images = []
        } else {
            isLoadingMore = true
        }

        APIService.shared.getHomeImages(page: page, sortBy: sortBy, categoryId: categoryId) { result in
            DispatchQueue.main.async {
                if resetImages {
                    isLoading = false
                } else {
                    isLoadingMore = false
                }
                
                switch result {
                case .success(let fetchedImages):                    
                    
                    // 过滤掉用户不感兴趣的内容
                    let filteredImages = fetchedImages.filter { image in
                        !self.userState.shouldFilterContent(imageId: image.imageId, username: image.username)
                    }
                    
                    if resetImages {
                        self.images = filteredImages
                        
                        // 数据加载完成后，触发滚动到顶部
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            self.scrollToTop = true
                        }
                    } else {
                        // 添加新图片并去重
                        let existingIds = Set(self.images.map { $0.imageId })
                        let newImages = filteredImages.filter { !existingIds.contains($0.imageId) }
                        
                        if !newImages.isEmpty {
                            self.images.append(contentsOf: newImages)
                        } 
                    }
                    
                    // 检查是否还有更多图片 - 如果获取到的图片数量小于页面大小，则没有更多图片
                    self.hasMoreImages = fetchedImages.count >= 20 // 假设每页20张图片
                    
                    // 更新当前页码
                    if !resetImages && fetchedImages.count > 0 {
                        self.currentPage = page
                    }
                    
                case .failure(let error):
                    if resetImages {
                        self.errorMessage = "加载图片失败: \(error.localizedDescription)"
                    }
                }
            }
        }
    }
    
    // 添加防抖机制的加载更多函数
    private func loadMoreHomeImagesWithDebounce() {
        let now = Date()
        // 如果距离上次加载时间不足2秒，则不重复加载
        if now.timeIntervalSince(lastLoadMoreTime) < 2.0 {
            return
        }
        
        lastLoadMoreTime = now
        loadMoreHomeImages()
    }

    private func loadMoreHomeImages() {
        guard hasMoreImages && !isLoadingMore else {
            return
        }

        // 记录当前页码并加载下一页
        let nextPage = currentPage + 1
        loadHomeImages(page: nextPage, sortBy: selectedContentTab == 0 ? "rank" : "time", categoryId: selectedCategory)
    }

    // 加载广告图片
    private func loadAdImages() {
        guard !isLoadingAds else { return }

        isLoadingAds = true
        APIService.shared.getRecommendImages { result in
            DispatchQueue.main.async {
                self.isLoadingAds = false
                switch result {
                case .success(let fetchedAdImages):
                    self.adImages = fetchedAdImages
                case .failure(let error):
                    print("加载广告图片失败: \(error.localizedDescription)")
                    // 广告加载失败不影响主要功能，只是不显示广告
                }
            }
        }
    }
}

// 添加刷新动画视图
struct RefreshAnimationView: View {
    @State private var blueBallPosition = CGFloat(0)
    @State private var yellowBallPosition = CGFloat(1)
    @State private var isAnimating = true
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 蓝色小球
                Circle()
                    .fill(Color.blue)
                    .frame(width: 10, height: 10)
                    .position(x: geometry.size.width * blueBallPosition, y: geometry.size.height / 2)
                
                // 黄色小球
                Circle()
                    .fill(Color.yellow)
                    .frame(width: 10, height: 10)
                    .position(x: geometry.size.width * yellowBallPosition, y: geometry.size.height / 2)
            }
            .onAppear {
                withAnimation(Animation.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
                    blueBallPosition = 1
                    yellowBallPosition = 0
                }
            }
        }
    }
}

struct ImageCard: View {
    let image: ImageData
    let geometry: GeometryProxy
    @EnvironmentObject var userState: UserState
    @State private var isLiked: Bool
    @State private var likeCount: Int
    @State private var navigateToDetail = false
    let heightMultiplier: CGFloat
    @State private var imageLoaded = false // 添加状态跟踪图片是否已加载
    @State private var shouldRemove = false // 添加状态控制是否移除卡片
    var onShowFeedback: (ImageData) -> Void // 添加回调函数
    
    init(image: ImageData, geometry: GeometryProxy, heightMultiplier: CGFloat = 1.0, onShowFeedback: @escaping (ImageData) -> Void = { _ in }) {
        self.image = image
        self.geometry = geometry
        self._likeCount = State(initialValue: image.likeCount)
        self._isLiked = State(initialValue: image.liked)
        self.heightMultiplier = heightMultiplier
        self.onShowFeedback = onShowFeedback
    }
    
    var body: some View {
        if shouldRemove {
            // 如果标记为移除，则显示空视图
            EmptyView()
        } else {
            VStack(alignment: .leading, spacing: 0) {
                // 图片
                ZStack {
                    // 使用低分辨率图片
                    AsyncImage(url: APIService.shared.getImageURL(filename: URL(string: image.storagePath)?.lastPathComponent ?? image.storagePath, lowResolution: true)) { phase in
                        switch phase {
                        case .empty:
                            if !imageLoaded {
                                Rectangle()
                                    .foregroundColor(.gray.opacity(0.3))
                                    .aspectRatio(1.0, contentMode: .fit)
                                    .overlay(
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle())
                                    )
                            }
                        case .success(let loadedImage):
                            loadedImage
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .onAppear {
                                    imageLoaded = true // 标记图片已加载
                                }
                        case .failure:
                            Rectangle()
                                .foregroundColor(.gray.opacity(0.3))
                                .aspectRatio(1.0, contentMode: .fit)
                                .overlay(
                                    Image(systemName: "exclamationmark.triangle")
                                        .foregroundColor(.gray)
                                )
                        @unknown default:
                            Rectangle()
                                .foregroundColor(.gray.opacity(0.3))
                                .aspectRatio(1.0, contentMode: .fit)
                        }
                    }
                    .frame(width: (geometry.size.width - 30) / 2)
                    .clipped()
                    .contentShape(Rectangle())
                    .onTapGesture {
                        // 只有点击时才导航到详情页
                        navigateToDetail = true
                    }
                    .onLongPressGesture(minimumDuration: 0.5) {
                        // 触发震动反馈
                        let generator = UIImpactFeedbackGenerator(style: .medium)
                        generator.impactOccurred()
                        
                        // 调用回调函数显示反馈菜单
                        onShowFeedback(image)
                    }
                    
                .fullScreenCover(isPresented: $navigateToDetail) {
                    ImageDetailView(imageId: image.imageId)
                        .environmentObject(userState)
                }
                }
                
                // 用户信息
                HStack {
                    // 头像
                    Image(image.avatar)
                        .resizable()
                        .scaledToFill()
                        .frame(width: 25, height: 25)
                        .clipShape(Circle())
                    
                    // 用户名
                    Text(image.username)
                        .font(Font.custom("PingFang SC", size: 12))
                        .foregroundColor(.black)
                        .lineLimit(1)
                    
                    Spacer()
                    
                    // 点赞按钮
                    Button(action: {
                        toggleLike()
                    }) {
                        Image(isLiked ? "5-5" : "5-4")
                            .resizable()
                            .frame(width: 16, height: 16)
                    }
                    
                    Text("\(likeCount)")
                        .font(Font.custom("PingFang SC", size: 12))
                        .foregroundColor(.gray)
                }
                .padding(.horizontal, 5)
                .padding(.vertical, 5)
                .frame(height: 35)
            }
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
            .onAppear {
                // 确保每次视图出现时都更新状态
                self.isLiked = image.liked
                self.likeCount = image.likeCount
            }
            .onChange(of: image.liked) { oldValue, newValue in
                isLiked = newValue
            }
            .onChange(of: image.likeCount) { oldValue, newValue in
                likeCount = newValue
            }
        }
    }
    
    private func toggleLike() {
        
        // 立即更新UI状态，提供即时反馈
        let newLikedState = !isLiked
        let newLikeCount = newLikedState ? likeCount + 1 : likeCount - 1
        
        // 先更新本地状态
        self.isLiked = newLikedState
        self.likeCount = newLikeCount
        
        // 然后发送API请求
        APIService.shared.likeImage(imageId: image.imageId, userId: userState.userId) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let (serverLikeCount, serverLiked)):
                    
                    // 如果服务器返回的状态与本地预期不一致，则更新为服务器状态
                    if self.isLiked != serverLiked || self.likeCount != serverLikeCount {
                        self.isLiked = serverLiked
                        self.likeCount = serverLikeCount
                    }
                    
                    // 发送通知更新图片状态，包含完整的点赞信息
                    NotificationCenter.default.post(
                        name: NSNotification.Name("RefreshHomeImages"),
                        object: nil,
                        userInfo: ["imageId": image.imageId, "liked": serverLiked, "likeCount": serverLikeCount]
                    )
                    
                    // 如果取消点赞，发送额外的通知以立即从喜欢列表中移除
                    if !serverLiked {
                        NotificationCenter.default.post(
                            name: NSNotification.Name("RemoveLikedImage"),
                            object: nil, 
                            userInfo: ["imageId": image.imageId]
                        )
                    }
                case .failure:
                    // 如果API请求失败，恢复到原始状态
                    self.isLiked = !newLikedState
                    self.likeCount = newLikedState ? newLikeCount - 1 : newLikeCount + 1
                }
            }
        }
    }
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
            .environmentObject(UserState())
    }
}

