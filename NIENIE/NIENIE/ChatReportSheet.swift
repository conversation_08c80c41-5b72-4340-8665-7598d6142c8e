import SwiftUI

// 聊天举报类型枚举
enum ChatReportType: String, CaseIterable {
    case advertisement = "广告"
    case harassment = "骚扰"
    case adultContent = "色情低俗"
    case inappropriateContent = "内容不适"
    case fraud = "诈骗"
    case illegal = "违法违规"
    case racism = "种族歧视"
    case minorRelated = "涉未成年"
    case spam = "垃圾信息"
    case other = "其他"
    
    // 获取对应的后端反馈类型
    var apiValue: String {
        return self.rawValue
    }
    
    // 获取图标名称
    var iconName: String {
        switch self {
        case .advertisement:
            return "megaphone.fill"
        case .harassment:
            return "person.fill.xmark"
        case .adultContent:
            return "eye.slash.fill"
        case .inappropriateContent:
            return "exclamationmark.octagon.fill"
        case .fraud:
            return "creditcard.fill"
        case .illegal:
            return "exclamationmark.shield.fill"
        case .racism:
            return "person.fill.badge.minus"
        case .minorRelated:
            return "person.fill.questionmark"
        case .spam:
            return "trash.fill"
        case .other:
            return "ellipsis.circle.fill"
        }
    }
    
    // 获取图标颜色
    var iconColor: Color {
        switch self {
        case .advertisement:
            return .blue
        case .harassment, .adultContent, .inappropriateContent, .illegal, .racism, .minorRelated:
            return .red
        case .fraud:
            return .orange
        case .spam, .other:
            return .gray
        }
    }
}

struct ChatReportSheet: View {
    let conversationId: String
    let userId: Int
    @Environment(\.dismiss) var dismiss
    @EnvironmentObject var userState: UserState
    @State private var showToast = false
    @State private var toastMessage = ""
    @State private var additionalText = "" // 可选的额外文本输入
    @State private var selectedReportType: ChatReportType? = nil
    @State private var keyboardHeight: CGFloat = 0
    @FocusState private var isTextFieldFocused: Bool

    // 将举报类型分组
    let reportGroups: [[ChatReportType]] = [
        [.advertisement, .harassment],
        [.fraud, .illegal],
        [.adultContent, .inappropriateContent],
        [.racism, .minorRelated],
        [.spam, .other]
    ]
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    Color.white.edgesIgnoringSafeArea(.all)

                    // 点击背景关闭键盘
                    Color.clear
                        .contentShape(Rectangle())
                        .onTapGesture {
                            isTextFieldFocused = false
                        }

                    VStack(spacing: 0) {
                        // 使用ScrollView包装所有内容，支持键盘适配
                        ScrollView {
                            VStack(spacing: 20) {
                                // 标题区域
                                VStack(spacing: 10) {
                                    Text("举报会话")
                                        .font(.system(size: 20, weight: .bold))
                                        .foregroundColor(.black)

                                    Text("请选择举报原因")
                                        .font(.system(size: 14))
                                        .foregroundColor(.gray)
                                }
                                .padding(.top, 20)

                                // 举报选项
                                VStack(spacing: 15) {
                                    ForEach(reportGroups, id: \.self) { group in
                                        HStack(spacing: 15) {
                                            ForEach(group, id: \.self) { type in
                                                Button(action: {
                                                    selectedReportType = type
                                                    // 选择举报类型时关闭键盘
                                                    isTextFieldFocused = false
                                                }) {
                                                    VStack(spacing: 8) {
                                                        Image(systemName: type.iconName)
                                                            .font(.system(size: 24))
                                                            .foregroundColor(type.iconColor)

                                                        Text(type.rawValue)
                                                            .font(.system(size: 14))
                                                            .foregroundColor(.black)
                                                            .lineLimit(1)
                                                    }
                                                    .frame(maxWidth: .infinity)
                                                    .padding(.vertical, 12)
                                                    .background(
                                                        selectedReportType == type ?
                                                        Color.blue.opacity(0.2) :
                                                        Color.gray.opacity(0.1)
                                                    )
                                                    .cornerRadius(10)
                                                    .overlay(
                                                        RoundedRectangle(cornerRadius: 10)
                                                            .stroke(
                                                                selectedReportType == type ?
                                                                Color.blue : Color.clear,
                                                                lineWidth: 2
                                                            )
                                                    )
                                                }
                                            }
                                        }
                                        .padding(.horizontal, 15)
                                    }
                                }

                                // 可选的文本输入框
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("补充说明（可选）")
                                        .font(.system(size: 14, weight: .medium))
                                        .foregroundColor(.black)

                                    TextEditor(text: $additionalText)
                                        .focused($isTextFieldFocused)
                                        .frame(height: 100)
                                        .padding(8)
                                        .background(Color.gray.opacity(0.1))
                                        .cornerRadius(8)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 8)
                                                .stroke(
                                                    isTextFieldFocused ? Color.blue : Color.gray.opacity(0.3),
                                                    lineWidth: isTextFieldFocused ? 2 : 1
                                                )
                                        )
                                }
                                .padding(.horizontal, 15)

                                // 提交按钮
                                Button(action: {
                                    if let reportType = selectedReportType {
                                        isTextFieldFocused = false // 提交前关闭键盘
                                        handleFeedback(reportType)
                                    }
                                }) {
                                    Text("提交举报")
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.white)
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, 12)
                                        .background(selectedReportType != nil ? Color.red : Color.gray)
                                        .cornerRadius(8)
                                }
                                .disabled(selectedReportType == nil)
                                .padding(.horizontal, 15)

                                // 底部间距，确保内容不被键盘遮挡
                                Color.clear
                                    .frame(height: keyboardHeight > 0 ? keyboardHeight + 20 : 50)
                            }
                        }
                        .scrollDismissesKeyboard(.interactively) // iOS 16+ 支持滚动时关闭键盘
                    }

                    // Toast提示
                    if showToast {
                        VStack {
                            Spacer()
                            Text(toastMessage)
                                .font(.system(size: 14))
                                .foregroundColor(.white)
                                .padding(.horizontal, 20)
                                .padding(.vertical, 10)
                                .background(
                                    RoundedRectangle(cornerRadius: 20)
                                        .fill(Color.black.opacity(0.7))
                                )
                                .padding(.bottom, keyboardHeight > 0 ? keyboardHeight + 20 : 50)
                        }
                        .transition(.opacity)
                        .animation(.easeInOut, value: showToast)
                        .zIndex(999)
                    }
                }
            }
            .navigationTitle("举报")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("取消") {
                    dismiss()
                }
            )
            .onReceive(NotificationCenter.default.publisher(for: UIResponder.keyboardWillShowNotification)) { notification in
                if let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        keyboardHeight = keyboardFrame.height
                    }
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: UIResponder.keyboardWillHideNotification)) { _ in
                withAnimation(.easeInOut(duration: 0.3)) {
                    keyboardHeight = 0
                }
            }
        }
    }
    
    // 处理反馈
    private func handleFeedback(_ type: ChatReportType) {
        // 发送内容反馈到后端
        // 使用会话ID作为image_id，额外文本作为image_path
        APIService.shared.submitFeedback(
            userId: userId,
            imageId: Int(conversationId.split(separator: "_").first ?? "0") ?? 0, // 从会话ID中提取数字作为ID
            feedbackType: type.apiValue,
            imagePath: additionalText.isEmpty ? nil : additionalText
        ) { result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    showToast(message: "举报成功，我们会尽快处理")
                case .failure:
                    showToast(message: "举报失败，请稍后再试")
                }
            }
        }
    }
    
    // 显示Toast提示
    private func showToast(message: String) {
        toastMessage = message
        withAnimation {
            showToast = true
        }
        
        // 1.5秒后关闭Toast并关闭页面
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            withAnimation {
                showToast = false
            }
            
            // 关闭页面
            dismiss()
        }
    }
}

// 预览
struct ChatReportSheet_Previews: PreviewProvider {
    static var previews: some View {
        ChatReportSheet(conversationId: "1_2", userId: 1)
            .environmentObject(UserState())
    }
}
