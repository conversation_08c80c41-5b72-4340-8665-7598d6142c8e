import SwiftUI
import PhotosUI
import Vision

// MARK: - 深度补光主视图
struct DepthLightingView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userState: UserState
    
    // 图片相关状态
    @State private var selectedImage: UIImage?
    @State private var depthImage: UIImage?
    @State private var lightedImage: UIImage?
    @State private var showImagePicker = false
    @State private var showDepthMap = false
    
    // 光源相关状态
    @State private var lightSource: LightSource?
    @State private var selectedColor: Color = .white
    @State private var showColorPicker = false
    
    // UI状态
    @State private var processingState: DepthLightingProcessingState = .idle
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var imageFrame: CGRect = .zero
    @State private var humanMask: VNPixelBufferObservation? // 存储人物遮罩
    @State private var isPreparingLightSource = false // 光源准备状态
    
    // 处理器
    @StateObject private var depthProcessor = DepthProcessor()
    @StateObject private var lightingRenderer = LightingRenderer()
    
    // 防抖定时器
    @State private var renderTimer: Timer?
    @State private var lastRenderTime = Date()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部导航栏
                topNavigationBar
                
                // 主内容区域
                ScrollView {
                    VStack(spacing: 20) {
                        // 图片显示区域
                        imageDisplaySection
                        
                        // 控制面板
                        if selectedImage != nil {
                            controlPanel
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 20)
                }
            }
            .background(Color.white.ignoresSafeArea())
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedImage)
                .onDisappear {
                    if selectedImage != nil {
                        processSelectedImage()
                    }
                }
        }
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
    }
    
    // MARK: - 顶部导航栏
    private var topNavigationBar: some View {
        HStack {
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.black)
            }
            
            Spacer()
            
            Text("深度补光")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.black)
            
            Spacer()
            
            Button(action: {
                showImagePicker = true
            }) {
                Image(systemName: "photo")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.black)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.white)
    }
    
    // MARK: - 图片显示区域
    private var imageDisplaySection: some View {
        VStack(spacing: 16) {
            if let image = selectedImage {
                imageWithLightingOverlay(image: image)
                    .frame(maxWidth: .infinity)
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )
                
                // 功能按钮行
                HStack(spacing: 16) {
                    // 添加光源按钮
                    Button(action: addLightSourceToCenter) {
                        HStack(spacing: 8) {
                            Image(systemName: "plus.circle.fill")
                            Text("添加光源")
                        }
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.blue)
                        .cornerRadius(20)
                    }
                    .disabled(lightSource != nil)
                    
                    // 显示深度图按钮
                    Button(action: {
                        showDepthMap.toggle()
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: showDepthMap ? "eye.slash" : "eye")
                            Text(showDepthMap ? "隐藏深度图" : "显示深度图")
                        }
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.gray)
                        .cornerRadius(20)
                    }
                    .disabled(depthImage == nil)
                    
                    Spacer()
                }
                
            } else {
                // 空状态 - 提示上传图片
                VStack(spacing: 16) {
                    Image(systemName: "photo.badge.plus")
                        .font(.system(size: 60))
                        .foregroundColor(.gray)
                    
                    Text("点击上传图片开始补光")
                        .font(.system(size: 16))
                        .foregroundColor(.gray)
                    
                    Button(action: {
                        showImagePicker = true
                    }) {
                        Text("选择图片")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(Color.blue)
                            .cornerRadius(25)
                    }
                }
                .frame(height: 300)
                .frame(maxWidth: .infinity)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
            }
            
            // 处理状态指示器
            if (processingState != .idle && processingState != .completed) || isPreparingLightSource {
                HStack(spacing: 12) {
                    ProgressView()
                        .scaleEffect(0.8)

                    Text(processingStateText)
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                }
                .padding(.vertical, 8)
            }
        }
    }
    
    private var processingStateText: String {
        if isPreparingLightSource {
            return "光源准备中..."
        }

        switch processingState {
        case .idle:
            return ""
        case .loadingDepth:
            return "正在生成深度图..."
        case .processingLighting:
            return "正在处理光照..."
        case .completed:
            return ""
        }
    }

    // MARK: - 控制面板
    private var controlPanel: some View {
        VStack(spacing: 20) {
            if let currentLightSource = lightSource {
                // 光源属性控制
                VStack(spacing: 16) {
                    Text("光源属性")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.black)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    // 强度滑块
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("强度")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.black)
                            Spacer()
                            Text(String(format: "%.1f", currentLightSource.intensity))
                                .font(.system(size: 14))
                                .foregroundColor(.gray)
                        }

                        Slider(
                            value: Binding(
                                get: { currentLightSource.intensity },
                                set: { newValue in
                                    if var updatedLightSource = lightSource {
                                        updatedLightSource.intensity = newValue
                                        lightSource = updatedLightSource
                                        // 使用防抖渲染
                                        scheduleRender()
                                    }
                                }
                            ),
                            in: 0...1,
                            step: 0.05,
                            onEditingChanged: { editing in
                                if !editing {
                                    // 滑块编辑结束时立即渲染
                                    renderLighting()
                                }
                            }
                        )
                        .accentColor(.blue)
                    }

                    // 照射距离通过拖拽箭头长度控制，移除滑块

                    // 光锥角度滑块
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("光锥角度")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.black)
                            Spacer()
                            Text(String(format: "%.0f°", currentLightSource.coneAngle))
                                .font(.system(size: 14))
                                .foregroundColor(.gray)
                        }

                        Slider(
                            value: Binding(
                                get: { currentLightSource.coneAngle },
                                set: { newValue in
                                    if var updatedLightSource = lightSource {
                                        updatedLightSource.coneAngle = newValue
                                        lightSource = updatedLightSource
                                        scheduleRender()
                                    }
                                }
                            ),
                            in: 10...120,
                            step: 5,
                            onEditingChanged: { editing in
                                if !editing {
                                    renderLighting()
                                }
                            }
                        )
                        .accentColor(.blue)
                    }

                    // 颜色选择器
                    VStack(alignment: .leading, spacing: 8) {
                        Text("颜色")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.black)

                        HStack {
                            Circle()
                                .fill(currentLightSource.color)
                                .frame(width: 30, height: 30)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white, lineWidth: 2)
                                )

                            ColorPicker("", selection: Binding(
                                get: { lightSource?.color ?? .white },
                                set: { newColor in
                                    if var updatedLightSource = lightSource {
                                        updatedLightSource.color = newColor
                                        lightSource = updatedLightSource
                                        scheduleRender()
                                    }
                                }
                            ))
                            .labelsHidden()
                            .frame(width: 100)

                            Spacer()
                        }
                    }

                    // 删除光源按钮
                    Button(action: {
                        lightSource = nil
                        lightedImage = selectedImage
                    }) {
                        HStack {
                            Image(systemName: "trash")
                            Text("删除光源")
                        }
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.red)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(20)
                    }
                }
                .padding(16)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
            }
        }
    }

    // MARK: - 方法实现

    private func processSelectedImage() {
        guard let image = selectedImage else { return }

        processingState = .loadingDepth

        depthProcessor.generateDepthMap(from: image) { generatedDepthImage in
            DispatchQueue.main.async {
                processingState = .completed
                depthImage = generatedDepthImage
                lightedImage = image // 初始显示原图
            }
        }
    }

    private func addLightSourceToCenter() {
        guard let image = selectedImage else {
            return
        }

        // 设置光源准备状态
        isPreparingLightSource = true

        // 计算图片在UI中的实际显示尺寸和位置
        let imageAspectRatio = image.size.width / image.size.height
        let frameAspectRatio = imageFrame.width / imageFrame.height

        var actualImageFrame: CGRect

        if imageAspectRatio > frameAspectRatio {
            // 图片更宽，以宽度为准
            let displayHeight = imageFrame.width / imageAspectRatio
            let yOffset = (imageFrame.height - displayHeight) / 2
            actualImageFrame = CGRect(
                x: 0,
                y: yOffset,
                width: imageFrame.width,
                height: displayHeight
            )
        } else {
            // 图片更高，以高度为准
            let displayWidth = imageFrame.height * imageAspectRatio
            let xOffset = (imageFrame.width - displayWidth) / 2
            actualImageFrame = CGRect(
                x: xOffset,
                y: 0,
                width: displayWidth,
                height: imageFrame.height
            )
        }

   

        // 计算图片中心位置（在实际显示区域内）
        let centerPosition = CGPoint(
            x: actualImageFrame.midX,
            y: actualImageFrame.midY
        )

      

        // 先进行人物分割以获取深度信息
        performHumanSegmentation(image: image) { mask in
            DispatchQueue.main.async { [self] in
                // 清除光源准备状态
                self.isPreparingLightSource = false

                // 计算人物深度
                var humanDepth: Float = 0.5 // 默认深度
                if let depthMap = self.depthImage, let mask = mask {
                    humanDepth = LightSource.calculateHumanDepth(from: depthMap, humanMask: mask)
                    print("👤 [DEBUG] 计算得到的人物深度: \(humanDepth)")
                }

                // 确保lightedImage保持为原图，避免显示问题
                if self.lightedImage == nil {
                    self.lightedImage = image
                }

                // 创建聚光灯光源
                // 计算与radius=3.0匹配的箭头长度：30 + (3.0-1.0)/9.0 * 90 = 50
                let defaultArrowLength: CGFloat = 50
                self.lightSource = LightSource(
                    position: centerPosition,
                    direction: CGPoint(x: 0, y: defaultArrowLength), // 默认向下照射，长度匹配radius
                    color: .white,
                    intensity: 0.8,
                    radius: 3.0,
                    coneAngle: 45.0,
                    depth: humanDepth
                )
                self.scheduleRender()
            }
        }
    }

    private func performHumanSegmentation(image: UIImage, completion: @escaping (VNPixelBufferObservation?) -> Void) {
        DispatchQueue.global(qos: .userInitiated).async {
            let segmentationRequest = VNGeneratePersonSegmentationRequest { request, error in
                if let error = error {
                    print("❌ [DEBUG] 人体分割错误: \(error.localizedDescription)")
                    completion(nil)
                    return
                }

                guard let observations = request.results as? [VNPixelBufferObservation],
                      let observation = observations.first else {
                    print("❌ [DEBUG] 未检测到人体")
                    completion(nil)
                    return
                }

                completion(observation)
            }

            segmentationRequest.qualityLevel = .balanced

            guard let cgImage = image.cgImage else {
                completion(nil)
                return
            }

            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])

            do {
                try handler.perform([segmentationRequest])
            } catch {
                print("❌ [DEBUG] 处理图像时出错: \(error.localizedDescription)")
                completion(nil)
            }
        }
    }

    private func scheduleRender() {
        // 取消之前的定时器
        renderTimer?.invalidate()

        // 设置新的定时器，延迟渲染以避免频繁更新
        renderTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { _ in
            self.renderLighting()
        }
    }

    private func renderLighting() {
        guard let originalImage = selectedImage,
              let depthMap = depthImage,
              let currentLightSource = lightSource else {
            print("❌ [DEBUG] 渲染失败：缺少必要数据")
            return
        }

        // 确保在渲染开始前lightedImage不为nil
        if lightedImage == nil {
            lightedImage = originalImage
        }

        print("🎨 [DEBUG] 开始渲染光照效果")
        print("🖼️ [DEBUG] 原图尺寸: \(originalImage.size)")
        print("🗺️ [DEBUG] 深度图尺寸: \(depthMap.size)")
        print("💡 [DEBUG] 光源位置: (\(currentLightSource.position.x), \(currentLightSource.position.y))")
        print("⚡ [DEBUG] 光源强度: \(currentLightSource.intensity), 半径: \(currentLightSource.radius)")

        // 移除处理状态提示

        // 需要将UI坐标转换为图片坐标
        var adjustedLightSource = currentLightSource

        // 计算UI坐标到图片坐标的转换比例
        let scaleX = originalImage.size.width / imageFrame.width
        let scaleY = originalImage.size.height / imageFrame.height

        print("📏 [DEBUG] 坐标转换比例: scaleX=\(scaleX), scaleY=\(scaleY)")

        // 转换光源位置到图片坐标系
        adjustedLightSource.position = CGPoint(
            x: currentLightSource.position.x * scaleX,
            y: currentLightSource.position.y * scaleY
        )

        print("🔄 [DEBUG] 转换后光源位置: (\(adjustedLightSource.position.x), \(adjustedLightSource.position.y))")

        // 保存当前的lightedImage作为备份
        let backupImage = lightedImage ?? originalImage

        lightingRenderer.renderLighting(
            originalImage: originalImage,
            depthMap: depthMap,
            lightSources: [adjustedLightSource]
        ) { result in
            DispatchQueue.main.async {
                // 移除处理状态提示
                if let result = result {
                    print("✅ [DEBUG] 光照渲染成功")
                    lightedImage = result
                } else {
                    print("❌ [DEBUG] 光照渲染失败，恢复备份图片")
                    lightedImage = backupImage
                }
            }
        }
    }

    // MARK: - 图片与光源叠加层
    private func imageWithLightingOverlay(image: UIImage) -> some View {
        ZStack {
            // 显示当前图片（原图或深度图）
            let displayImage = showDepthMap ? (depthImage ?? image) : (lightedImage ?? image)

            // 调试信息
            let _ = print("🖼️ [DEBUG] 显示图片: showDepthMap=\(showDepthMap), lightedImage=\(lightedImage != nil ? "存在" : "nil"), 使用图片=\(displayImage === image ? "原图" : "处理后图片")")

            Image(uiImage: displayImage)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(maxWidth: .infinity, maxHeight: 400)
                .clipped()
                .background(
                    GeometryReader { geometry in
                        Color.clear
                            .onAppear {
                                imageFrame = geometry.frame(in: .local)
                            }
                            .onChange(of: geometry.frame(in: .local)) { _, newFrame in
                                imageFrame = newFrame
                            }
                    }
                )

            // 聚光灯指示器 - 包含光源和方向箭头
            if let currentLightSource = lightSource, !showDepthMap {
                lightSourceOverlay(lightSource: currentLightSource)
            }
        }
    }

    // MARK: - 光源叠加层
    private func lightSourceOverlay(lightSource: LightSource) -> some View {
        ZStack {
            // 聚光灯锥形显示
            let arrowEndPosition = CGPoint(
                x: lightSource.position.x + lightSource.direction.x,
                y: lightSource.position.y + lightSource.direction.y
            )

            // 计算光锥的边界点
            let directionLength = sqrt(lightSource.direction.x * lightSource.direction.x + lightSource.direction.y * lightSource.direction.y)

            if directionLength > 0 {
                let normalizedDirection = CGPoint(
                    x: lightSource.direction.x / directionLength,
                    y: lightSource.direction.y / directionLength
                )

                // 计算垂直于方向的向量
                let perpendicular = CGPoint(x: -normalizedDirection.y, y: normalizedDirection.x)

                // 根据光锥角度计算锥形宽度
                let coneRadius = directionLength * tan(Double(lightSource.coneAngle) * Double.pi / 180.0 / 2.0)

                // 光锥形状 - 扇形（不画底边）
                Path { path in
                    path.move(to: lightSource.position)

                    let leftPoint = CGPoint(
                        x: arrowEndPosition.x + perpendicular.x * coneRadius,
                        y: arrowEndPosition.y + perpendicular.y * coneRadius
                    )
                    let rightPoint = CGPoint(
                        x: arrowEndPosition.x - perpendicular.x * coneRadius,
                        y: arrowEndPosition.y - perpendicular.y * coneRadius
                    )

                    path.addLine(to: leftPoint)
                    path.move(to: lightSource.position)
                    path.addLine(to: rightPoint)
                }
                .fill(lightSource.color.opacity(0.2))
                .stroke(lightSource.color.opacity(0.6), lineWidth: 2)
            }

            // 光源中心点
            Circle()
                .fill(lightSource.color.opacity(0.9))
                .frame(width: 16, height: 16)
                .overlay(
                    Circle()
                        .stroke(Color.white, lineWidth: 2)
                        .frame(width: 16, height: 16)
                )
                .position(lightSource.position)
                .gesture(lightSourceDragGesture())

            // 方向箭头线条
            Path { path in
                path.move(to: lightSource.position)
                path.addLine(to: arrowEndPosition)
            }
            .stroke(Color.yellow, lineWidth: 3)

            // 箭头头部
            Image(systemName: "arrowtriangle.down.fill")
                .foregroundColor(.yellow)
                .font(.system(size: 16))
                .rotationEffect(.degrees(Double(atan2(lightSource.direction.y, lightSource.direction.x)) * 180.0 / Double.pi + 90.0))
                .position(arrowEndPosition)
                .gesture(arrowDragGesture())
        }
    }

    // MARK: - 拖拽手势
    private func lightSourceDragGesture() -> some Gesture {
        DragGesture()
            .onChanged { value in
                if var updatedLightSource = lightSource {
                    print("🖱️ [DEBUG] 拖拽光源位置: (\(value.location.x), \(value.location.y))")

                    // 直接使用拖拽位置，不需要额外的坐标转换
                    let newPosition = value.location

                    // 确保光源位置在图片范围内
                    let clampedX = max(15, min(newPosition.x, imageFrame.width - 15))
                    let clampedY = max(15, min(newPosition.y, imageFrame.height - 15))
                    updatedLightSource.position = CGPoint(x: clampedX, y: clampedY)

                    print("✅ [DEBUG] 光源新位置: (\(clampedX), \(clampedY))")
                    lightSource = updatedLightSource
                }
            }
            .onEnded { _ in
                print("🎯 [DEBUG] 光源拖拽结束，开始渲染")
                scheduleRender()
            }
    }

    private func arrowDragGesture() -> some Gesture {
        DragGesture()
            .onChanged { value in
                if var updatedLightSource = lightSource {
                    print("🖱️ [DEBUG] 拖拽箭头位置: (\(value.location.x), \(value.location.y))")

                    // 计算新的方向向量（从光源位置到拖拽位置）
                    let newDirection = CGPoint(
                        x: value.location.x - updatedLightSource.position.x,
                        y: value.location.y - updatedLightSource.position.y
                    )

                    // 计算箭头长度，同时控制方向和照射距离
                    let length = sqrt(newDirection.x * newDirection.x + newDirection.y * newDirection.y)
                    let maxLength: CGFloat = 120  // 增加最大长度
                    let minLength: CGFloat = 30

                    if length > minLength {
                        let normalizedLength = min(length, maxLength)
                        // 保持方向，但限制长度
                        updatedLightSource.direction = CGPoint(
                            x: (newDirection.x / length) * normalizedLength,
                            y: (newDirection.y / length) * normalizedLength
                        )

                        // 箭头长度控制照射距离：30-120像素对应1-10的照射距离
                        let radiusRange: Float = 9.0  // 10 - 1 = 9
                        let lengthRange: CGFloat = maxLength - minLength  // 120 - 30 = 90
                        let normalizedLengthRatio = (normalizedLength - minLength) / lengthRange
                        updatedLightSource.radius = 1.0 + Float(normalizedLengthRatio) * radiusRange

                        print("✅ [DEBUG] 新方向: (\(updatedLightSource.direction.x), \(updatedLightSource.direction.y))")
                        print("📏 [DEBUG] 箭头长度: \(normalizedLength), 照射距离: \(updatedLightSource.radius)")
                        lightSource = updatedLightSource
                    }
                }
            }
            .onEnded { _ in
                print("🎯 [DEBUG] 方向拖拽结束，开始渲染")
                scheduleRender()
            }
    }
}
