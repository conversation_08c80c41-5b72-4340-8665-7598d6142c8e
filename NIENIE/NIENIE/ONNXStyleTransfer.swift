import Foundation
import UIKit
import CoreVideo
import Accelerate

// ONNX Runtime风格转换处理器
class ONNXStyleTransfer {
    
    enum StyleType: String, CaseIterable {
        case hayao = "AnimeGANv3_Hayao_36"
        case shinkai = "AnimeGANv3_Shinkai_40"
        case portraitSketch = "AnimeGANv3_PortraitSketch_25"
        case cute = "AnimeGANv3_tiny_Cute"
        
        var displayName: String {
            switch self {
            case .hayao:
                return "宫崎骏风格"
            case .shinkai:
                return "新海诚风格"
            case .portraitSketch:
                return "素描风格"
            case .cute:
                return "可爱风格"
            }
        }
        
        var modelFileName: String {
            return self.rawValue + ".onnx"
        }
    }
    
    private var session: ORTSession?
    private var environment: ORTEnv?
    private var currentModelType: StyleType?

    init() {
        do {
            // 初始化ONNX Runtime环境
            environment = try ORTEnv(loggingLevel: ORTLoggingLevel.warning)
        } catch {
            print("Failed to create ONNX Runtime environment: \(error)")
        }
    }

    deinit {
        cleanupMemory()
        environment = nil
        print("🗑️ ONNXStyleTransfer deinitialized")
    }
    
    func loadModel(styleType: StyleType) -> Bool {
        guard let environment = environment else { return false }

        // 如果已经加载了相同的模型，直接返回
        if currentModelType == styleType && session != nil {
            return true
        }

        // 释放之前的会话
        session = nil

        guard let modelPath = Bundle.main.path(forResource: styleType.rawValue, ofType: "onnx") else {
            print("Model file not found: \(styleType.modelFileName)")
            return false
        }

        do {
            // 创建会话选项
            let sessionOptions = try ORTSessionOptions()

            // 创建会话
            session = try ORTSession(env: environment, modelPath: modelPath, sessionOptions: sessionOptions)

            currentModelType = styleType
            print("Successfully loaded model: \(styleType.displayName)")
            return true
        } catch {
            print("Failed to create ONNX Runtime session for \(styleType.displayName): \(error)")
            return false
        }
    }
    
    func processImage(_ image: UIImage, styleType: StyleType) -> UIImage? {
        // 确保模型已加载
        guard loadModel(styleType: styleType) else {
            print("Failed to load model for \(styleType.displayName)")
            return nil
        }

        guard session != nil else {
            print("No active session")
            return nil
        }

        // 预处理图像
        guard let (inputData, inputShape, originalSize) = preprocessImage(image, for: styleType) else {
            print("Failed to preprocess image")
            cleanupMemory()
            return nil
        }

        // 运行推理
        guard let outputData = runInference(inputData: inputData, inputShape: inputShape) else {
            print("Failed to run inference")
            cleanupMemory()
            return nil
        }

        // 后处理
        let result = postprocessOutput(outputData, originalSize: originalSize, inputSize: CGSize(width: CGFloat(inputShape[2]), height: CGFloat(inputShape[1])))

        // 处理完成后立即清理内存
        cleanupMemory()

        return result
    }
    
    private func preprocessImage(_ image: UIImage, for styleType: StyleType) -> (Data, [Int64], CGSize)? {
        let originalSize = image.size
        print("🖼️ Original image size: \(originalSize.width) x \(originalSize.height)")

        // 计算目标尺寸
        let targetSize = calculateTargetSize(width: Int(originalSize.width), height: Int(originalSize.height), for: styleType)
        print("🎯 Target size: \(Int(targetSize.width)) x \(Int(targetSize.height))")

        // 调整图像尺寸
        guard let resizedImage = resizeImageToTargetSize(image, to: targetSize) else {
            print("❌ Failed to resize image")
            return nil
        }

        // 转换为RGB数据
        guard let cgImage = resizedImage.cgImage else {
            print("❌ Failed to get CGImage")
            return nil
        }

        let width = Int(targetSize.width)
        let height = Int(targetSize.height)
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8

        print("📊 Processing image data: \(width) x \(height) x 3 channels")

        var pixelData = [UInt8](repeating: 0, count: width * height * bytesPerPixel)

        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.noneSkipLast.rawValue
        )

        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        // 转换为浮点数并归一化到[-1, 1] (类似 cv2.cvtColor + normalization)
        var floatData = [Float]()
        floatData.reserveCapacity(width * height * 3)

        // 按HWC格式排列 (Height, Width, Channel) - 模型期望的格式
        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = (y * width + x) * bytesPerPixel
                // RGB 通道，归一化到 [-1, 1] (/ 127.5 - 1.0)
                let r = Float(pixelData[pixelIndex]) / 127.5 - 1.0
                let g = Float(pixelData[pixelIndex + 1]) / 127.5 - 1.0
                let b = Float(pixelData[pixelIndex + 2]) / 127.5 - 1.0

                floatData.append(r)
                floatData.append(g)
                floatData.append(b)
            }
        }

        print("✅ Normalized pixel data: \(floatData.count) values (expected: \(width * height * 3))")

        // 输入形状: [1, height, width, 3] - HWC格式
        let inputShape: [Int64] = [1, Int64(height), Int64(width), 3]
        print("📐 Input tensor shape: \(inputShape)")

        let data = Data(bytes: floatData, count: floatData.count * MemoryLayout<Float>.size)

        return (data, inputShape, originalSize)
    }
    
    private func calculateTargetSize(width: Int, height: Int, for styleType: StyleType) -> CGSize {
        var targetWidth = width
        var targetHeight = height

        // 如果宽或高超过640，等比例缩放
        if width > 640 || height > 640 {
            let maxDimension = max(width, height)
            let scale = 640.0 / Double(maxDimension)
            targetWidth = Int(Double(width) * scale)
            targetHeight = Int(Double(height) * scale)
            print("🔄 Scaling down from \(width)x\(height) to \(targetWidth)x\(targetHeight)")
        }

        // 根据模型类型确定对齐要求
        let alignment: Int
        if styleType == .cute {
            // tiny模型需要16的倍数
            alignment = 16
        } else {
            // 其他模型需要8的倍数
            alignment = 8
        }

        // 确保宽高是对应倍数
        targetWidth = targetWidth - (targetWidth % alignment)
        targetHeight = targetHeight - (targetHeight % alignment)

        // 确保最小尺寸
        targetWidth = max(targetWidth, alignment)
        targetHeight = max(targetHeight, alignment)

        print("📏 Final size after \(alignment)-pixel alignment: \(targetWidth)x\(targetHeight)")

        return CGSize(width: targetWidth, height: targetHeight)
    }

    private func resizeImageToTargetSize(_ image: UIImage, to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 1.0)
        image.draw(in: CGRect(origin: .zero, size: size))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        return resizedImage
    }
    

    
    private func runInference(inputData: Data, inputShape: [Int64]) -> Data? {
        guard let session = session else { return nil }

        do {
            // 将输入数据转换为Float数组
            let floatCount = inputData.count / MemoryLayout<Float>.size
            var floatArray = [Float](repeating: 0, count: floatCount)
            inputData.withUnsafeBytes { bytes in
                let floatPointer = bytes.bindMemory(to: Float.self)
                for i in 0..<floatCount {
                    floatArray[i] = floatPointer[i]
                }
            }

            // 创建输入张量
            let inputTensor = try ORTValue(tensorData: NSMutableData(data: Data(bytes: floatArray, count: floatArray.count * MemoryLayout<Float>.size)),
                                         elementType: ORTTensorElementDataType.float,
                                         shape: inputShape.map { NSNumber(value: $0) })

            // 获取输入输出名称
            let inputNames = try session.inputNames()
            let outputNames = try session.outputNames()

            guard let inputName = inputNames.first,
                  let outputName = outputNames.first else {
                print("Failed to get input/output names")
                return nil
            }

            // 运行推理
            let outputs = try session.run(withInputs: [inputName: inputTensor],
                                        outputNames: Set([outputName]),
                                        runOptions: nil)

            guard let outputTensor = outputs[outputName] else {
                print("Failed to get output tensor")
                return nil
            }

            // 获取输出数据
            let outputData = try outputTensor.tensorData()
            return outputData as Data

        } catch {
            print("Failed to run inference: \(error)")
            return nil
        }
    }

    private func postprocessOutput(_ data: Data, originalSize: CGSize, inputSize: CGSize) -> UIImage? {
        let width = Int(inputSize.width)
        let height = Int(inputSize.height)

        print("🔄 Postprocessing output: \(width) x \(height)")

        // 将输出数据转换为浮点数组
        let floatCount = data.count / MemoryLayout<Float>.size
        var floatArray = [Float](repeating: 0, count: floatCount)
        data.withUnsafeBytes { bytes in
            let floatPointer = bytes.bindMemory(to: Float.self)
            for i in 0..<floatCount {
                floatArray[i] = floatPointer[i]
            }
        }

        print("📊 Output data: \(floatArray.count) values (expected: \(width * height * 3))")

        // 转换为像素数据 (假设输出也是HWC格式)
        var pixelData = [UInt8](repeating: 0, count: width * height * 4)

        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = (y * width + x) * 4
                let dataIndex = (y * width + x) * 3

                // HWC格式：RGB值是连续的
                let r = floatArray[dataIndex]
                let g = floatArray[dataIndex + 1]
                let b = floatArray[dataIndex + 2]

                // 反归一化：从[-1, 1]转换到[0, 255]
                pixelData[pixelIndex] = UInt8(max(0, min(255, (r + 1.0) / 2.0 * 255.0)))
                pixelData[pixelIndex + 1] = UInt8(max(0, min(255, (g + 1.0) / 2.0 * 255.0)))
                pixelData[pixelIndex + 2] = UInt8(max(0, min(255, (b + 1.0) / 2.0 * 255.0)))
                pixelData[pixelIndex + 3] = 255 // Alpha
            }
        }

        // 创建CGImage
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: width * 4,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.noneSkipLast.rawValue
        )

        guard let cgImage = context?.makeImage() else {
            print("❌ Failed to create CGImage")
            return nil
        }

        print("✅ Successfully created processed image")

        // 调整回原始尺寸
        UIGraphicsBeginImageContextWithOptions(originalSize, false, 1.0)
        let image = UIImage(cgImage: cgImage)
        image.draw(in: CGRect(origin: .zero, size: originalSize))
        let finalImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        print("🎯 Resized back to original size: \(originalSize.width) x \(originalSize.height)")

        return finalImage
    }

    // MARK: - Memory Management

    private func cleanupMemory() {
        print("🧹 Cleaning up memory...")

        // 清理会话
        session = nil

        // 强制垃圾回收
        autoreleasepool {
            // 这个空的 autoreleasepool 会强制释放临时对象
        }

        // 建议系统进行内存清理
        DispatchQueue.global(qos: .background).async {
            // 在后台线程执行内存压力释放
            if #available(iOS 13.0, *) {
                // iOS 13+ 可以使用内存压力事件
                let source = DispatchSource.makeMemoryPressureSource(eventMask: .all, queue: .global())
                source.setEventHandler {
                    // 内存压力处理
                }
                source.resume()
                source.cancel()
            }
        }

        print("✅ Memory cleanup completed")
    }
}
