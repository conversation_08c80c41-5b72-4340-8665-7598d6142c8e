import SwiftUI

struct UserProfileView: View {
    @EnvironmentObject var userState: UserState
    @Environment(\.dismiss) var dismiss
    
    // Properties for the user being viewed
    let profileUserId: Int
    let profileUsername: String
    let profileAvatar: String

    @State private var userImages: [ImageData] = []
    @State private var isLoading = false
    @State private var errorMessage: String? = nil
    @State private var userImagesSkip = 0
    @State private var hasMoreUserImages = true
    @State private var isLoadingMoreUserImages = false
    @State private var showChat = false // 修改为显示聊天的状态
    
    private var gridLayout: [GridItem] {
        [
            GridItem(.flexible(), spacing: 16),
            GridItem(.flexible(), spacing: 16)
        ]
    }
    
    var body: some View {
        // Use a ZStack as the root, not GeometryReader
        ZStack(alignment: .top) {
            // Background Gradient
            LinearGradient(
                stops: [
                    Gradient.Stop(color: Color(red: 0.69, green: 1, blue: 0.91), location: 0.00),
                    Gradient.Stop(color: Color(red: 1, green: 0.9, blue: 0.54), location: 0.80),
                ],
                startPoint: UnitPoint(x: 0.34, y: 0),
                endPoint: UnitPoint(x: 0.73, y: 1.18)
            )
            .frame(height: 220)
            .ignoresSafeArea()

            VStack(spacing: 0) {
                // Custom Top Navigation
                HStack {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.black)
                            .font(.system(size: 18))
                            .padding()
                    }
                    Spacer()
                }
                .frame(height: 44)
                
                // User Info
                HStack(alignment: .top, spacing: 16) {
                    Image(profileAvatar)
                        .resizable()
                        .scaledToFill()
                        .frame(width: 70, height: 70)
                        .clipShape(Circle())
                        .overlay(Circle().stroke(Color.white, lineWidth: 2))
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(profileUsername)
                            .font(Font.custom("PingFang SC", size: 18).weight(.bold))
                            .foregroundColor(.black)
                        
                        Text("用户ID: \(profileUserId)")
                            .font(Font.custom("PingFang SC", size: 14))
                            .foregroundColor(Color.gray)
                            .padding(.top, 6)
                    }
                    
                    Spacer()
                    
                    // 私信按钮
                    Button(action: {
                        showChat = true
                    }) {
                        Text("私信")
                            .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                            .foregroundColor(.black)
                            .padding(.vertical, 8)
                            .padding(.horizontal, 16)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color(red: 1, green: 0.9, blue: 0.4), Color(red: 1, green: 0.8, blue: 0.2)]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .cornerRadius(16)
                            .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                    }
                    .offset(x: -10, y: 20)
                }
                .padding(.horizontal, 16)
                .padding(.top, 20)
                
                // "作品" Title
                HStack {
                    Text("作品")
                        .font(Font.custom("PingFang SC", size: 16).weight(.bold))
                        .foregroundColor(.black)
                    Spacer()
                }
                .padding(.horizontal, 16)
                .padding(.top, 50)
                .padding(.bottom, 10)

                // Content Area
                ScrollView {
                    if isLoading && userImages.isEmpty {
                        ProgressView("加载中...")
                            .padding(.top, 50)
                    } else if let error = errorMessage {
                        VStack {
                            Text(error)
                            Button("重新加载") {
                                loadUserData()
                            }
                        }
                        .padding(.top, 50)
                    } else if userImages.isEmpty {
                        Text("该用户暂无作品")
                            .font(Font.custom("PingFang SC", size: 16))
                            .foregroundColor(.gray)
                            .padding(.top, 50)
                    } else {
                        // Calculate card width based on screen width
                        let cardWidth = (UIScreen.main.bounds.width - 48) / 2
                        LazyVGrid(columns: gridLayout, spacing: 16) {
                            ForEach(userImages) { image in
                                PublicUserImageCard(image: image, cardWidth: cardWidth)
                                    .environmentObject(userState)
                            }
                        }
                        .padding(.horizontal, 16)
                        
                        // Load more indicator
                        if hasMoreUserImages {
                            if isLoadingMoreUserImages {
                                ProgressView("加载更多...")
                                    .padding()
                            } else {
                                // Use onAppear on the indicator to trigger loading more
                                Color.clear
                                    .frame(height: 50)
                                    .onAppear {
                                        loadMoreUserImages()
                                    }
                            }
                        } else if !userImages.isEmpty {
                            Text("没有更多内容了")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .padding()
                        }
                    }
                }
            }
        }
        .fullScreenCover(isPresented: $showChat) {
            // 生成会话ID（格式：小ID_大ID）
            let conversationId = userState.userId < profileUserId ? "\(userState.userId)_\(profileUserId)" : "\(profileUserId)_\(userState.userId)"
            
            // 显示聊天界面
            NavigationView {
                ChatView(
                    conversationId: conversationId,
                    otherUserId: profileUserId,
                    otherUsername: profileUsername,
                    userId: userState.userId,
                    otherUserAvatar: profileAvatar
                )
            }
        }
        .onAppear {
            
            loadUserData()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ImageDeleted"))) { notification in
             if let userInfo = notification.userInfo,
               let imageId = userInfo["imageId"] as? Int {
                userImages.removeAll { $0.imageId == imageId }
            }
        }
        // 添加右滑返回手势
        .gesture(
            DragGesture()
                .onEnded { gesture in
                    if gesture.translation.width > 50 {
                        dismiss()
                    }
                }
        )
        .onDisappear {
            UserState.shared.updatePageStatus(page: "other", conversationId: "")

        }
    }
    
    private func loadUserData(resetImages: Bool = true) {
        if resetImages {
            isLoading = true
            errorMessage = nil
            userImagesSkip = 0
            hasMoreUserImages = true
            userImages = [] // Clear images on full reload
        }
        
        // Prevent multiple simultaneous loads
        guard !isLoadingMoreUserImages else { return }
        isLoadingMoreUserImages = true
        
        APIService.shared.getUserImages(userId: profileUserId, skip: userImagesSkip) { result in
            DispatchQueue.main.async {
                if resetImages {
                    isLoading = false
                }
                isLoadingMoreUserImages = false
                
                switch result {
                case .success(let images):
                    let existingIds = Set(self.userImages.map { $0.imageId })
                    let newImages = images.filter { !existingIds.contains($0.imageId) }
                    self.userImages.append(contentsOf: newImages)
                    
                    self.userImagesSkip += newImages.count
                    self.hasMoreUserImages = images.count >= 20
                    
                case .failure(let error):
                    self.errorMessage = "加载图片失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func loadMoreUserImages() {
        guard hasMoreUserImages && !isLoadingMoreUserImages else { return }
        loadUserData(resetImages: false)
    }
}

struct PublicUserImageCard: View {
    let image: ImageData
    let cardWidth: CGFloat
    @EnvironmentObject var userState: UserState
    @State private var navigateToDetail = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 5) {
            ZStack {
                AsyncImage(url: APIService.shared.getImageURL(filename: URL(string: image.storagePath)?.lastPathComponent ?? image.storagePath, lowResolution: true)) { phase in
                     switch phase {
                    case .empty:
                        Rectangle().foregroundColor(.gray.opacity(0.3)).aspectRatio(1.0, contentMode: .fill).overlay(ProgressView())
                    case .success(let img):
                        img.resizable().aspectRatio(contentMode: .fill)
                    case .failure:
                        Rectangle().foregroundColor(.gray.opacity(0.3)).overlay(Image(systemName: "exclamationmark.triangle"))
                    @unknown default:
                        Rectangle().foregroundColor(.gray.opacity(0.3))
                    }
                }
                .frame(width: cardWidth, height: cardWidth)
                .clipped()
                .cornerRadius(10)
                
                Rectangle()
                    .fill(Color.clear)
                    .contentShape(Rectangle())
                    .onTapGesture {
                        navigateToDetail = true
                    }
                
                .fullScreenCover(isPresented: $navigateToDetail) {
                    ImageDetailView(imageId: image.imageId)
                        .environmentObject(userState)
                }
            }
            
            HStack {
                Spacer() 
                Text(formatDate(image.createdAt))
                    .font(Font.custom("PingFang SC", size: 10))
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 5)
            .padding(.vertical, 3)
        }
        .frame(width: cardWidth)
        .background(Color.white.opacity(0.7))
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 1)
    }
    
    private func formatDate(_ dateString: String) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        guard let date = dateFormatter.date(from: dateString) else { return dateString }
        let outputFormatter = DateFormatter()
        outputFormatter.dateFormat = "MM-dd"
        return outputFormatter.string(from: date)
    }
} 