// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.

// this header contains the entire ONNX Runtime Objective-C API
// the headers below can also be imported individually

#import "ort_coreml_execution_provider.h"
#import "ort_custom_op_registration.h"
#import "ort_enums.h"
#import "ort_env.h"
#import "ort_session.h"
#import "ort_value.h"
#import "ort_xnnpack_execution_provider.h"
