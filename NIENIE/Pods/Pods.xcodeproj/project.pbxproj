// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXAggregateTarget section */
		01D26C9790741D5F08E2B47A48D79331 /* onnxruntime-c */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 1FAA34C873E6D52B09F47086906FF3E4 /* Build configuration list for PBXAggregateTarget "onnxruntime-c" */;
			buildPhases = (
				3CFBA14174592D8554CB04535DA6DCD3 /* [CP] Copy XCFrameworks */,
			);
			dependencies = (
			);
			name = "onnxruntime-c";
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		003389E4C9F05DBB059F4A4A49B3D280 /* ort_value_internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 2218AD92BB8A628BA35835E413260AD4 /* ort_value_internal.h */; settings = {ATTRIBUTES = (Project, ); }; };
		04A098FFE4DABE4F287FB34FA1A8569B /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		1CE67C2EE5603ED2EF29637C4B28493A /* onnxruntime-objc-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = EA644FB4C1395CD0303E0B3EB0227D00 /* onnxruntime-objc-dummy.m */; };
		1D2C141BBFEF29A242D7ACDF4A4FB5DA /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		1D972401495FD59B3FEF0A7CC30AF75F /* Pods-NIENIETests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C73EA7A3DF33FE06D57801EDCAEE145 /* Pods-NIENIETests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1DE618B41C2E4D6ED753D47BE2711A03 /* ort_enums.h in Headers */ = {isa = PBXBuildFile; fileRef = 32D9C9176A56D2B9AA6535043314E1C7 /* ort_enums.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1EE249919A0FC312F41D887D6D162DA1 /* error_utils.h in Headers */ = {isa = PBXBuildFile; fileRef = 6039F2182F0055C6E5DE18806C772A82 /* error_utils.h */; settings = {ATTRIBUTES = (Project, ); }; };
		20000D3295699399D132ACD1BF177952 /* Pods-NIENIE-NIENIEUITests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 1FDB1AD44BB853E2134D05E5CA297ECD /* Pods-NIENIE-NIENIEUITests-dummy.m */; };
		200ADE11771EE4C9169827DBD9D3F12B /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		20F0C7DE6A41A6271D4DD97E90594977 /* ort_enums_internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 532324709128DA6299B9F524112B9FA0 /* ort_enums_internal.h */; settings = {ATTRIBUTES = (Project, ); }; };
		29BDDDB0D0D7B89A15142AEDFE3CFA61 /* ort_value.h in Headers */ = {isa = PBXBuildFile; fileRef = 6982F3A5A75D4E93CF93239FF9F00AC5 /* ort_value.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3658AD7DC59EDF86222C2A1DAC7DD8A6 /* Pods-NIENIE-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = EE3E1DF87432070E6E1D34243848CC61 /* Pods-NIENIE-dummy.m */; };
		374FCE398818C8F9D45FD51FB81C370D /* ort_session.h in Headers */ = {isa = PBXBuildFile; fileRef = DD05866E3B414DC4D7662060B95A0962 /* ort_session.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3DDB99C4A965583EB36CCEF4EF335761 /* cxx_utils.h in Headers */ = {isa = PBXBuildFile; fileRef = 4CEDE43107A6E9768206FA6D37E55C38 /* cxx_utils.h */; settings = {ATTRIBUTES = (Project, ); }; };
		4C70E99F2DC5461D714237B5875D191D /* cxx_utils.mm in Sources */ = {isa = PBXBuildFile; fileRef = 650412AF02FAD6BB98328A1CEC81D167 /* cxx_utils.mm */; settings = {COMPILER_FLAGS = "-std=c++17 -fobjc-arc-exceptions -Wall -Wextra -Werror"; }; };
		5C427410E5ACAD9060A825ED8DF80DFF /* onnxruntime-objc-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 06F8BAD46079237E0C7698F16EC5864E /* onnxruntime-objc-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		693F184DAF6CB144679C155F407AC76F /* ort_session_internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 51B10664E800667CE1F77AF64A7D78DF /* ort_session_internal.h */; settings = {ATTRIBUTES = (Project, ); }; };
		722532CD5E4E7AE13515EA63F232C901 /* ort_session.mm in Sources */ = {isa = PBXBuildFile; fileRef = 11FA4CE2DFD7837E2EAC53964B4DDA7C /* ort_session.mm */; settings = {COMPILER_FLAGS = "-std=c++17 -fobjc-arc-exceptions -Wall -Wextra -Werror"; }; };
		7CB6F3EA00C4DC2E0A21BE411D69D049 /* ort_coreml_execution_provider.mm in Sources */ = {isa = PBXBuildFile; fileRef = 00420C407D686DF8686D4E1049CD6D91 /* ort_coreml_execution_provider.mm */; settings = {COMPILER_FLAGS = "-std=c++17 -fobjc-arc-exceptions -Wall -Wextra -Werror"; }; };
		7F3DD6BA37CD60DC3E839F99DB381805 /* ort_xnnpack_execution_provider.mm in Sources */ = {isa = PBXBuildFile; fileRef = 97F989349A441C6DBDB6BED0F0956DE2 /* ort_xnnpack_execution_provider.mm */; settings = {COMPILER_FLAGS = "-std=c++17 -fobjc-arc-exceptions -Wall -Wextra -Werror"; }; };
		89F7BEC62C9F5139E3D8412A991DC28E /* assert_arc_enabled.mm in Sources */ = {isa = PBXBuildFile; fileRef = 2995B6D006B67CE86729B47DB7CAE682 /* assert_arc_enabled.mm */; settings = {COMPILER_FLAGS = "-std=c++17 -fobjc-arc-exceptions -Wall -Wextra -Werror"; }; };
		98A9332AE76C148FEB6F1AC7A68D6009 /* Pods-NIENIE-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 4E6D1F3C18C6219FB88174FCE36EA8E1 /* Pods-NIENIE-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9BD586BCF14AC35C3B562E2A949C4C21 /* ort_coreml_execution_provider.h in Headers */ = {isa = PBXBuildFile; fileRef = A94ABB63B75AFABFCE1F09DA668C4B63 /* ort_coreml_execution_provider.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B2387223AA7F55A0A24DE2E54F2C8E12 /* ort_enums.mm in Sources */ = {isa = PBXBuildFile; fileRef = 623B77735BDD4A2D92E63E41415969C9 /* ort_enums.mm */; settings = {COMPILER_FLAGS = "-std=c++17 -fobjc-arc-exceptions -Wall -Wextra -Werror"; }; };
		B318102AD3DA8A74A5761E2379D9375A /* cxx_api.h in Headers */ = {isa = PBXBuildFile; fileRef = E9C7F42B5DB92B1153DC6C35EBF45322 /* cxx_api.h */; settings = {ATTRIBUTES = (Project, ); }; };
		B7D715200A32EE884D21B1B58D2D16C9 /* onnxruntime.h in Headers */ = {isa = PBXBuildFile; fileRef = 1A582EB14E1FE683ACACBDCDEF5F65A0 /* onnxruntime.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BB36BE597D18CAFC9E1D9A01B50664F8 /* ort_env.h in Headers */ = {isa = PBXBuildFile; fileRef = FBFE450855C348FC932F38C8BAE57C1E /* ort_env.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C8966630304F0051ADD3D3C0B31FF5BA /* ort_custom_op_registration.h in Headers */ = {isa = PBXBuildFile; fileRef = 48539BC85CBA097FB1638931DF333BC3 /* ort_custom_op_registration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CAA5C2184FE1019B21DA25F6A00FF059 /* ort_xnnpack_execution_provider.h in Headers */ = {isa = PBXBuildFile; fileRef = 9B7B3E17C6B3E3D9174F730655AEF221 /* ort_xnnpack_execution_provider.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DD27BE48DA885DF8DF2C9E7EA76C554A /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		DEBE738A438F6E92813AA1023A8B2308 /* Pods-NIENIETests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 66EC32C17D740EF7D93780BA775CD227 /* Pods-NIENIETests-dummy.m */; };
		E1264C0E9E5A7F8B96816B328354510F /* ort_env_internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 86A02984818DA5413F5FF746368DC1A1 /* ort_env_internal.h */; settings = {ATTRIBUTES = (Project, ); }; };
		E7927D5D6E3237AFF3326409A16BE344 /* Pods-NIENIE-NIENIEUITests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 9B9A042B42F66864CE0AA57834DE3F6E /* Pods-NIENIE-NIENIEUITests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EC58F1A3C67AF5AA3EA552EFD72CCB9C /* ort_env.mm in Sources */ = {isa = PBXBuildFile; fileRef = FF6B6BD0E2522ED58BAF909A762A6562 /* ort_env.mm */; settings = {COMPILER_FLAGS = "-std=c++17 -fobjc-arc-exceptions -Wall -Wextra -Werror"; }; };
		F088CED73ABB6AD990ABEF8D01B3AE1B /* ort_value.mm in Sources */ = {isa = PBXBuildFile; fileRef = B44129574A1F000BF900D83D92430306 /* ort_value.mm */; settings = {COMPILER_FLAGS = "-std=c++17 -fobjc-arc-exceptions -Wall -Wextra -Werror"; }; };
		F26E8BBE948ABFBC616DAC9CB9431EA8 /* error_utils.mm in Sources */ = {isa = PBXBuildFile; fileRef = C1723CC24F12B6465A57023567B5CF19 /* error_utils.mm */; settings = {COMPILER_FLAGS = "-std=c++17 -fobjc-arc-exceptions -Wall -Wextra -Werror"; }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		87F1C8C97B4D66DFD923748ED7B96735 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DD8A7EE9BACB64198DBBF12EA5122AC8;
			remoteInfo = "onnxruntime-objc";
		};
		902236A3362AD92743AB92608D96FE84 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DD8A7EE9BACB64198DBBF12EA5122AC8;
			remoteInfo = "onnxruntime-objc";
		};
		C1612D17B0E301FF4845014CEC97699E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 746CC253F54424F46904E0AC4C424624;
			remoteInfo = "Pods-NIENIE";
		};
		F5C5E5BBF5A7F49274E478196379DAEE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 01D26C9790741D5F08E2B47A48D79331;
			remoteInfo = "onnxruntime-c";
		};
		F70F0A2CF487F98A8DC896714200DDEC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 01D26C9790741D5F08E2B47A48D79331;
			remoteInfo = "onnxruntime-c";
		};
		F95FB149A8256D78ABAFB77410EC4145 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 01D26C9790741D5F08E2B47A48D79331;
			remoteInfo = "onnxruntime-c";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00420C407D686DF8686D4E1049CD6D91 /* ort_coreml_execution_provider.mm */ = {isa = PBXFileReference; includeInIndex = 1; name = ort_coreml_execution_provider.mm; path = objectivec/ort_coreml_execution_provider.mm; sourceTree = "<group>"; };
		01B2276DBC87EB32ADB469A99D80CAFE /* Pods-NIENIETests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-NIENIETests.debug.xcconfig"; sourceTree = "<group>"; };
		01F17C0DB1D33EF83F2C85B863D99927 /* Pods-NIENIE-NIENIEUITests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-NIENIE-NIENIEUITests"; path = Pods_NIENIE_NIENIEUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		0212A920D55D5189CB30E0CD8225B25F /* onnxruntime.xcframework */ = {isa = PBXFileReference; includeInIndex = 1; path = onnxruntime.xcframework; sourceTree = "<group>"; };
		06F8BAD46079237E0C7698F16EC5864E /* onnxruntime-objc-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "onnxruntime-objc-umbrella.h"; sourceTree = "<group>"; };
		0E27CDB4000BB7AA946EB85929A49A7E /* onnxruntime-c.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "onnxruntime-c.debug.xcconfig"; sourceTree = "<group>"; };
		0F0CE342B755EE78B5F4D16591EE60F7 /* onnxruntime_cxx_api.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = onnxruntime_cxx_api.h; path = Headers/onnxruntime_cxx_api.h; sourceTree = "<group>"; };
		11FA4CE2DFD7837E2EAC53964B4DDA7C /* ort_session.mm */ = {isa = PBXFileReference; includeInIndex = 1; name = ort_session.mm; path = objectivec/ort_session.mm; sourceTree = "<group>"; };
		1A582EB14E1FE683ACACBDCDEF5F65A0 /* onnxruntime.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = onnxruntime.h; path = objectivec/include/onnxruntime.h; sourceTree = "<group>"; };
		1FDB1AD44BB853E2134D05E5CA297ECD /* Pods-NIENIE-NIENIEUITests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-NIENIE-NIENIEUITests-dummy.m"; sourceTree = "<group>"; };
		20D51334F6DF60766D3C0A66CC40D50F /* Pods-NIENIE */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-NIENIE"; path = Pods_NIENIE.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2218AD92BB8A628BA35835E413260AD4 /* ort_value_internal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ort_value_internal.h; path = objectivec/ort_value_internal.h; sourceTree = "<group>"; };
		2995B6D006B67CE86729B47DB7CAE682 /* assert_arc_enabled.mm */ = {isa = PBXFileReference; includeInIndex = 1; name = assert_arc_enabled.mm; path = objectivec/assert_arc_enabled.mm; sourceTree = "<group>"; };
		2C56A052B2A225D05FB5B66FC65D746F /* Pods-NIENIE-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-NIENIE-acknowledgements.markdown"; sourceTree = "<group>"; };
		2EA79908F0B7B7E876DAF9BC0DF44C54 /* onnxruntime-objc.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "onnxruntime-objc.release.xcconfig"; sourceTree = "<group>"; };
		2F96B72407029937B85B213E17E4F465 /* Pods-NIENIE-NIENIEUITests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-NIENIE-NIENIEUITests-acknowledgements.plist"; sourceTree = "<group>"; };
		32D9C9176A56D2B9AA6535043314E1C7 /* ort_enums.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ort_enums.h; path = objectivec/include/ort_enums.h; sourceTree = "<group>"; };
		384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		3F561F4B465BF7B4D4DC83051294C3D0 /* onnxruntime-objc-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "onnxruntime-objc-prefix.pch"; sourceTree = "<group>"; };
		48539BC85CBA097FB1638931DF333BC3 /* ort_custom_op_registration.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ort_custom_op_registration.h; path = objectivec/include/ort_custom_op_registration.h; sourceTree = "<group>"; };
		4B936D4F36665F1B9D90834321A6CEAD /* Pods-NIENIETests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-NIENIETests-acknowledgements.markdown"; sourceTree = "<group>"; };
		4CEDE43107A6E9768206FA6D37E55C38 /* cxx_utils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = cxx_utils.h; path = objectivec/cxx_utils.h; sourceTree = "<group>"; };
		4E0898AD2F769D1FFDD93D5AD83EB5C5 /* Pods-NIENIE-NIENIEUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-NIENIE-NIENIEUITests.release.xcconfig"; sourceTree = "<group>"; };
		4E6D1F3C18C6219FB88174FCE36EA8E1 /* Pods-NIENIE-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-NIENIE-umbrella.h"; sourceTree = "<group>"; };
		4F5F252B7FCB6AE8347E01EB793D31B4 /* cpu_provider_factory.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = cpu_provider_factory.h; path = Headers/cpu_provider_factory.h; sourceTree = "<group>"; };
		51B10664E800667CE1F77AF64A7D78DF /* ort_session_internal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ort_session_internal.h; path = objectivec/ort_session_internal.h; sourceTree = "<group>"; };
		532324709128DA6299B9F524112B9FA0 /* ort_enums_internal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ort_enums_internal.h; path = objectivec/ort_enums_internal.h; sourceTree = "<group>"; };
		54D9C1B1D66D9C10179834E50AFFA884 /* coreml_provider_factory.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = coreml_provider_factory.h; path = Headers/coreml_provider_factory.h; sourceTree = "<group>"; };
		573CCBCEE223D2A3142A41316BE61205 /* Pods-NIENIETests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-NIENIETests"; path = Pods_NIENIETests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		590EDC71C7866E0F19AA22CC053A8747 /* onnxruntime_float16.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = onnxruntime_float16.h; path = Headers/onnxruntime_float16.h; sourceTree = "<group>"; };
		5937AE477F4DA9ABBA35EF258F9BE0E9 /* Pods-NIENIETests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-NIENIETests-acknowledgements.plist"; sourceTree = "<group>"; };
		5B6108C1314D86CCB21A3B80276024CA /* Pods-NIENIE-NIENIEUITests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-NIENIE-NIENIEUITests.modulemap"; sourceTree = "<group>"; };
		5BB4D477C1FA8B16A3363CCD139B1C9D /* Pods-NIENIE-NIENIEUITests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-NIENIE-NIENIEUITests-acknowledgements.markdown"; sourceTree = "<group>"; };
		5C73EA7A3DF33FE06D57801EDCAEE145 /* Pods-NIENIETests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-NIENIETests-umbrella.h"; sourceTree = "<group>"; };
		6039F2182F0055C6E5DE18806C772A82 /* error_utils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = error_utils.h; path = objectivec/error_utils.h; sourceTree = "<group>"; };
		623B77735BDD4A2D92E63E41415969C9 /* ort_enums.mm */ = {isa = PBXFileReference; includeInIndex = 1; name = ort_enums.mm; path = objectivec/ort_enums.mm; sourceTree = "<group>"; };
		629006E43F671BC3AE067B8B4A905801 /* Pods-NIENIETests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-NIENIETests.modulemap"; sourceTree = "<group>"; };
		650412AF02FAD6BB98328A1CEC81D167 /* cxx_utils.mm */ = {isa = PBXFileReference; includeInIndex = 1; name = cxx_utils.mm; path = objectivec/cxx_utils.mm; sourceTree = "<group>"; };
		66EC32C17D740EF7D93780BA775CD227 /* Pods-NIENIETests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-NIENIETests-dummy.m"; sourceTree = "<group>"; };
		6982F3A5A75D4E93CF93239FF9F00AC5 /* ort_value.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ort_value.h; path = objectivec/include/ort_value.h; sourceTree = "<group>"; };
		7C691DE464F33FAB6FE65013CAE8DC3B /* onnxruntime-c-xcframeworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "onnxruntime-c-xcframeworks.sh"; sourceTree = "<group>"; };
		83CEBE207DAF23EB35299EF9DFE949A9 /* Pods-NIENIE-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-NIENIE-acknowledgements.plist"; sourceTree = "<group>"; };
		86A02984818DA5413F5FF746368DC1A1 /* ort_env_internal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ort_env_internal.h; path = objectivec/ort_env_internal.h; sourceTree = "<group>"; };
		949D6BB481118634621F32BD7909EFEE /* onnxruntime_cxx_inline.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = onnxruntime_cxx_inline.h; path = Headers/onnxruntime_cxx_inline.h; sourceTree = "<group>"; };
		97F989349A441C6DBDB6BED0F0956DE2 /* ort_xnnpack_execution_provider.mm */ = {isa = PBXFileReference; includeInIndex = 1; name = ort_xnnpack_execution_provider.mm; path = objectivec/ort_xnnpack_execution_provider.mm; sourceTree = "<group>"; };
		9B2319E1BECB82D886A23344104DCBD6 /* Pods-NIENIETests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-NIENIETests-Info.plist"; sourceTree = "<group>"; };
		9B7B3E17C6B3E3D9174F730655AEF221 /* ort_xnnpack_execution_provider.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ort_xnnpack_execution_provider.h; path = objectivec/include/ort_xnnpack_execution_provider.h; sourceTree = "<group>"; };
		9B9A042B42F66864CE0AA57834DE3F6E /* Pods-NIENIE-NIENIEUITests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-NIENIE-NIENIEUITests-umbrella.h"; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A94ABB63B75AFABFCE1F09DA668C4B63 /* ort_coreml_execution_provider.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ort_coreml_execution_provider.h; path = objectivec/include/ort_coreml_execution_provider.h; sourceTree = "<group>"; };
		AF44BE896BEBBE099592D03904279C1C /* onnxruntime-objc */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "onnxruntime-objc"; path = onnxruntime_objc.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B079C7D0C569B2255BC7C6F7142305E4 /* Pods-NIENIETests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-NIENIETests.release.xcconfig"; sourceTree = "<group>"; };
		B29EC2F23C94F1E06246D5C2B33FA10F /* Pods-NIENIE.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-NIENIE.debug.xcconfig"; sourceTree = "<group>"; };
		B2BAEFC344F6A9A8CB421AF1A78D6CB6 /* Pods-NIENIE.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-NIENIE.release.xcconfig"; sourceTree = "<group>"; };
		B44129574A1F000BF900D83D92430306 /* ort_value.mm */ = {isa = PBXFileReference; includeInIndex = 1; name = ort_value.mm; path = objectivec/ort_value.mm; sourceTree = "<group>"; };
		B713F04AB626F156457E5171710B6BD0 /* Pods-NIENIE-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-NIENIE-Info.plist"; sourceTree = "<group>"; };
		BA5935AD9A339C9EDCD92CC30290695A /* onnxruntime_c_api.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = onnxruntime_c_api.h; path = Headers/onnxruntime_c_api.h; sourceTree = "<group>"; };
		BF0A9D18667CE600F6E578E608A7EEA9 /* onnxruntime-objc.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "onnxruntime-objc.modulemap"; sourceTree = "<group>"; };
		C1723CC24F12B6465A57023567B5CF19 /* error_utils.mm */ = {isa = PBXFileReference; includeInIndex = 1; name = error_utils.mm; path = objectivec/error_utils.mm; sourceTree = "<group>"; };
		C1D28038E33341CB41357718321D872E /* onnxruntime-c.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "onnxruntime-c.release.xcconfig"; sourceTree = "<group>"; };
		C3830DF17045B7F805C455266ED3F430 /* Pods-NIENIE-NIENIEUITests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-NIENIE-NIENIEUITests-Info.plist"; sourceTree = "<group>"; };
		D497A39364D6627403D2F460C120BB48 /* Pods-NIENIE.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-NIENIE.modulemap"; sourceTree = "<group>"; };
		DD05866E3B414DC4D7662060B95A0962 /* ort_session.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ort_session.h; path = objectivec/include/ort_session.h; sourceTree = "<group>"; };
		DF0122ED0EAD0ED8664428047F535254 /* Pods-NIENIE-NIENIEUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-NIENIE-NIENIEUITests.debug.xcconfig"; sourceTree = "<group>"; };
		E9C7F42B5DB92B1153DC6C35EBF45322 /* cxx_api.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = cxx_api.h; path = objectivec/cxx_api.h; sourceTree = "<group>"; };
		EA644FB4C1395CD0303E0B3EB0227D00 /* onnxruntime-objc-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "onnxruntime-objc-dummy.m"; sourceTree = "<group>"; };
		EE3E1DF87432070E6E1D34243848CC61 /* Pods-NIENIE-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-NIENIE-dummy.m"; sourceTree = "<group>"; };
		EF58748D772D83A4EE322A2B283B228C /* onnxruntime-objc-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "onnxruntime-objc-Info.plist"; sourceTree = "<group>"; };
		FBFE450855C348FC932F38C8BAE57C1E /* ort_env.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ort_env.h; path = objectivec/include/ort_env.h; sourceTree = "<group>"; };
		FE4F142E921F6AABBB28F6AAC9D3D391 /* onnxruntime-objc.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "onnxruntime-objc.debug.xcconfig"; sourceTree = "<group>"; };
		FF6B6BD0E2522ED58BAF909A762A6562 /* ort_env.mm */ = {isa = PBXFileReference; includeInIndex = 1; name = ort_env.mm; path = objectivec/ort_env.mm; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0B84E22CCCDB903EDE372C60A40680BF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				04A098FFE4DABE4F287FB34FA1A8569B /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3C704582B36595F22D3FB92E119A2252 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				1D2C141BBFEF29A242D7ACDF4A4FB5DA /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6527006BFB79AEE816BE6ABDE3402E9F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				DD27BE48DA885DF8DF2C9E7EA76C554A /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		93D0323F73B3EA8A2AD0EFA0EB9E83FA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				200ADE11771EE4C9169827DBD9D3F12B /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0D666F4A629E94A71A62593A95366BF8 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				BF0A9D18667CE600F6E578E608A7EEA9 /* onnxruntime-objc.modulemap */,
				EA644FB4C1395CD0303E0B3EB0227D00 /* onnxruntime-objc-dummy.m */,
				EF58748D772D83A4EE322A2B283B228C /* onnxruntime-objc-Info.plist */,
				3F561F4B465BF7B4D4DC83051294C3D0 /* onnxruntime-objc-prefix.pch */,
				06F8BAD46079237E0C7698F16EC5864E /* onnxruntime-objc-umbrella.h */,
				FE4F142E921F6AABBB28F6AAC9D3D391 /* onnxruntime-objc.debug.xcconfig */,
				2EA79908F0B7B7E876DAF9BC0DF44C54 /* onnxruntime-objc.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/onnxruntime-objc";
			sourceTree = "<group>";
		};
		12DF7458D12CD869DE9A5B8EBEEEB7E7 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				7C691DE464F33FAB6FE65013CAE8DC3B /* onnxruntime-c-xcframeworks.sh */,
				0E27CDB4000BB7AA946EB85929A49A7E /* onnxruntime-c.debug.xcconfig */,
				C1D28038E33341CB41357718321D872E /* onnxruntime-c.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/onnxruntime-c";
			sourceTree = "<group>";
		};
		1D507DFD365C6ED530A211737A7B3285 /* Pods-NIENIE-NIENIEUITests */ = {
			isa = PBXGroup;
			children = (
				5B6108C1314D86CCB21A3B80276024CA /* Pods-NIENIE-NIENIEUITests.modulemap */,
				5BB4D477C1FA8B16A3363CCD139B1C9D /* Pods-NIENIE-NIENIEUITests-acknowledgements.markdown */,
				2F96B72407029937B85B213E17E4F465 /* Pods-NIENIE-NIENIEUITests-acknowledgements.plist */,
				1FDB1AD44BB853E2134D05E5CA297ECD /* Pods-NIENIE-NIENIEUITests-dummy.m */,
				C3830DF17045B7F805C455266ED3F430 /* Pods-NIENIE-NIENIEUITests-Info.plist */,
				9B9A042B42F66864CE0AA57834DE3F6E /* Pods-NIENIE-NIENIEUITests-umbrella.h */,
				DF0122ED0EAD0ED8664428047F535254 /* Pods-NIENIE-NIENIEUITests.debug.xcconfig */,
				4E0898AD2F769D1FFDD93D5AD83EB5C5 /* Pods-NIENIE-NIENIEUITests.release.xcconfig */,
			);
			name = "Pods-NIENIE-NIENIEUITests";
			path = "Target Support Files/Pods-NIENIE-NIENIEUITests";
			sourceTree = "<group>";
		};
		3892A07EF9016F3822F9D0BCD6AE81E0 /* onnxruntime-c */ = {
			isa = PBXGroup;
			children = (
				54D9C1B1D66D9C10179834E50AFFA884 /* coreml_provider_factory.h */,
				4F5F252B7FCB6AE8347E01EB793D31B4 /* cpu_provider_factory.h */,
				BA5935AD9A339C9EDCD92CC30290695A /* onnxruntime_c_api.h */,
				0F0CE342B755EE78B5F4D16591EE60F7 /* onnxruntime_cxx_api.h */,
				949D6BB481118634621F32BD7909EFEE /* onnxruntime_cxx_inline.h */,
				590EDC71C7866E0F19AA22CC053A8747 /* onnxruntime_float16.h */,
				F9FA6C1EFDCE3D560404B9B18308528A /* Frameworks */,
				12DF7458D12CD869DE9A5B8EBEEEB7E7 /* Support Files */,
			);
			name = "onnxruntime-c";
			path = "onnxruntime-c";
			sourceTree = "<group>";
		};
		4A60F898E67E026275576E55A684E918 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				4DCDA7872961B79CC5CE66A8A3FE6640 /* Pods-NIENIE */,
				1D507DFD365C6ED530A211737A7B3285 /* Pods-NIENIE-NIENIEUITests */,
				AE98AA01ACB0BB27106C0C7482D9292E /* Pods-NIENIETests */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		4DCDA7872961B79CC5CE66A8A3FE6640 /* Pods-NIENIE */ = {
			isa = PBXGroup;
			children = (
				D497A39364D6627403D2F460C120BB48 /* Pods-NIENIE.modulemap */,
				2C56A052B2A225D05FB5B66FC65D746F /* Pods-NIENIE-acknowledgements.markdown */,
				83CEBE207DAF23EB35299EF9DFE949A9 /* Pods-NIENIE-acknowledgements.plist */,
				EE3E1DF87432070E6E1D34243848CC61 /* Pods-NIENIE-dummy.m */,
				B713F04AB626F156457E5171710B6BD0 /* Pods-NIENIE-Info.plist */,
				4E6D1F3C18C6219FB88174FCE36EA8E1 /* Pods-NIENIE-umbrella.h */,
				B29EC2F23C94F1E06246D5C2B33FA10F /* Pods-NIENIE.debug.xcconfig */,
				B2BAEFC344F6A9A8CB421AF1A78D6CB6 /* Pods-NIENIE.release.xcconfig */,
			);
			name = "Pods-NIENIE";
			path = "Target Support Files/Pods-NIENIE";
			sourceTree = "<group>";
		};
		66945BB44D7E0552DAE6B6470A98592D /* onnxruntime-objc */ = {
			isa = PBXGroup;
			children = (
				A4F5D2C0A030FACECBCD9CE4EA46A0C8 /* Core */,
				0D666F4A629E94A71A62593A95366BF8 /* Support Files */,
			);
			name = "onnxruntime-objc";
			path = "onnxruntime-objc";
			sourceTree = "<group>";
		};
		A2A039E8E4C4803F75C45F19564E9851 /* Pods */ = {
			isa = PBXGroup;
			children = (
				3892A07EF9016F3822F9D0BCD6AE81E0 /* onnxruntime-c */,
				66945BB44D7E0552DAE6B6470A98592D /* onnxruntime-objc */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		A4F5D2C0A030FACECBCD9CE4EA46A0C8 /* Core */ = {
			isa = PBXGroup;
			children = (
				2995B6D006B67CE86729B47DB7CAE682 /* assert_arc_enabled.mm */,
				E9C7F42B5DB92B1153DC6C35EBF45322 /* cxx_api.h */,
				4CEDE43107A6E9768206FA6D37E55C38 /* cxx_utils.h */,
				650412AF02FAD6BB98328A1CEC81D167 /* cxx_utils.mm */,
				6039F2182F0055C6E5DE18806C772A82 /* error_utils.h */,
				C1723CC24F12B6465A57023567B5CF19 /* error_utils.mm */,
				1A582EB14E1FE683ACACBDCDEF5F65A0 /* onnxruntime.h */,
				A94ABB63B75AFABFCE1F09DA668C4B63 /* ort_coreml_execution_provider.h */,
				00420C407D686DF8686D4E1049CD6D91 /* ort_coreml_execution_provider.mm */,
				48539BC85CBA097FB1638931DF333BC3 /* ort_custom_op_registration.h */,
				32D9C9176A56D2B9AA6535043314E1C7 /* ort_enums.h */,
				623B77735BDD4A2D92E63E41415969C9 /* ort_enums.mm */,
				532324709128DA6299B9F524112B9FA0 /* ort_enums_internal.h */,
				FBFE450855C348FC932F38C8BAE57C1E /* ort_env.h */,
				FF6B6BD0E2522ED58BAF909A762A6562 /* ort_env.mm */,
				86A02984818DA5413F5FF746368DC1A1 /* ort_env_internal.h */,
				DD05866E3B414DC4D7662060B95A0962 /* ort_session.h */,
				11FA4CE2DFD7837E2EAC53964B4DDA7C /* ort_session.mm */,
				51B10664E800667CE1F77AF64A7D78DF /* ort_session_internal.h */,
				6982F3A5A75D4E93CF93239FF9F00AC5 /* ort_value.h */,
				B44129574A1F000BF900D83D92430306 /* ort_value.mm */,
				2218AD92BB8A628BA35835E413260AD4 /* ort_value_internal.h */,
				9B7B3E17C6B3E3D9174F730655AEF221 /* ort_xnnpack_execution_provider.h */,
				97F989349A441C6DBDB6BED0F0956DE2 /* ort_xnnpack_execution_provider.mm */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		AE98AA01ACB0BB27106C0C7482D9292E /* Pods-NIENIETests */ = {
			isa = PBXGroup;
			children = (
				629006E43F671BC3AE067B8B4A905801 /* Pods-NIENIETests.modulemap */,
				4B936D4F36665F1B9D90834321A6CEAD /* Pods-NIENIETests-acknowledgements.markdown */,
				5937AE477F4DA9ABBA35EF258F9BE0E9 /* Pods-NIENIETests-acknowledgements.plist */,
				66EC32C17D740EF7D93780BA775CD227 /* Pods-NIENIETests-dummy.m */,
				9B2319E1BECB82D886A23344104DCBD6 /* Pods-NIENIETests-Info.plist */,
				5C73EA7A3DF33FE06D57801EDCAEE145 /* Pods-NIENIETests-umbrella.h */,
				01B2276DBC87EB32ADB469A99D80CAFE /* Pods-NIENIETests.debug.xcconfig */,
				B079C7D0C569B2255BC7C6F7142305E4 /* Pods-NIENIETests.release.xcconfig */,
			);
			name = "Pods-NIENIETests";
			path = "Target Support Files/Pods-NIENIETests";
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */,
				A2A039E8E4C4803F75C45F19564E9851 /* Pods */,
				F60A0E9A5DE59656B263A48F149CF683 /* Products */,
				4A60F898E67E026275576E55A684E918 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E4801F62A6B08CD9B5410329F1A18FDE /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		E4801F62A6B08CD9B5410329F1A18FDE /* iOS */ = {
			isa = PBXGroup;
			children = (
				384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		F60A0E9A5DE59656B263A48F149CF683 /* Products */ = {
			isa = PBXGroup;
			children = (
				AF44BE896BEBBE099592D03904279C1C /* onnxruntime-objc */,
				20D51334F6DF60766D3C0A66CC40D50F /* Pods-NIENIE */,
				01F17C0DB1D33EF83F2C85B863D99927 /* Pods-NIENIE-NIENIEUITests */,
				573CCBCEE223D2A3142A41316BE61205 /* Pods-NIENIETests */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F9FA6C1EFDCE3D560404B9B18308528A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				0212A920D55D5189CB30E0CD8225B25F /* onnxruntime.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		008D24122511E8FCF3CDC7F9893A670A /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				E7927D5D6E3237AFF3326409A16BE344 /* Pods-NIENIE-NIENIEUITests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		394D28609EF2B0785B2B803F5B8AF53A /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				98A9332AE76C148FEB6F1AC7A68D6009 /* Pods-NIENIE-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A6E58B8C7D675EC8CDC9D20F5AA81C3D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				1D972401495FD59B3FEF0A7CC30AF75F /* Pods-NIENIETests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F2B5DB65F724674B692A202A7B921506 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				B318102AD3DA8A74A5761E2379D9375A /* cxx_api.h in Headers */,
				3DDB99C4A965583EB36CCEF4EF335761 /* cxx_utils.h in Headers */,
				1EE249919A0FC312F41D887D6D162DA1 /* error_utils.h in Headers */,
				B7D715200A32EE884D21B1B58D2D16C9 /* onnxruntime.h in Headers */,
				5C427410E5ACAD9060A825ED8DF80DFF /* onnxruntime-objc-umbrella.h in Headers */,
				9BD586BCF14AC35C3B562E2A949C4C21 /* ort_coreml_execution_provider.h in Headers */,
				C8966630304F0051ADD3D3C0B31FF5BA /* ort_custom_op_registration.h in Headers */,
				1DE618B41C2E4D6ED753D47BE2711A03 /* ort_enums.h in Headers */,
				20F0C7DE6A41A6271D4DD97E90594977 /* ort_enums_internal.h in Headers */,
				BB36BE597D18CAFC9E1D9A01B50664F8 /* ort_env.h in Headers */,
				E1264C0E9E5A7F8B96816B328354510F /* ort_env_internal.h in Headers */,
				374FCE398818C8F9D45FD51FB81C370D /* ort_session.h in Headers */,
				693F184DAF6CB144679C155F407AC76F /* ort_session_internal.h in Headers */,
				29BDDDB0D0D7B89A15142AEDFE3CFA61 /* ort_value.h in Headers */,
				003389E4C9F05DBB059F4A4A49B3D280 /* ort_value_internal.h in Headers */,
				CAA5C2184FE1019B21DA25F6A00FF059 /* ort_xnnpack_execution_provider.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		746CC253F54424F46904E0AC4C424624 /* Pods-NIENIE */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 69A605A291FDDFE67CB147AA508D72B9 /* Build configuration list for PBXNativeTarget "Pods-NIENIE" */;
			buildPhases = (
				394D28609EF2B0785B2B803F5B8AF53A /* Headers */,
				99D144D30D34F4F63C1BF0A7C3BBF2B7 /* Sources */,
				93D0323F73B3EA8A2AD0EFA0EB9E83FA /* Frameworks */,
				465AE4EDCE7FFBAC5F249097AF99CBDA /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F0E4D558B66717700DD6AF999D114CAD /* PBXTargetDependency */,
				83C7911ECCB83C7ED07EB06E71ADA744 /* PBXTargetDependency */,
			);
			name = "Pods-NIENIE";
			productName = Pods_NIENIE;
			productReference = 20D51334F6DF60766D3C0A66CC40D50F /* Pods-NIENIE */;
			productType = "com.apple.product-type.framework";
		};
		A1AF1E7F6489783186A7BCB5795F9456 /* Pods-NIENIETests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 74B39B0AF4A3C5DD517A0E00F168645D /* Build configuration list for PBXNativeTarget "Pods-NIENIETests" */;
			buildPhases = (
				A6E58B8C7D675EC8CDC9D20F5AA81C3D /* Headers */,
				DFF49B923A4E486A225370ADB8C9261C /* Sources */,
				6527006BFB79AEE816BE6ABDE3402E9F /* Frameworks */,
				11EE87AD58A19F9A4EE71730E72A0FF1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				45CDD4E09B5F04E041336E8F5ACAF306 /* PBXTargetDependency */,
			);
			name = "Pods-NIENIETests";
			productName = Pods_NIENIETests;
			productReference = 573CCBCEE223D2A3142A41316BE61205 /* Pods-NIENIETests */;
			productType = "com.apple.product-type.framework";
		};
		C2D858C69FFC8103A31FF5A94C8B97D6 /* Pods-NIENIE-NIENIEUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E74AACE5639FC1351A384F9AD9F67F20 /* Build configuration list for PBXNativeTarget "Pods-NIENIE-NIENIEUITests" */;
			buildPhases = (
				008D24122511E8FCF3CDC7F9893A670A /* Headers */,
				992CC4AC5B15987E578AD74CCC3BB686 /* Sources */,
				3C704582B36595F22D3FB92E119A2252 /* Frameworks */,
				83ABF7F28A9329052077A1731F925438 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0B58DB114A47D592C4FE50531A7F89F0 /* PBXTargetDependency */,
				A8F5595A4629865CDF1D65045AE27CE4 /* PBXTargetDependency */,
			);
			name = "Pods-NIENIE-NIENIEUITests";
			productName = Pods_NIENIE_NIENIEUITests;
			productReference = 01F17C0DB1D33EF83F2C85B863D99927 /* Pods-NIENIE-NIENIEUITests */;
			productType = "com.apple.product-type.framework";
		};
		DD8A7EE9BACB64198DBBF12EA5122AC8 /* onnxruntime-objc */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 639C65AE92756E2E984BEBD55988678B /* Build configuration list for PBXNativeTarget "onnxruntime-objc" */;
			buildPhases = (
				F2B5DB65F724674B692A202A7B921506 /* Headers */,
				6627BD2BABB5B6AF64EC8FDA502CD71B /* Sources */,
				0B84E22CCCDB903EDE372C60A40680BF /* Frameworks */,
				67B357B0034C312ECBF3B45B58626A99 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				051D2CE1FC59F22DEA7483F2B5FC2493 /* PBXTargetDependency */,
			);
			name = "onnxruntime-objc";
			productName = onnxruntime_objc;
			productReference = AF44BE896BEBBE099592D03904279C1C /* onnxruntime-objc */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 16.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = F60A0E9A5DE59656B263A48F149CF683 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				01D26C9790741D5F08E2B47A48D79331 /* onnxruntime-c */,
				DD8A7EE9BACB64198DBBF12EA5122AC8 /* onnxruntime-objc */,
				746CC253F54424F46904E0AC4C424624 /* Pods-NIENIE */,
				C2D858C69FFC8103A31FF5A94C8B97D6 /* Pods-NIENIE-NIENIEUITests */,
				A1AF1E7F6489783186A7BCB5795F9456 /* Pods-NIENIETests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		11EE87AD58A19F9A4EE71730E72A0FF1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		465AE4EDCE7FFBAC5F249097AF99CBDA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		67B357B0034C312ECBF3B45B58626A99 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		83ABF7F28A9329052077A1731F925438 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		3CFBA14174592D8554CB04535DA6DCD3 /* [CP] Copy XCFrameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/onnxruntime-c/onnxruntime-c-xcframeworks-input-files.xcfilelist",
			);
			name = "[CP] Copy XCFrameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/onnxruntime-c/onnxruntime-c-xcframeworks-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/onnxruntime-c/onnxruntime-c-xcframeworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6627BD2BABB5B6AF64EC8FDA502CD71B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				89F7BEC62C9F5139E3D8412A991DC28E /* assert_arc_enabled.mm in Sources */,
				4C70E99F2DC5461D714237B5875D191D /* cxx_utils.mm in Sources */,
				F26E8BBE948ABFBC616DAC9CB9431EA8 /* error_utils.mm in Sources */,
				1CE67C2EE5603ED2EF29637C4B28493A /* onnxruntime-objc-dummy.m in Sources */,
				7CB6F3EA00C4DC2E0A21BE411D69D049 /* ort_coreml_execution_provider.mm in Sources */,
				B2387223AA7F55A0A24DE2E54F2C8E12 /* ort_enums.mm in Sources */,
				EC58F1A3C67AF5AA3EA552EFD72CCB9C /* ort_env.mm in Sources */,
				722532CD5E4E7AE13515EA63F232C901 /* ort_session.mm in Sources */,
				F088CED73ABB6AD990ABEF8D01B3AE1B /* ort_value.mm in Sources */,
				7F3DD6BA37CD60DC3E839F99DB381805 /* ort_xnnpack_execution_provider.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		992CC4AC5B15987E578AD74CCC3BB686 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				20000D3295699399D132ACD1BF177952 /* Pods-NIENIE-NIENIEUITests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		99D144D30D34F4F63C1BF0A7C3BBF2B7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3658AD7DC59EDF86222C2A1DAC7DD8A6 /* Pods-NIENIE-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DFF49B923A4E486A225370ADB8C9261C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				DEBE738A438F6E92813AA1023A8B2308 /* Pods-NIENIETests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		051D2CE1FC59F22DEA7483F2B5FC2493 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "onnxruntime-c";
			target = 01D26C9790741D5F08E2B47A48D79331 /* onnxruntime-c */;
			targetProxy = F95FB149A8256D78ABAFB77410EC4145 /* PBXContainerItemProxy */;
		};
		0B58DB114A47D592C4FE50531A7F89F0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "onnxruntime-c";
			target = 01D26C9790741D5F08E2B47A48D79331 /* onnxruntime-c */;
			targetProxy = F5C5E5BBF5A7F49274E478196379DAEE /* PBXContainerItemProxy */;
		};
		45CDD4E09B5F04E041336E8F5ACAF306 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Pods-NIENIE";
			target = 746CC253F54424F46904E0AC4C424624 /* Pods-NIENIE */;
			targetProxy = C1612D17B0E301FF4845014CEC97699E /* PBXContainerItemProxy */;
		};
		83C7911ECCB83C7ED07EB06E71ADA744 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "onnxruntime-objc";
			target = DD8A7EE9BACB64198DBBF12EA5122AC8 /* onnxruntime-objc */;
			targetProxy = 87F1C8C97B4D66DFD923748ED7B96735 /* PBXContainerItemProxy */;
		};
		A8F5595A4629865CDF1D65045AE27CE4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "onnxruntime-objc";
			target = DD8A7EE9BACB64198DBBF12EA5122AC8 /* onnxruntime-objc */;
			targetProxy = 902236A3362AD92743AB92608D96FE84 /* PBXContainerItemProxy */;
		};
		F0E4D558B66717700DD6AF999D114CAD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "onnxruntime-c";
			target = 01D26C9790741D5F08E2B47A48D79331 /* onnxruntime-c */;
			targetProxy = F70F0A2CF487F98A8DC896714200DDEC /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		4973C1863633C95B781FAFF3B01EFC0D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B2BAEFC344F6A9A8CB421AF1A78D6CB6 /* Pods-NIENIE.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-NIENIE/Pods-NIENIE-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-NIENIE/Pods-NIENIE.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		4AEBD3569705A04DE67BE595ECC9B629 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0E27CDB4000BB7AA946EB85929A49A7E /* onnxruntime-c.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		5D5C422C117140537E70D6B07B61F836 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 01B2276DBC87EB32ADB469A99D80CAFE /* Pods-NIENIETests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-NIENIETests/Pods-NIENIETests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-NIENIETests/Pods-NIENIETests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		6CD6F1BEF7F8D03B0D5D815739A091F4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C1D28038E33341CB41357718321D872E /* onnxruntime-c.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		8B4773C96B27CE2EF7D3A9CBF127C948 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DF0122ED0EAD0ED8664428047F535254 /* Pods-NIENIE-NIENIEUITests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		8C9963745FD01B6B3EF95F9CE58D9F10 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		A18693D0D6ED29BA0D1F0A4C9379006B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FE4F142E921F6AABBB28F6AAC9D3D391 /* onnxruntime-objc.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/onnxruntime-objc/onnxruntime-objc-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/onnxruntime-objc/onnxruntime-objc.modulemap";
				PRODUCT_MODULE_NAME = onnxruntime_objc;
				PRODUCT_NAME = onnxruntime_objc;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B399B0055732E9B4264B010F309CF4B3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4E0898AD2F769D1FFDD93D5AD83EB5C5 /* Pods-NIENIE-NIENIEUITests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		B9F3557045385CA606F5AF548D58B58A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		BA4A4289E8A9903C2FCA4C09E3940F7B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B29EC2F23C94F1E06246D5C2B33FA10F /* Pods-NIENIE.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-NIENIE/Pods-NIENIE-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-NIENIE/Pods-NIENIE.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		F3824B48CD109A9A6029119FA8D6391B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2EA79908F0B7B7E876DAF9BC0DF44C54 /* onnxruntime-objc.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/onnxruntime-objc/onnxruntime-objc-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/onnxruntime-objc/onnxruntime-objc.modulemap";
				PRODUCT_MODULE_NAME = onnxruntime_objc;
				PRODUCT_NAME = onnxruntime_objc;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		F76693374D676F35E8329D0D76694AA8 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B079C7D0C569B2255BC7C6F7142305E4 /* Pods-NIENIETests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-NIENIETests/Pods-NIENIETests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-NIENIETests/Pods-NIENIETests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1FAA34C873E6D52B09F47086906FF3E4 /* Build configuration list for PBXAggregateTarget "onnxruntime-c" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4AEBD3569705A04DE67BE595ECC9B629 /* Debug */,
				6CD6F1BEF7F8D03B0D5D815739A091F4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8C9963745FD01B6B3EF95F9CE58D9F10 /* Debug */,
				B9F3557045385CA606F5AF548D58B58A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		639C65AE92756E2E984BEBD55988678B /* Build configuration list for PBXNativeTarget "onnxruntime-objc" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A18693D0D6ED29BA0D1F0A4C9379006B /* Debug */,
				F3824B48CD109A9A6029119FA8D6391B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		69A605A291FDDFE67CB147AA508D72B9 /* Build configuration list for PBXNativeTarget "Pods-NIENIE" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BA4A4289E8A9903C2FCA4C09E3940F7B /* Debug */,
				4973C1863633C95B781FAFF3B01EFC0D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		74B39B0AF4A3C5DD517A0E00F168645D /* Build configuration list for PBXNativeTarget "Pods-NIENIETests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5D5C422C117140537E70D6B07B61F836 /* Debug */,
				F76693374D676F35E8329D0D76694AA8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E74AACE5639FC1351A384F9AD9F67F20 /* Build configuration list for PBXNativeTarget "Pods-NIENIE-NIENIEUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8B4773C96B27CE2EF7D3A9CBF127C948 /* Debug */,
				B399B0055732E9B4264B010F309CF4B3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
