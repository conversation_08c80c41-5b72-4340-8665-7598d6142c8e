{"buildCommand": {"command": "prepareForIndexing", "enableIndexBuildArena": false, "targets": null}, "configuredTargets": [{"guid": "bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742"}, {"guid": "bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22"}, {"guid": "bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8"}, {"guid": "bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec"}, {"guid": "bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff"}], "containerPath": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Pods.xcodeproj", "continueBuildingAfterErrors": true, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "arenaInfo": {"buildIntermediatesPath": "", "buildProductsPath": "", "derivedDataPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Pods-gaqiaivdsecekedvjwdbnbumlypb/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Pods-gaqiaivdsecekedvjwdbnbumlypb/Index.noindex/PrecompiledHeaders", "pchPath": ""}, "configurationName": "Release", "overrides": {"synthesized": {"table": {"ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "NO", "ONLY_ACTIVE_ARCH": "YES"}}}}, "qos": "default", "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": true, "useImplicitDependencies": false, "useLegacyBuildLocations": false, "useParallelTargets": false}