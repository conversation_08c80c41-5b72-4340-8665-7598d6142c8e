{"case-sensitive": "false", "roots": [{"contents": [{"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE-umbrella.h", "name": "Pods-NIENIE-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests-umbrella.h", "name": "Pods-NIENIETests-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-umbrella.h", "name": "Pods-NIENIE-NIENIEUITests-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-umbrella.h", "name": "onnxruntime-objc-umbrella.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/onnxruntime.h", "name": "onnxruntime.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_coreml_execution_provider.h", "name": "ort_coreml_execution_provider.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_custom_op_registration.h", "name": "ort_custom_op_registration.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_enums.h", "name": "ort_enums.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_env.h", "name": "ort_env.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_session.h", "name": "ort_session.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_value.h", "name": "ort_value.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_xnnpack_execution_provider.h", "name": "ort_xnnpack_execution_provider.h", "type": "file"}], "name": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules", "type": "directory"}], "version": 0}