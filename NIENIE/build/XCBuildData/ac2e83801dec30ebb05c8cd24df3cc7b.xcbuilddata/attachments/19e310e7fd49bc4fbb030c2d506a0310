-target arm64-apple-ios17.6 '-std=gnu11' -fobjc-arc -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex' '-fmodule-name=Pods_NIENIETests' -fpascal-strings -Os -fno-common '-DPOD_CONFIGURATION_RELEASE=1' '-DCOCOAPODS=1' '-DNS_BLOCK_ASSERTIONS=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk -g -iquote '/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-generated-files.hmap' '-I/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-own-target-headers.hmap' '-I/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-all-non-framework-target-headers.hmap' -ivfsoverlay '/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-bfdfe7dc352907fc980b868725387e98-VFS-iphoneos/all-product-headers.yaml' -iquote '/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-project-headers.hmap' '-I/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/include' '-I/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers' '-I/Users/<USER>/Desktop/]ig/NIENIE/Pods/Headers/Public' '-I/Users/<USER>/Desktop/]ig/NIENIE/Pods/Headers/Public/onnxruntime-c' '-I/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources-normal/arm64' '-I/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/arm64' '-I/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources' '-F/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos' '-F/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc' '-F/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-c' '-F/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/XCFrameworkIntermediates/onnxruntime-c'