{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Desktop/]ig/NIENIE/build": {"is-mutated": true}, "/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos": {"is-mutated": true}, "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos": {"is-mutated": true}, "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c": {"is-mutated": true}, "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc": {"is-mutated": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Pods_NIENIE", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Pods_NIENIETests", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Pods_NIENIE_NIENIEUITests", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/onnxruntime_objc", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c>", "<Linked Binary /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Pods_NIENIE>", "<Linked Binary /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Pods_NIENIETests>", "<Linked Binary /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Pods_NIENIE_NIENIEUITests>", "<Linked Binary /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/onnxruntime_objc>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-scanning>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--linker-inputs-ready>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--modules-ready>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-scanning>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--linker-inputs-ready>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--modules-ready>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-scanning>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--end>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--linker-inputs-ready>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--modules-ready>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-linking>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-scanning>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--linker-inputs-ready>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--modules-ready>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--will-sign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-scanning>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--linker-inputs-ready>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--modules-ready>", "<workspace-Release--stale-file-removal>"], "outputs": ["<all>"]}, "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-Release-iphoneos--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Info.plist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers/Pods-NIENIE-NIENIEUITests-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Pods_NIENIE_NIENIEUITests", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/DerivedSources/Pods_NIENIE_NIENIEUITests_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-generated-files.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-project-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.hmap"], "roots": ["/tmp/Pods.dst", "/Users/<USER>/Desktop/]ig/NIENIE/build", "/Users/<USER>/Desktop/]ig/NIENIE/build"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-Release-iphoneos--arm64-build-headers-stale-file-removal>"]}, "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-Release-iphoneos--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Info.plist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers/Pods-NIENIE-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Pods_NIENIE", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/DerivedSources/Pods_NIENIE_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-generated-files.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-project-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.hmap"], "roots": ["/tmp/Pods.dst", "/Users/<USER>/Desktop/]ig/NIENIE/build", "/Users/<USER>/Desktop/]ig/NIENIE/build"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-Release-iphoneos--arm64-build-headers-stale-file-removal>"]}, "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-Release-iphoneos--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Info.plist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers/Pods-NIENIETests-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Pods_NIENIETests", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/Pods_NIENIETests_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-generated-files.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-project-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.hmap"], "roots": ["/tmp/Pods.dst", "/Users/<USER>/Desktop/]ig/NIENIE/build", "/Users/<USER>/Desktop/]ig/NIENIE/build"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-Release-iphoneos--arm64-build-headers-stale-file-removal>"]}, "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-Release-iphoneos--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/XCFrameworkIntermediates/onnxruntime-c/onnxruntime.framework", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/InputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-input-files-895d2e6a9bdc7486ec0417646b06ea49-resolved.xcfilelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/OutputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-output-files-656e02bc143860ae06dd67f9f8235416-resolved.xcfilelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Script-3CFBA14174592D8554CB04535DA6DCD3.sh"], "roots": ["/tmp/Pods.dst", "/Users/<USER>/Desktop/]ig/NIENIE/build", "/Users/<USER>/Desktop/]ig/NIENIE/build"], "outputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-Release-iphoneos--arm64-build-headers-stale-file-removal>"]}, "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-Release-iphoneos--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Info.plist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/assert_arc_enabled.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/cxx_utils.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/error_utils.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_coreml_execution_provider.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_enums.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_env.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_session.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_value.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_xnnpack_execution_provider.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime-objc-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_coreml_execution_provider.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_custom_op_registration.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_enums.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_env.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_session.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_value.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_xnnpack_execution_provider.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/onnxruntime_objc", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/DerivedSources/onnxruntime_objc_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-generated-files.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-project-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.hmap"], "roots": ["/tmp/Pods.dst", "/Users/<USER>/Desktop/]ig/NIENIE/build", "/Users/<USER>/Desktop/]ig/NIENIE/build"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-Release-iphoneos--arm64-build-headers-stale-file-removal>"]}, "<workspace-Release--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-bfdfe7dc352907fc980b868725387e98-VFS-iphoneos/all-product-headers.yaml"], "outputs": ["<workspace-Release--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Pods.xcodeproj", "signature": "b77dae8fe6f68d91a04884a76c1e1add"}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/]ig/NIENIE/build": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/]ig/NIENIE/build", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "/Users/<USER>/Desktop/]ig/NIENIE/build"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>", "/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-bfdfe7dc352907fc980b868725387e98-VFS-iphoneos/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyStaticMetadataFileList"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-ChangePermissions>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--will-sign>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-StripSymbols>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--will-sign>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-ChangePermissions>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-ChangeAlternatePermissions>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--will-sign>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-CodeSign>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-GenerateStubAPI>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--will-sign>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-CopyAside>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ProductPostprocessingTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-CodeSign>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--will-sign>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-Validate>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--will-sign>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "<Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-RegisterProduct>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-CopyAside>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--will-sign>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-StripSymbols>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-Validate": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-RegisterExecutionPolicyException>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--will-sign>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-Validate>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--CustomTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--DocumentationTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--GeneratedFilesTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ProductStructureTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-generated-files.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-project-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.hmap"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--HeadermapTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Info.plist"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--InfoPlistTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules/module.modulemap"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleMapTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--RealityAssetsTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleMapTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftPackageCopyFilesTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--InfoPlistTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SanitizerTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftStandardLibrariesTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftFrameworkABICheckerTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftABIBaselineGenerationTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TestTargetTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TestHostTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--CopySwiftPackageResourcesTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TAPISymbolExtractorTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--DocumentationTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--CustomTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--StubBinaryTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--AppIntentsMetadataTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--start>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ProductStructureTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--HeadermapTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--RealityAssetsTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SanitizerTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--StubBinaryTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TestHostTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ProductPostprocessingTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TestTargetTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--copy-headers-completion": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules/module.modulemap"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--copy-headers-completion>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase0-copy-headers": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers/Pods-NIENIE-NIENIEUITests-umbrella.h"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase0-copy-headers>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase1-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase0-copy-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/DerivedSources/Pods_NIENIE_NIENIEUITests_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase1-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--generated-headers": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--generated-headers>"]}, "P0:::Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--swift-generated-headers": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--swift-generated-headers>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyStaticMetadataFileList"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-ChangePermissions>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--will-sign>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-StripSymbols>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--will-sign>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-ChangePermissions>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-ChangeAlternatePermissions>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--will-sign>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-CodeSign>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-GenerateStubAPI>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--will-sign>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-CopyAside>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ProductPostprocessingTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-CodeSign>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--will-sign>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--<PERSON><PERSON>-RegisterExecutionPolicyException>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-Validate>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--will-sign>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "<Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--<PERSON>ier-RegisterProduct>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-CopyAside>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--will-sign>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-StripSymbols>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-Validate": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--<PERSON><PERSON>-RegisterExecutionPolicyException>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--will-sign>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-Validate>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--CustomTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--DocumentationTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--GeneratedFilesTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ProductStructureTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-generated-files.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-project-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.hmap"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--HeadermapTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Info.plist"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--InfoPlistTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules/module.modulemap"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleMapTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--RealityAssetsTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleMapTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftPackageCopyFilesTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--InfoPlistTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SanitizerTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftStandardLibrariesTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftFrameworkABICheckerTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftABIBaselineGenerationTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TestTargetTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TestHostTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--CopySwiftPackageResourcesTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TAPISymbolExtractorTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--DocumentationTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--CustomTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--StubBinaryTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--AppIntentsMetadataTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--start>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ProductStructureTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--HeadermapTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--RealityAssetsTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SanitizerTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--StubBinaryTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TestHostTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ProductPostprocessingTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TestTargetTaskProducer>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--copy-headers-completion": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules/module.modulemap"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--copy-headers-completion>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase0-copy-headers": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers/Pods-NIENIE-umbrella.h"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase0-copy-headers>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase1-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase0-copy-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/DerivedSources/Pods_NIENIE_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase1-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--generated-headers": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--generated-headers>"]}, "P0:::Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--swift-generated-headers": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--swift-generated-headers>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyStaticMetadataFileList"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-ChangePermissions>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--will-sign>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-StripSymbols>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--will-sign>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-ChangePermissions>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-ChangeAlternatePermissions>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--will-sign>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-CodeSign>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-GenerateStubAPI>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--will-sign>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-CopyAside>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ProductPostprocessingTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-CodeSign>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--will-sign>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-Validate>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--will-sign>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "<Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-RegisterProduct>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-CopyAside>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--will-sign>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-StripSymbols>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-Validate": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-RegisterExecutionPolicyException>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--will-sign>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-Validate>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--CustomTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--DocumentationTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--GeneratedFilesTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ProductStructureTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-generated-files.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-project-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.hmap"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--HeadermapTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Info.plist"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--InfoPlistTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules/module.modulemap"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleMapTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--RealityAssetsTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleMapTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftPackageCopyFilesTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--InfoPlistTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SanitizerTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftStandardLibrariesTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftFrameworkABICheckerTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftABIBaselineGenerationTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TestTargetTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TestHostTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--CopySwiftPackageResourcesTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TAPISymbolExtractorTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--DocumentationTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--CustomTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--StubBinaryTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--AppIntentsMetadataTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--start>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ProductStructureTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--HeadermapTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--RealityAssetsTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SanitizerTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--StubBinaryTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TestHostTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ProductPostprocessingTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TestTargetTaskProducer>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--copy-headers-completion": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules/module.modulemap"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--copy-headers-completion>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase0-copy-headers": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers/Pods-NIENIETests-umbrella.h"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase0-copy-headers>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase1-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase0-copy-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/Pods_NIENIETests_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase1-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--generated-headers": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--generated-headers>"]}, "P0:::Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--swift-generated-headers": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--swift-generated-headers>"]}, "P0:::Gate target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--fused-phase0--cp--copy-xcframeworks": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--start>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/XCFrameworkIntermediates/onnxruntime-c/onnxruntime.framework", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/InputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-input-files-895d2e6a9bdc7486ec0417646b06ea49-resolved.xcfilelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/OutputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-output-files-656e02bc143860ae06dd67f9f8235416-resolved.xcfilelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Script-3CFBA14174592D8554CB04535DA6DCD3.sh"], "outputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--fused-phase0--cp--copy-xcframeworks>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyStaticMetadataFileList"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-ChangePermissions>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--will-sign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-StripSymbols>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--will-sign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-ChangePermissions>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-ChangeAlternatePermissions>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--will-sign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-CodeSign>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-GenerateStubAPI>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--will-sign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-CopyAside>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ProductPostprocessingTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-CodeSign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--will-sign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-Validate>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--will-sign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-RegisterProduct>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-CopyAside>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--will-sign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-StripSymbols>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-Validate": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-RegisterExecutionPolicyException>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--will-sign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-Validate>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--CustomTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--DocumentationTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--GeneratedFilesTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ProductStructureTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-generated-files.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-project-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.hmap"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--HeadermapTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Info.plist"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--InfoPlistTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleMapTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--RealityAssetsTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleMapTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftPackageCopyFilesTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--InfoPlistTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SanitizerTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftStandardLibrariesTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftFrameworkABICheckerTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftABIBaselineGenerationTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TestTargetTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TestHostTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--CopySwiftPackageResourcesTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TAPISymbolExtractorTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--DocumentationTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--CustomTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--StubBinaryTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--AppIntentsMetadataTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--start>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ProductStructureTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--HeadermapTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--RealityAssetsTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SanitizerTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--StubBinaryTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TestHostTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ProductPostprocessingTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TestTargetTaskProducer>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--copy-headers-completion": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--copy-headers-completion>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime-objc-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_coreml_execution_provider.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_custom_op_registration.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_enums.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_env.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_session.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_value.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_xnnpack_execution_provider.h"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase1-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/assert_arc_enabled.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/cxx_utils.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/error_utils.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_coreml_execution_provider.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_enums.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_env.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_session.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_value.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_xnnpack_execution_provider.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/DerivedSources/onnxruntime_objc_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc.LinkFileList"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase1-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>"]}, "P0:::Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--end>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-linking": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--end>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-linking>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-scanning": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--scan-inputs-ready>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--scan-inputs-ready>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--scan-inputs-ready>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-scanning>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--end": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--entry>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Info.plist", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o.scan", "<Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers/Pods-NIENIE-NIENIEUITests-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/DerivedSources/Pods_NIENIE_NIENIEUITests_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-generated-files.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-project-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.hmap", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--AppIntentsMetadataTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-ChangeAlternatePermissions>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-ChangePermissions>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-CodeSign>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-CopyAside>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-GenerateStubAPI>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-RegisterExecutionPolicyException>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-RegisterProduct>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-StripSymbols>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-Validate>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--CopySwiftPackageResourcesTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--CustomTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--DocumentationTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--GeneratedFilesTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--HeadermapTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--InfoPlistTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleMapTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ProductPostprocessingTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ProductStructureTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--RealityAssetsTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SanitizerTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--StubBinaryTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftABIBaselineGenerationTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftFrameworkABICheckerTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftPackageCopyFilesTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--SwiftStandardLibrariesTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TAPISymbolExtractorTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TestHostTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TestTargetPostprocessingTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--TestTargetTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--copy-headers-completion>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase0-copy-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--generated-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--swift-generated-headers>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--end>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--entry": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--end>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--entry>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--end>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests.LinkFileList"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--linker-inputs-ready>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--modules-ready": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers/Pods-NIENIE-NIENIEUITests-umbrella.h"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--modules-ready>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers/Pods-NIENIE-NIENIEUITests-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/DerivedSources/Pods_NIENIE_NIENIEUITests_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyStaticMetadataFileList", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-GenerateStubAPI>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--unsigned-product-ready>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Gate target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--will-sign": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--unsigned-product-ready>"], "outputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--will-sign>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--start>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--start>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:ProcessInfoPlistFile /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Info.plist /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-Info.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Info.plist /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-Info.plist", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-Info.plist", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--entry>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Info.plist"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-CodeSign>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--will-sign>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework>"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-dummy.m", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--generated-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--swift-generated-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase0-copy-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o.scan"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/DerivedSources/Pods_NIENIE_NIENIEUITests_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/DerivedSources/Pods_NIENIE_NIENIEUITests_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/DerivedSources/Pods_NIENIE_NIENIEUITests_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--generated-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--swift-generated-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase0-copy-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o.scan"]}, "P0:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework": {"tool": "shell", "description": "Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--Barrier-Validate>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--will-sign>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--entry>"], "outputs": ["<Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "signature": "2e76e7d0fb083d13515f5d4ffa54420c"}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-linking": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-linking>"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-scanning": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--scan-inputs-ready>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--scan-inputs-ready>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--scan-inputs-ready>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-scanning>"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--end": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--entry>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Info.plist", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o.scan", "<Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers/Pods-NIENIE-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/DerivedSources/Pods_NIENIE_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-generated-files.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-project-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.hmap", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--AppIntentsMetadataTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-ChangeAlternatePermissions>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-ChangePermissions>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-CodeSign>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-CopyAside>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-GenerateStubAPI>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--<PERSON><PERSON>-RegisterExecutionPolicyException>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--<PERSON>ier-RegisterProduct>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-StripSymbols>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-Validate>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--CopySwiftPackageResourcesTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--CustomTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--DocumentationTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--GeneratedFilesTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--HeadermapTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--InfoPlistTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleMapTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ProductPostprocessingTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ProductStructureTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--RealityAssetsTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SanitizerTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--StubBinaryTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftABIBaselineGenerationTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftFrameworkABICheckerTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftPackageCopyFilesTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--SwiftStandardLibrariesTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TAPISymbolExtractorTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TestHostTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TestTargetPostprocessingTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--TestTargetTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--copy-headers-completion>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase0-copy-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--generated-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--swift-generated-headers>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--end>"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--entry": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--entry>"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE.LinkFileList"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--linker-inputs-ready>"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--modules-ready": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers/Pods-NIENIE-umbrella.h"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--modules-ready>"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers/Pods-NIENIE-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/DerivedSources/Pods_NIENIE_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyStaticMetadataFileList", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-GenerateStubAPI>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--unsigned-product-ready>"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Gate target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--will-sign": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--unsigned-product-ready>"], "outputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--will-sign>"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--start>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework>"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--start>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers>"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:ProcessInfoPlistFile /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Info.plist /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE-Info.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Info.plist /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE-Info.plist", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE-Info.plist", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--ModuleVerifierTaskProducer>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--entry>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Info.plist"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-CodeSign>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--will-sign>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework>"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE-dummy.m", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--generated-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--swift-generated-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase0-copy-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o.scan"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/DerivedSources/Pods_NIENIE_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/DerivedSources/Pods_NIENIE_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/DerivedSources/Pods_NIENIE_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--generated-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--swift-generated-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase0-copy-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o.scan"]}, "P0:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework": {"tool": "shell", "description": "Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--Barrier-Validate>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--will-sign>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--entry>"], "outputs": ["<Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "signature": "50b116e9414041ba03a3738fd52a98e6"}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--end>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--end>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-linking": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--end>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--end>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-linking>"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-scanning": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--scan-inputs-ready>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--scan-inputs-ready>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-scanning>"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--end": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--entry>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Info.plist", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o.scan", "<Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers/Pods-NIENIETests-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/Pods_NIENIETests_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-generated-files.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-project-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.hmap", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--AppIntentsMetadataTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-ChangeAlternatePermissions>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-ChangePermissions>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-CodeSign>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-CopyAside>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-GenerateStubAPI>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-RegisterExecutionPolicyException>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-RegisterProduct>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-StripSymbols>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-Validate>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--CopySwiftPackageResourcesTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--CustomTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--DocumentationTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--GeneratedFilesTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--HeadermapTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--InfoPlistTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleMapTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ProductPostprocessingTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ProductStructureTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--RealityAssetsTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SanitizerTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--StubBinaryTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftABIBaselineGenerationTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftFrameworkABICheckerTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftPackageCopyFilesTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--SwiftStandardLibrariesTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TAPISymbolExtractorTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TestHostTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TestTargetPostprocessingTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--TestTargetTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--copy-headers-completion>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase0-copy-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--generated-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--swift-generated-headers>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--end>"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--entry": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--end>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--end>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--entry>"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate": {"tool": "phony", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--end>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--end>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests.LinkFileList"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--linker-inputs-ready>"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--modules-ready": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers/Pods-NIENIETests-umbrella.h"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--modules-ready>"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers/Pods-NIENIETests-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/Pods_NIENIETests_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyStaticMetadataFileList", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-GenerateStubAPI>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--unsigned-product-ready>"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Gate target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--will-sign": {"tool": "phony", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--unsigned-product-ready>"], "outputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--will-sign>"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--start>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework>"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--start>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers>"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:ProcessInfoPlistFile /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Info.plist /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests-Info.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Info.plist /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests-Info.plist", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests-Info.plist", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--ModuleVerifierTaskProducer>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--entry>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Info.plist"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-CodeSign>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--will-sign>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework>"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests-dummy.m", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--generated-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--swift-generated-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase0-copy-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o.scan"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/Pods_NIENIETests_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/Pods_NIENIETests_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/Pods_NIENIETests_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--generated-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--swift-generated-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase0-copy-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o.scan"]}, "P0:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework": {"tool": "shell", "description": "Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--Barrier-Validate>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--will-sign>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--entry>"], "outputs": ["<Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "signature": "ffd83a98f271acab77dc7a42ec4831b5"}, "P0:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:Gate target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-compiling": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-Release-iphoneos--arm64-build-headers-stale-file-removal>"], "outputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-compiling>"]}, "P0:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:Gate target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-linking": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-Release-iphoneos--arm64-build-headers-stale-file-removal>"], "outputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-linking>"]}, "P0:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:Gate target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-scanning": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-compiling>"], "outputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-scanning>"]}, "P0:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:Gate target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--entry>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/XCFrameworkIntermediates/onnxruntime-c/onnxruntime.framework", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/InputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-input-files-895d2e6a9bdc7486ec0417646b06ea49-resolved.xcfilelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/OutputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-output-files-656e02bc143860ae06dd67f9f8235416-resolved.xcfilelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Script-3CFBA14174592D8554CB04535DA6DCD3.sh", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--fused-phase0--cp--copy-xcframeworks>"], "outputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>"]}, "P0:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:Gate target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--entry": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-compiling>"], "outputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--entry>"]}, "P0:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:Gate target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--immediate": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-Release-iphoneos--arm64-build-headers-stale-file-removal>"], "outputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--immediate>"]}, "P0:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:Gate target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-compiling>"], "outputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--linker-inputs-ready>"]}, "P0:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:Gate target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--modules-ready": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/XCFrameworkIntermediates/onnxruntime-c/onnxruntime.framework", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/InputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-input-files-895d2e6a9bdc7486ec0417646b06ea49-resolved.xcfilelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/OutputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-output-files-656e02bc143860ae06dd67f9f8235416-resolved.xcfilelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Script-3CFBA14174592D8554CB04535DA6DCD3.sh"], "outputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--modules-ready>"]}, "P0:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:Gate target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/XCFrameworkIntermediates/onnxruntime-c/onnxruntime.framework", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/InputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-input-files-895d2e6a9bdc7486ec0417646b06ea49-resolved.xcfilelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/OutputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-output-files-656e02bc143860ae06dd67f9f8235416-resolved.xcfilelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Script-3CFBA14174592D8554CB04535DA6DCD3.sh"], "outputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--unsigned-product-ready>"]}, "P0:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:Gate target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--will-sign": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--unsigned-product-ready>"], "outputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--will-sign>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-linking": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-linking>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-scanning": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--scan-inputs-ready>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--scan-inputs-ready>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-scanning>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--entry>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework>", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Info.plist", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o.scan", "<Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/assert_arc_enabled.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/cxx_utils.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/error_utils.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_coreml_execution_provider.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_enums.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_env.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_session.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_value.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_xnnpack_execution_provider.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime-objc-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_coreml_execution_provider.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_custom_op_registration.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_enums.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_env.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_session.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_value.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_xnnpack_execution_provider.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/DerivedSources/onnxruntime_objc_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-generated-files.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-project-headers.hmap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.hmap", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--AppIntentsMetadataTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-ChangeAlternatePermissions>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-ChangePermissions>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-CodeSign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-CopyAside>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-GenerateStubAPI>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-RegisterExecutionPolicyException>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-RegisterProduct>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-StripSymbols>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-Validate>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--CopySwiftPackageResourcesTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--CustomTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--DocumentationTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--GeneratedFilesTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--HeadermapTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--InfoPlistTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleMapTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ProductPostprocessingTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ProductStructureTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--RealityAssetsTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SanitizerTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--StubBinaryTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftABIBaselineGenerationTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftFrameworkABICheckerTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftPackageCopyFilesTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--SwiftStandardLibrariesTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TAPISymbolExtractorTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TestHostTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TestTargetPostprocessingTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--TestTargetTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--copy-headers-completion>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--end>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--entry": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--entry>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate": {"tool": "phony", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--end>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Pods.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/NIENIE/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc.LinkFileList"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--linker-inputs-ready>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--modules-ready": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime-objc-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_coreml_execution_provider.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_custom_op_registration.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_enums.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_env.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_session.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_value.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_xnnpack_execution_provider.h"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--modules-ready>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o.scan", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/assert_arc_enabled.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/cxx_utils.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/error_utils.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_coreml_execution_provider.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_enums.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_env.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_session.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_value.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_xnnpack_execution_provider.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime-objc-umbrella.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_coreml_execution_provider.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_custom_op_registration.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_enums.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_env.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_session.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_value.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_xnnpack_execution_provider.h", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_libtool_dependency_info.dat", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/DerivedSources/onnxruntime_objc_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc.LinkFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyStaticMetadataFileList", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-GenerateStubAPI>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--unsigned-product-ready>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Gate target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--will-sign": {"tool": "phony", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--unsigned-product-ready>"], "outputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--will-sign>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--start>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--start>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers", "<MkDir /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:ProcessInfoPlistFile /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Info.plist /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-Info.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Info.plist /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-Info.plist", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-Info.plist", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--ModuleVerifierTaskProducer>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--entry>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Info.plist"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-CodeSign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--will-sign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework>"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-dummy.m", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o.scan"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/DerivedSources/onnxruntime_objc_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/DerivedSources/onnxruntime_objc_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/DerivedSources/onnxruntime_objc_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o.scan"]}, "P0:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework": {"tool": "shell", "description": "Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--Barrier-Validate>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--will-sign>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--entry>"], "outputs": ["<Touch /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "signature": "a511b57fea04398f9b0ce5da7c79267d"}, "P1:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-dummy.m", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o.scan", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--generated-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--swift-generated-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase0-copy-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o"]}, "P1:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/DerivedSources/Pods_NIENIE_NIENIEUITests_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/DerivedSources/Pods_NIENIE_NIENIEUITests_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/DerivedSources/Pods_NIENIE_NIENIEUITests_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o.scan", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--generated-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--swift-generated-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase0-copy-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o"]}, "P1:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE-dummy.m", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o.scan", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--generated-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--swift-generated-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase0-copy-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o"]}, "P1:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/DerivedSources/Pods_NIENIE_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/DerivedSources/Pods_NIENIE_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/DerivedSources/Pods_NIENIE_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o.scan", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--generated-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--swift-generated-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase0-copy-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o"]}, "P1:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests-dummy.m", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o.scan", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--generated-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--swift-generated-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase0-copy-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o"]}, "P1:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/Pods_NIENIETests_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/Pods_NIENIETests_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/Pods_NIENIETests_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o.scan", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--generated-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--swift-generated-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase0-copy-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o"]}, "P1:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/assert_arc_enabled.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/assert_arc_enabled.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "shell", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/assert_arc_enabled.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/assert_arc_enabled.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/assert_arc_enabled.mm", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/assert_arc_enabled.o"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wdocumentation", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-std=c++17", "-fobjc-arc-exceptions", "-Wall", "-Wextra", "-Werror", "-include", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/assert_arc_enabled.d", "--serialize-diagnostics", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/assert_arc_enabled.dia", "-c", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/assert_arc_enabled.mm", "-o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/assert_arc_enabled.o"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "deps": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/assert_arc_enabled.d"], "deps-style": "makefile", "signature": "2744c9ed53cb2de1c3f37c2bdf2b8ee2"}, "P1:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/cxx_utils.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/cxx_utils.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "shell", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/cxx_utils.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/cxx_utils.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/cxx_utils.mm", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/cxx_utils.o"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wdocumentation", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-std=c++17", "-fobjc-arc-exceptions", "-Wall", "-Wextra", "-Werror", "-include", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/cxx_utils.d", "--serialize-diagnostics", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/cxx_utils.dia", "-c", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/cxx_utils.mm", "-o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/cxx_utils.o"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "deps": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/cxx_utils.d"], "deps-style": "makefile", "signature": "558360f31b498ab6389b645c4fc6594f"}, "P1:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/error_utils.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/error_utils.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "shell", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/error_utils.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/error_utils.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/error_utils.mm", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/error_utils.o"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wdocumentation", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-std=c++17", "-fobjc-arc-exceptions", "-Wall", "-Wextra", "-Werror", "-include", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/error_utils.d", "--serialize-diagnostics", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/error_utils.dia", "-c", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/error_utils.mm", "-o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/error_utils.o"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "deps": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/error_utils.d"], "deps-style": "makefile", "signature": "29977f5eb5bf5cf82278c6403bcbc8aa"}, "P1:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-dummy.m", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o.scan", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o"]}, "P1:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/DerivedSources/onnxruntime_objc_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/DerivedSources/onnxruntime_objc_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/DerivedSources/onnxruntime_objc_vers.c", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o.scan", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o"]}, "P1:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_coreml_execution_provider.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_coreml_execution_provider.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "shell", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_coreml_execution_provider.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_coreml_execution_provider.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_coreml_execution_provider.mm", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_coreml_execution_provider.o"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wdocumentation", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-std=c++17", "-fobjc-arc-exceptions", "-Wall", "-Wextra", "-Werror", "-include", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_coreml_execution_provider.d", "--serialize-diagnostics", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_coreml_execution_provider.dia", "-c", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_coreml_execution_provider.mm", "-o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_coreml_execution_provider.o"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "deps": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_coreml_execution_provider.d"], "deps-style": "makefile", "signature": "fd8fbfed5741204a6a852c050249c8dd"}, "P1:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_enums.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_enums.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "shell", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_enums.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_enums.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_enums.mm", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_enums.o"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wdocumentation", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-std=c++17", "-fobjc-arc-exceptions", "-Wall", "-Wextra", "-Werror", "-include", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_enums.d", "--serialize-diagnostics", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_enums.dia", "-c", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_enums.mm", "-o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_enums.o"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "deps": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_enums.d"], "deps-style": "makefile", "signature": "a0b89d5de4bb8f0bc65461e73a308e0a"}, "P1:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_env.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_env.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "shell", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_env.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_env.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_env.mm", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_env.o"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wdocumentation", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-std=c++17", "-fobjc-arc-exceptions", "-Wall", "-Wextra", "-Werror", "-include", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_env.d", "--serialize-diagnostics", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_env.dia", "-c", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_env.mm", "-o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_env.o"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "deps": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_env.d"], "deps-style": "makefile", "signature": "b60a83395d0db361d603ce820fccc1fe"}, "P1:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_session.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_session.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "shell", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_session.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_session.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_session.mm", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_session.o"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wdocumentation", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-std=c++17", "-fobjc-arc-exceptions", "-Wall", "-Wextra", "-Werror", "-include", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_session.d", "--serialize-diagnostics", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_session.dia", "-c", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_session.mm", "-o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_session.o"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "deps": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_session.d"], "deps-style": "makefile", "signature": "1489f4ea28d6d47f4fd360f9f4aa5b9a"}, "P1:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_value.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_value.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "shell", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_value.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_value.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_value.mm", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_value.o"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wdocumentation", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-std=c++17", "-fobjc-arc-exceptions", "-Wall", "-Wextra", "-Werror", "-include", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_value.d", "--serialize-diagnostics", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_value.dia", "-c", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_value.mm", "-o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_value.o"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "deps": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_value.d"], "deps-style": "makefile", "signature": "e5134abf83e8edc882355cfa1f195e55"}, "P1:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_xnnpack_execution_provider.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_xnnpack_execution_provider.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "shell", "description": "CompileC /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_xnnpack_execution_provider.o /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_xnnpack_execution_provider.mm normal arm64 objective-c++ com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_xnnpack_execution_provider.mm", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap", "<ClangStatCache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--swift-generated-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_xnnpack_execution_provider.o"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wdocumentation", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-std=c++17", "-fobjc-arc-exceptions", "-Wall", "-Wextra", "-Werror", "-include", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_xnnpack_execution_provider.d", "--serialize-diagnostics", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_xnnpack_execution_provider.dia", "-c", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/ort_xnnpack_execution_provider.mm", "-o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_xnnpack_execution_provider.o"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "deps": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_xnnpack_execution_provider.d"], "deps-style": "makefile", "signature": "9e1c71334a87dfd4c4cc117bfad482d1"}, "P2:::WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-bfdfe7dc352907fc980b868725387e98-VFS-iphoneos/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-bfdfe7dc352907fc980b868725387e98-VFS-iphoneos/all-product-headers.yaml", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-bfdfe7dc352907fc980b868725387e98-VFS-iphoneos/all-product-headers.yaml"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests.modulemap": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests.modulemap", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests.modulemap/", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/module.modulemap"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/module.modulemap": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/module.modulemap", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/module.modulemap/", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Modules/module.modulemap"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers/Pods-NIENIE-NIENIEUITests-umbrella.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-umbrella.h": {"tool": "file-copy", "description": "CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers/Pods-NIENIE-NIENIEUITests-umbrella.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-umbrella.h", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE-NIENIEUITests/Pods-NIENIE-NIENIEUITests-umbrella.h/", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Headers/Pods-NIENIE-NIENIEUITests-umbrella.h"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:Libtool /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Pods_NIENIE_NIENIEUITests normal": {"tool": "shell", "description": "Libtool /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Pods_NIENIE_NIENIEUITests normal", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods-NIENIE-NIENIEUITests-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests.LinkFileList", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--fused-phase0-copy-headers>", "<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Pods_NIENIE_NIENIEUITests", "<Linked Binary /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Pods_NIENIE_NIENIEUITests>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_libtool_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool", "-static", "-arch_only", "arm64", "-D", "-s<PERSON><PERSON><PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "-L/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos", "-filelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests.LinkFileList", "-framework", "Foundation", "-dependency_info", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_libtool_dependency_info.dat", "-o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE_NIENIEUITests.framework/Pods_NIENIE_NIENIEUITests"], "env": {"IPHONEOS_DEPLOYMENT_TARGET": "17.6"}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "deps": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests_libtool_dependency_info.dat"], "deps-style": "dependency-info", "signature": "a96ce262fd7d66d72169c26be31d2a8a"}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/DerivedSources/Pods_NIENIE_NIENIEUITests_vers.c": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/DerivedSources/Pods_NIENIE_NIENIEUITests_vers.c", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/DerivedSources/Pods_NIENIE_NIENIEUITests_vers.c"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests.LinkFileList", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/Pods_NIENIE_NIENIEUITests.LinkFileList"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-all-non-framework-target-headers.hmap", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-all-non-framework-target-headers.hmap"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-all-target-headers.hmap", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-all-target-headers.hmap"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-generated-files.hmap", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-generated-files.hmap"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-own-target-headers.hmap", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-own-target-headers.hmap"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-project-headers.hmap", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests-project-headers.hmap"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyMetadataFileList", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyMetadataFileList"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyStaticMetadataFileList", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.DependencyStaticMetadataFileList"]}, "P2:target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.hmap", "inputs": ["<target-Pods-NIENIE-NIENIEUITests-bfdfe7dc352907fc980b868725387e98e08e6a0444249c4710bc89add9ea1e22--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE-NIENIEUITests.build/Pods_NIENIE_NIENIEUITests.hmap"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE.modulemap": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE.modulemap", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE.modulemap/", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/module.modulemap"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/module.modulemap": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/module.modulemap", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/module.modulemap/", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Modules/module.modulemap"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers/Pods-NIENIE-umbrella.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE-umbrella.h": {"tool": "file-copy", "description": "CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers/Pods-NIENIE-umbrella.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE-umbrella.h", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIE/Pods-NIENIE-umbrella.h/", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Headers/Pods-NIENIE-umbrella.h"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:Libtool /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Pods_NIENIE normal": {"tool": "shell", "description": "Libtool /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Pods_NIENIE normal", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods-NIENIE-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE.LinkFileList", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--fused-phase0-copy-headers>", "<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Pods_NIENIE", "<Linked Binary /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Pods_NIENIE>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_libtool_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool", "-static", "-arch_only", "arm64", "-D", "-s<PERSON><PERSON><PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "-L/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos", "-filelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE.LinkFileList", "-framework", "Foundation", "-dependency_info", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_libtool_dependency_info.dat", "-o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIE.framework/Pods_NIENIE"], "env": {"IPHONEOS_DEPLOYMENT_TARGET": "17.6"}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "deps": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE_libtool_dependency_info.dat"], "deps-style": "dependency-info", "signature": "84571b5c02fdb38364b4d4076bf5fffc"}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/DerivedSources/Pods_NIENIE_vers.c": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/DerivedSources/Pods_NIENIE_vers.c", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/DerivedSources/Pods_NIENIE_vers.c"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE.LinkFileList", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/Pods_NIENIE.LinkFileList"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-all-non-framework-target-headers.hmap", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-all-non-framework-target-headers.hmap"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-all-target-headers.hmap", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-all-target-headers.hmap"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-generated-files.hmap", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-generated-files.hmap"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-own-target-headers.hmap", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-own-target-headers.hmap"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-project-headers.hmap", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE-project-headers.hmap"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyMetadataFileList", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyMetadataFileList"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyStaticMetadataFileList", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.DependencyStaticMetadataFileList"]}, "P2:target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.hmap", "inputs": ["<target-Pods-NIENIE-bfdfe7dc352907fc980b868725387e98eebaa505ac26acebe01379f1248e4742--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIE.build/Pods_NIENIE.hmap"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests.modulemap": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests.modulemap", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests.modulemap/", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/module.modulemap"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/module.modulemap": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/module.modulemap", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/module.modulemap/", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Modules/module.modulemap"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers/Pods-NIENIETests-umbrella.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests-umbrella.h": {"tool": "file-copy", "description": "CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers/Pods-NIENIETests-umbrella.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests-umbrella.h", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/Pods-NIENIETests/Pods-NIENIETests-umbrella.h/", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Headers/Pods-NIENIETests-umbrella.h"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:Libtool /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Pods_NIENIETests normal": {"tool": "shell", "description": "Libtool /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Pods_NIENIETests normal", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods-NIENIETests-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests.LinkFileList", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--fused-phase0-copy-headers>", "<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Pods_NIENIETests", "<Linked Binary /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Pods_NIENIETests>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_libtool_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool", "-static", "-arch_only", "arm64", "-D", "-s<PERSON><PERSON><PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "-L/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos", "-filelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests.LinkFileList", "-framework", "Foundation", "-dependency_info", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_libtool_dependency_info.dat", "-o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/Pods_NIENIETests.framework/Pods_NIENIETests"], "env": {"IPHONEOS_DEPLOYMENT_TARGET": "17.6"}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "deps": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests_libtool_dependency_info.dat"], "deps-style": "dependency-info", "signature": "b71c03cb1a9b6ebb6f0b3a1f27b1fd41"}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/Pods_NIENIETests_vers.c": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/Pods_NIENIETests_vers.c", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/DerivedSources/Pods_NIENIETests_vers.c"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests.LinkFileList", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/Pods_NIENIETests.LinkFileList"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-all-non-framework-target-headers.hmap", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-all-non-framework-target-headers.hmap"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-all-target-headers.hmap", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-all-target-headers.hmap"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-generated-files.hmap", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-generated-files.hmap"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-own-target-headers.hmap", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-own-target-headers.hmap"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-project-headers.hmap", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests-project-headers.hmap"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyMetadataFileList", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyMetadataFileList"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyStaticMetadataFileList", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.DependencyStaticMetadataFileList"]}, "P2:target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.hmap", "inputs": ["<target-Pods-NIENIETests-bfdfe7dc352907fc980b868725387e987f86042f32eb6b054024794cdd4285e8--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/Pods-NIENIETests.build/Pods_NIENIETests.hmap"]}, "P2:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:PhaseScriptExecution [CP] Copy XCFrameworks /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Script-3CFBA14174592D8554CB04535DA6DCD3.sh": {"tool": "shell", "description": "PhaseScriptExecution [CP] Copy XCFrameworks /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Script-3CFBA14174592D8554CB04535DA6DCD3.sh", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-c/onnxruntime-c-xcframeworks-input-files.xcfilelist/", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-c/onnxruntime-c-xcframeworks-output-files.xcfilelist", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-c/onnxruntime-c-xcframeworks.sh/", "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-c/onnxruntime.xcframework/", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/InputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-input-files-895d2e6a9bdc7486ec0417646b06ea49-resolved.xcfilelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/OutputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-output-files-656e02bc143860ae06dd67f9f8235416-resolved.xcfilelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Script-3CFBA14174592D8554CB04535DA6DCD3.sh", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--start>", "<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--entry>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/XCFrameworkIntermediates/onnxruntime-c/onnxruntime.framework"], "args": ["/bin/sh", "-c", "/Users/<USER>/Desktop/\\]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Script-3CFBA14174592D8554CB04535DA6DCD3.sh"], "env": {"ACTION": "build", "AD_HOC_CODE_SIGNING_ALLOWED": "NO", "ALLOW_BUILD_REQUEST_OVERRIDES": "NO", "ALLOW_TARGET_PLATFORM_SPECIALIZATION": "NO", "ALTERNATE_GROUP": "staff", "ALTERNATE_MODE": "u+w,go-w,a+rX", "ALTERNATE_OWNER": "<PERSON><PERSON><PERSON><PERSON>", "ALTERNATIVE_DISTRIBUTION_WEB": "NO", "ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "ALWAYS_SEARCH_USER_PATHS": "NO", "ALWAYS_USE_SEPARATE_HEADERMAPS": "NO", "APPLICATION_EXTENSION_API_ONLY": "NO", "APPLY_RULES_IN_COPY_FILES": "NO", "APPLY_RULES_IN_COPY_HEADERS": "NO", "APP_SHORTCUTS_ENABLE_FLEXIBLE_MATCHING": "YES", "ARCHS": "arm64", "ARCHS_STANDARD": "arm64", "ARCHS_STANDARD_32_64_BIT": "armv7 arm64", "ARCHS_STANDARD_32_BIT": "armv7", "ARCHS_STANDARD_64_BIT": "arm64", "ARCHS_STANDARD_INCLUDING_64_BIT": "arm64", "ARCHS_UNIVERSAL_IPHONE_OS": "armv7 arm64", "ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "AUTOMATICALLY_MERGE_DEPENDENCIES": "NO", "AVAILABLE_PLATFORMS": "android appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx qnx watchos watchsimulator xros xrsimulator", "BITCODE_GENERATION_MODE": "marker", "BUILD_ACTIVE_RESOURCES_ONLY": "NO", "BUILD_COMPONENTS": "headers build", "BUILD_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build", "BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "BUILD_ROOT": "/Users/<USER>/Desktop/]ig/NIENIE/build", "BUILD_STYLE": "", "BUILD_VARIANTS": "normal", "BUILT_PRODUCTS_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c", "BUNDLE_CONTENTS_FOLDER_PATH_deep": "Contents/", "BUNDLE_EXECUTABLE_FOLDER_NAME_deep": "MacOS", "BUNDLE_EXTENSIONS_FOLDER_PATH": "Extensions", "BUNDLE_FORMAT": "shallow", "BUNDLE_FRAMEWORKS_FOLDER_PATH": "Frameworks", "BUNDLE_PLUGINS_FOLDER_PATH": "PlugIns", "BUNDLE_PRIVATE_HEADERS_FOLDER_PATH": "PrivateHeaders", "BUNDLE_PUBLIC_HEADERS_FOLDER_PATH": "Headers", "CACHE_ROOT": "/var/folders/2s/c4x2yp356596ht4cd1scc_1r0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode", "CCHROOT": "/var/folders/2s/c4x2yp356596ht4cd1scc_1r0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode", "CHMOD": "/bin/chmod", "CHOWN": "/usr/sbin/chown", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CACHE_FINE_GRAINED_OUTPUTS": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_EXPLICIT_MODULES": "YES", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_MODULES_BUILD_SESSION_FILE": "/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "CLASS_FILE_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/JavaClasses", "CLEAN_PRECOMPS": "YES", "CLONE_HEADERS": "NO", "CODESIGNING_FOLDER_PATH": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c/", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_REQUIRED": "YES", "CODE_SIGN_CONTEXT_CLASS": "XCiPhoneOSCodeSignContext", "CODE_SIGN_IDENTITY": "Apple Development", "CODE_SIGN_INJECT_BASE_ENTITLEMENTS": "YES", "COLOR_DIAGNOSTICS": "NO", "COMBINE_HIDPI_IMAGES": "NO", "COMPILATION_CACHE_CAS_PATH": "/Users/<USER>/Library/Developer/Xcode/DerivedData/CompilationCache.noindex", "COMPILATION_CACHE_KEEP_CAS_DIRECTORY": "YES", "COMPILER_INDEX_STORE_ENABLE": "<PERSON><PERSON><PERSON>", "COMPOSITE_SDK_DIRS": "/Users/<USER>/Desktop/]ig/NIENIE/build/CompositeSDKs", "COMPRESS_PNG_FILES": "YES", "CONFIGURATION": "Release", "CONFIGURATION_BUILD_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c", "CONFIGURATION_TEMP_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos", "COPYING_PRESERVES_HFS_DATA": "NO", "COPY_HEADERS_RUN_UNIFDEF": "NO", "COPY_PHASE_STRIP": "NO", "CORRESPONDING_SIMULATOR_PLATFORM_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform", "CORRESPONDING_SIMULATOR_PLATFORM_NAME": "iphonesimulator", "CORRESPONDING_SIMULATOR_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "CORRESPONDING_SIMULATOR_SDK_NAME": "iphonesimulator18.5", "CP": "/bin/cp", "CREATE_INFOPLIST_SECTION_IN_BINARY": "NO", "CURRENT_ARCH": "undefined_arch", "CURRENT_VARIANT": "normal", "DEAD_CODE_STRIPPING": "YES", "DEBUGGING_SYMBOLS": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEBUG_INFORMATION_VERSION": "compiler-default", "DEFAULT_COMPILER": "com.apple.compilers.llvm.clang.1_0", "DEFAULT_DEXT_INSTALL_PATH": "/System/Library/DriverExtensions", "DEFAULT_KEXT_INSTALL_PATH": "/System/Library/Extensions", "DEFINES_MODULE": "NO", "DEPLOYMENT_LOCATION": "NO", "DEPLOYMENT_POSTPROCESSING": "NO", "DEPLOYMENT_TARGET_SETTING_NAME": "IPHONEOS_DEPLOYMENT_TARGET", "DEPLOYMENT_TARGET_SUGGESTED_VALUES": "12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2 14.3 14.4 14.5 14.6 14.7 15.0 15.1 15.2 15.3 15.4 15.5 15.6 16.0 16.1 16.2 16.3 16.4 16.5 16.6 17.0 17.1 17.2 17.3 17.4 17.5 17.6 18.0 18.1 18.2 18.3 18.4 18.5", "DERIVED_FILES_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/DerivedSources", "DERIVED_FILE_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/DerivedSources", "DERIVED_SOURCES_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/DerivedSources", "DERIVE_MACCATALYST_PRODUCT_BUNDLE_IDENTIFIER": "NO", "DEVELOPER_APPLICATIONS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "DEVELOPER_FRAMEWORKS_DIR": "/Applications/Xcode.app/Contents/Developer/Library/Frameworks", "DEVELOPER_FRAMEWORKS_DIR_QUOTED": "/Applications/Xcode.app/Contents/Developer/Library/Frameworks", "DEVELOPER_LIBRARY_DIR": "/Applications/Xcode.app/Contents/Developer/Library", "DEVELOPER_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs", "DEVELOPER_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Tools", "DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "DEVELOPMENT_LANGUAGE": "en", "DIAGNOSE_MISSING_TARGET_DEPENDENCIES": "YES", "DIFF": "/usr/bin/diff", "DONT_GENERATE_INFOPLIST_FILE": "NO", "DSTROOT": "/tmp/Pods.dst", "DT_TOOLCHAIN_DIR": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "DWARF_DSYM_FILE_NAME": ".dSYM", "DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT": "NO", "DWARF_DSYM_FOLDER_PATH": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c", "DYNAMIC_LIBRARY_EXTENSION": "dylib", "EAGER_COMPILATION_ALLOW_SCRIPTS": "NO", "EAGER_LINKING": "NO", "EFFECTIVE_PLATFORM_NAME": "-iphoneos", "EMBEDDED_CONTENT_CONTAINS_SWIFT": "NO", "EMBEDDED_PROFILE_NAME": "embedded.mobileprovision", "EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE": "NO", "ENABLE_APP_SANDBOX": "NO", "ENABLE_BITCODE": "NO", "ENABLE_CODE_COVERAGE": "YES", "ENABLE_DEFAULT_HEADER_SEARCH_PATHS": "YES", "ENABLE_DEFAULT_SEARCH_PATHS": "YES", "ENABLE_HARDENED_RUNTIME": "NO", "ENABLE_HEADER_DEPENDENCIES": "YES", "ENABLE_INCOMING_NETWORK_CONNECTIONS": "NO", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_ON_DEMAND_RESOURCES": "NO", "ENABLE_OUTGOING_NETWORK_CONNECTIONS": "NO", "ENABLE_PREVIEWS": "NO", "ENABLE_RESOURCE_ACCESS_AUDIO_INPUT": "NO", "ENABLE_RESOURCE_ACCESS_BLUETOOTH": "NO", "ENABLE_RESOURCE_ACCESS_CALENDARS": "NO", "ENABLE_RESOURCE_ACCESS_CAMERA": "NO", "ENABLE_RESOURCE_ACCESS_CONTACTS": "NO", "ENABLE_RESOURCE_ACCESS_LOCATION": "NO", "ENABLE_RESOURCE_ACCESS_PRINTING": "NO", "ENABLE_RESOURCE_ACCESS_USB": "NO", "ENABLE_SDK_IMPORTS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "NO", "ENABLE_TESTING_SEARCH_PATHS": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "ENABLE_XOJIT_PREVIEWS": "NO", "ENFORCE_VALID_ARCHS": "YES", "ENTITLEMENTS_DESTINATION": "Signature", "ENTITLEMENTS_REQUIRED": "YES", "EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS": ".DS_Store .svn .git .hg CVS", "EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES": "*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj", "FILE_LIST": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Objects/LinkFileList", "FIXED_FILES_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/FixedFiles", "FRAMEWORK_SEARCH_PATHS": " \"/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-c\" \"/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/XCFrameworkIntermediates/onnxruntime-c\"", "FRAMEWORK_VERSION": "A", "FUSE_BUILD_PHASES": "YES", "FUSE_BUILD_SCRIPT_PHASES": "NO", "GCC3_VERSION": "3.3", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PFE_FILE_C_DIALECTS": "c objective-c c++ objective-c++", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1  COCOAPODS=1", "GCC_THUMB_SUPPORT": "YES", "GCC_TREAT_WARNINGS_AS_ERRORS": "NO", "GCC_VERSION": "com.apple.compilers.llvm.clang.1_0", "GCC_VERSION_IDENTIFIER": "com_apple_compilers_llvm_clang_1_0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "GENERATED_MODULEMAP_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/GeneratedModuleMaps-iphoneos", "GENERATE_INFOPLIST_FILE": "NO", "GENERATE_INTERMEDIATE_TEXT_BASED_STUBS": "YES", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_PKGINFO_FILE": "NO", "GENERATE_PROFILING_CODE": "NO", "GENERATE_TEXT_BASED_STUBS": "NO", "GID": "20", "GROUP": "staff", "HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT": "YES", "HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES": "YES", "HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_TARGETS_NOT_BEING_BUILT": "YES", "HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS": "YES", "HEADERMAP_INCLUDES_PROJECT_HEADERS": "YES", "HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES": "YES", "HEADERMAP_USES_VFS": "NO", "HEADER_SEARCH_PATHS": " \"/Users/<USER>/Desktop/]ig/NIENIE/Pods/Headers/Private\" \"/Users/<USER>/Desktop/]ig/NIENIE/Pods/Headers/Private/onnxruntime-c\" \"/Users/<USER>/Desktop/]ig/NIENIE/Pods/Headers/Public\"", "HIDE_BITCODE_SYMBOLS": "YES", "HOME": "/Users/<USER>", "HOST_ARCH": "arm64", "HOST_PLATFORM": "macosx", "ICONV": "/usr/bin/iconv", "IMPLICIT_DEPENDENCY_DOMAIN": "default", "INFOPLIST_ENABLE_CFBUNDLEICONS_MERGE": "YES", "INFOPLIST_EXPAND_BUILD_SETTINGS": "YES", "INFOPLIST_OUTPUT_FORMAT": "binary", "INFOPLIST_PREPROCESS": "NO", "INLINE_PRIVATE_FRAMEWORKS": "NO", "INSTALLAPI_IGNORE_SKIP_INSTALL": "YES", "INSTALLHDRS_COPY_PHASE": "NO", "INSTALLHDRS_SCRIPT_PHASE": "NO", "INSTALL_DIR": "/tmp/Pods.dst", "INSTALL_GROUP": "staff", "INSTALL_MODE_FLAG": "u+w,go-w,a+rX", "INSTALL_OWNER": "<PERSON><PERSON><PERSON><PERSON>", "INSTALL_ROOT": "/tmp/Pods.dst", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IS_UNOPTIMIZED_BUILD": "NO", "JAVAC_DEFAULT_FLAGS": "-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8", "JAVA_APP_STUB": "/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub", "JAVA_ARCHIVE_CLASSES": "YES", "JAVA_ARCHIVE_TYPE": "JAR", "JAVA_COMPILER": "/usr/bin/javac", "JAVA_FRAMEWORK_RESOURCES_DIRS": "Resources", "JAVA_JAR_FLAGS": "cv", "JAVA_SOURCE_SUBDIR": ".", "JAVA_USE_DEPENDENCIES": "YES", "JAVA_ZIP_FLAGS": "-urg", "JIKES_DEFAULT_FLAGS": "+E +OLDCSO", "KASAN_CFLAGS_CLASSIC": "-DKASAN=1 -DKASAN_CLASSIC=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow", "KASAN_CFLAGS_TBI": "-DKASAN=1 -DKASAN_TBI=1 -fsanitize=kernel-hwaddress -mllvm -hwasan-recover=0 -mllvm -hwasan-instrument-atomics=0 -mllvm -hwasan-instrument-stack=1 -mllvm -hwasan-generate-tags-with-calls=1 -mllvm -hwasan-instrument-with-calls=1 -mllvm -hwasan-use-short-granules=0 -mllvm -hwasan-memory-access-callback-prefix=__asan_", "KASAN_DEFAULT_CFLAGS": "-DKASAN=1 -DKASAN_CLASSIC=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow", "KEEP_PRIVATE_EXTERNS": "NO", "LD_DEPENDENCY_INFO_FILE": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Objects-normal/undefined_arch/onnxruntime-c_dependency_info.dat", "LD_EXPORT_SYMBOLS": "YES", "LD_GENERATE_MAP_FILE": "NO", "LD_MAP_FILE_PATH": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/onnxruntime-c-LinkMap-normal-undefined_arch.txt", "LD_NO_PIE": "NO", "LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER": "YES", "LD_RUNPATH_SEARCH_PATHS": " @executable_path/Frameworks", "LD_SHARED_CACHE_ELIGIBLE": "Automatic", "LD_WARN_DUPLICATE_LIBRARIES": "NO", "LD_WARN_UNUSED_DYLIBS": "NO", "LEGACY_DEVELOPER_DIR": "/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer", "LEX": "lex", "LIBRARY_DEXT_INSTALL_PATH": "/Library/DriverExtensions", "LIBRARY_FLAG_NOSPACE": "YES", "LIBRARY_KEXT_INSTALL_PATH": "/Library/Extensions", "LINKER_DISPLAYS_MANGLED_NAMES": "NO", "LINK_FILE_LIST_normal_arm64": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Objects-normal/arm64/onnxruntime-c.LinkFileList", "LINK_OBJC_RUNTIME": "YES", "LINK_WITH_STANDARD_LIBRARIES": "YES", "LLVM_TARGET_TRIPLE_OS_VERSION": "ios12.0", "LLVM_TARGET_TRIPLE_VENDOR": "apple", "LM_AUX_CONST_METADATA_LIST_PATH_normal_arm64": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Objects-normal/arm64/onnxruntime-c.SwiftConstValuesFileList", "LOCALIZATION_EXPORT_SUPPORTED": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "NO", "LOCALIZED_STRING_MACRO_NAMES": "NSLocalizedString CFCopyLocalizedString", "LOCALIZED_STRING_SWIFTUI_SUPPORT": "YES", "LOCAL_ADMIN_APPS_DIR": "/Applications/Utilities", "LOCAL_APPS_DIR": "/Applications", "LOCAL_DEVELOPER_DIR": "/Library/Developer", "LOCAL_LIBRARY_DIR": "/Library", "LOCROOT": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "LOCSYMROOT": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "MAC_OS_X_PRODUCT_BUILD_VERSION": "24F74", "MAC_OS_X_VERSION_ACTUAL": "150500", "MAC_OS_X_VERSION_MAJOR": "150000", "MAC_OS_X_VERSION_MINOR": "150500", "MAKE_MERGEABLE": "NO", "MERGEABLE_LIBRARY": "NO", "MERGED_BINARY_TYPE": "none", "MERGE_LINKED_LIBRARIES": "NO", "METAL_LIBRARY_FILE_BASE": "default", "METAL_LIBRARY_OUTPUT_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c/", "MODULE_CACHE_DIR": "/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "NATIVE_ARCH": "arm64", "NATIVE_ARCH_32_BIT": "arm", "NATIVE_ARCH_64_BIT": "arm64", "NATIVE_ARCH_ACTUAL": "arm64", "NO_COMMON": "YES", "OBJECT_FILE_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Objects", "OBJECT_FILE_DIR_normal": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Objects-normal", "OBJROOT": "/Users/<USER>/Desktop/]ig/NIENIE/build", "ONLY_ACTIVE_ARCH": "YES", "OS": "MACOS", "OSAC": "/usr/bin/osacompile", "OTHER_CPLUSPLUSFLAGS": "-fvisibility=hidden -fvisibility-inlines-hidden", "PASCAL_STRINGS": "YES", "PATH": "/Applications/Xcode.app/Contents/SharedFrameworks/SwiftBuild.framework/Versions/A/PlugIns/SWBBuildService.bundle/Contents/PlugIns/SWBUniversalPlatformPlugin.bundle/Contents/Frameworks/SWBUniversalPlatform.framework/Resources:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin", "PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES": "/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms", "PER_ARCH_MODULE_FILE_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Objects-normal/undefined_arch", "PER_ARCH_OBJECT_FILE_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Objects-normal/undefined_arch", "PER_VARIANT_OBJECT_FILE_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Objects-normal", "PKGINFO_FILE_PATH": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/PkgInfo", "PLATFORM_DEVELOPER_APPLICATIONS_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Applications", "PLATFORM_DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin", "PLATFORM_DEVELOPER_LIBRARY_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library", "PLATFORM_DEVELOPER_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs", "PLATFORM_DEVELOPER_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Tools", "PLATFORM_DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr", "PLATFORM_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform", "PLATFORM_DISPLAY_NAME": "iOS", "PLATFORM_FAMILY_NAME": "iOS", "PLATFORM_NAME": "iphoneos", "PLATFORM_PREFERRED_ARCH": "arm64", "PLATFORM_PRODUCT_BUILD_VERSION": "22F76", "PLATFORM_REQUIRES_SWIFT_AUTOLINK_EXTRACT": "NO", "PLATFORM_REQUIRES_SWIFT_MODULEWRAP": "NO", "PLIST_FILE_OUTPUT_FORMAT": "binary", "PODS_BUILD_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build", "PODS_CONFIGURATION_BUILD_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos", "PODS_DEVELOPMENT_LANGUAGE": "en", "PODS_ROOT": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "PODS_TARGET_SRCROOT": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-c", "PODS_XCFRAMEWORKS_BUILD_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/XCFrameworkIntermediates", "PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR": "YES", "PRECOMP_DESTINATION_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/PrefixHeaders", "PROCESSED_INFOPLIST_PATH": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Objects-normal/undefined_arch/Processed-Info.plist", "PRODUCT_BUNDLE_IDENTIFIER": "org.cocoapods.onnxruntime-c", "PRODUCT_MODULE_NAME": "onnxruntime_c", "PRODUCT_NAME": "onnxruntime-c", "PRODUCT_SETTINGS_PATH": "", "PROFILING_CODE": "NO", "PROJECT": "Pods", "PROJECT_DERIVED_FILE_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/DerivedSources", "PROJECT_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "PROJECT_FILE_PATH": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Pods.xcodeproj", "PROJECT_GUID": "bfdfe7dc352907fc980b868725387e98", "PROJECT_NAME": "Pods", "PROJECT_TEMP_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build", "PROJECT_TEMP_ROOT": "/Users/<USER>/Desktop/]ig/NIENIE/build", "RECOMMENDED_IPHONEOS_DEPLOYMENT_TARGET": "15.0", "RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS": "YES", "REMOVE_CVS_FROM_RESOURCES": "YES", "REMOVE_GIT_FROM_RESOURCES": "YES", "REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES": "YES", "REMOVE_HG_FROM_RESOURCES": "YES", "REMOVE_STATIC_EXECUTABLES_FROM_EMBEDDED_BUNDLES": "YES", "REMOVE_SVN_FROM_RESOURCES": "YES", "RESCHEDULE_INDEPENDENT_HEADERS_PHASES": "YES", "REZ_COLLECTOR_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/ResourceManagerResources", "REZ_OBJECTS_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/ResourceManagerResources/Objects", "SCAN_ALL_SOURCE_FILES_FOR_INCLUDES": "NO", "SCRIPT_INPUT_FILE_COUNT": "0", "SCRIPT_INPUT_FILE_LIST_0": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/InputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-input-files-895d2e6a9bdc7486ec0417646b06ea49-resolved.xcfilelist", "SCRIPT_INPUT_FILE_LIST_COUNT": "1", "SCRIPT_OUTPUT_FILE_COUNT": "0", "SCRIPT_OUTPUT_FILE_LIST_0": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/OutputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-output-files-656e02bc143860ae06dd67f9f8235416-resolved.xcfilelist", "SCRIPT_OUTPUT_FILE_LIST_COUNT": "1", "SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "SDK_DIR_iphoneos": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "SDK_DIR_iphoneos18_5": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "SDK_NAME": "iphoneos18.5", "SDK_NAMES": "iphoneos18.5", "SDK_PRODUCT_BUILD_VERSION": "22F76", "SDK_STAT_CACHE_DIR": "/Users/<USER>/Library/Developer/Xcode/DerivedData", "SDK_STAT_CACHE_ENABLE": "YES", "SDK_STAT_CACHE_PATH": "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache", "SDK_VERSION": "18.5", "SDK_VERSION_ACTUAL": "180500", "SDK_VERSION_MAJOR": "180000", "SDK_VERSION_MINOR": "180500", "SED": "/usr/bin/sed", "SEPARATE_STRIP": "NO", "SEPARATE_SYMBOL_EDIT": "NO", "SET_DIR_MODE_OWNER_GROUP": "YES", "SET_FILE_MODE_OWNER_GROUP": "NO", "SHALLOW_BUNDLE": "NO", "SHARED_DERIVED_FILE_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c/DerivedSources", "SHARED_PRECOMPS_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/SharedPrecompiledHeaders", "SKIP_INSTALL": "YES", "SOURCE_ROOT": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "SRCROOT": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "STRINGSDATA_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Objects-normal/undefined_arch", "STRINGSDATA_ROOT": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build", "STRINGS_FILE_INFOPLIST_RENAME": "YES", "STRINGS_FILE_OUTPUT_ENCODING": "binary", "STRIP_BITCODE_FROM_COPIED_FILES": "YES", "STRIP_INSTALLED_PRODUCT": "NO", "STRIP_STYLE": "all", "STRIP_SWIFT_SYMBOLS": "YES", "SUPPORTED_DEVICE_FAMILIES": "1,2", "SUPPORTED_PLATFORMS": "iphoneos iphonesimulator", "SUPPORTS_MACCATALYST": "YES", "SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD": "YES", "SUPPORTS_TEXT_BASED_API": "NO", "SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD": "YES", "SUPPRESS_WARNINGS": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_PLATFORM_TARGET_PREFIX": "ios", "SWIFT_RESPONSE_FILE_PATH_normal_arm64": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Objects-normal/arm64/onnxruntime-c.SwiftFileList", "SWIFT_VERSION": "5.0", "SYMROOT": "/Users/<USER>/Desktop/]ig/NIENIE/build", "SYSTEM_ADMIN_APPS_DIR": "/Applications/Utilities", "SYSTEM_APPS_DIR": "/Applications", "SYSTEM_CORE_SERVICES_DIR": "/System/Library/CoreServices", "SYSTEM_DEMOS_DIR": "/Applications/Extras", "SYSTEM_DEVELOPER_APPS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "SYSTEM_DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "SYSTEM_DEVELOPER_DEMOS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples", "SYSTEM_DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "SYSTEM_DEVELOPER_DOC_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library", "SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools", "SYSTEM_DEVELOPER_JAVA_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Java Tools", "SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools", "SYSTEM_DEVELOPER_RELEASENOTES_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes", "SYSTEM_DEVELOPER_TOOLS": "/Applications/Xcode.app/Contents/Developer/Tools", "SYSTEM_DEVELOPER_TOOLS_DOC_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools", "SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools", "SYSTEM_DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "SYSTEM_DEVELOPER_UTILITIES_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Utilities", "SYSTEM_DEXT_INSTALL_PATH": "/System/Library/DriverExtensions", "SYSTEM_DOCUMENTATION_DIR": "/Library/Documentation", "SYSTEM_KEXT_INSTALL_PATH": "/System/Library/Extensions", "SYSTEM_LIBRARY_DIR": "/System/Library", "TAPI_DEMANGLE": "YES", "TAPI_ENABLE_PROJECT_HEADERS": "NO", "TAPI_LANGUAGE": "objective-c", "TAPI_LANGUAGE_STANDARD": "compiler-default", "TAPI_USE_SRCROOT": "YES", "TAPI_VERIFY_MODE": "Pedantic", "TARGETED_DEVICE_FAMILY": "1,2", "TARGETNAME": "onnxruntime-c", "TARGET_BUILD_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-c", "TARGET_NAME": "onnxruntime-c", "TARGET_TEMP_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build", "TEMP_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build", "TEMP_FILES_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build", "TEMP_FILE_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build", "TEMP_ROOT": "/Users/<USER>/Desktop/]ig/NIENIE/build", "TEMP_SANDBOX_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/TemporaryTaskSandboxes", "TEST_FRAMEWORK_SEARCH_PATHS": " /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/Developer/Library/Frameworks", "TEST_LIBRARY_SEARCH_PATHS": " /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib", "TOOLCHAINS": "com.apple.dt.toolchain.XcodeDefault", "TOOLCHAIN_DIR": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "TREAT_MISSING_BASELINES_AS_TEST_FAILURES": "NO", "TREAT_MISSING_SCRIPT_PHASE_OUTPUTS_AS_ERRORS": "NO", "UID": "501", "UNINSTALLED_PRODUCTS_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/build/UninstalledProducts", "UNSTRIPPED_PRODUCT": "NO", "USER": "<PERSON><PERSON><PERSON><PERSON>", "USER_APPS_DIR": "/Users/<USER>/Applications", "USER_LIBRARY_DIR": "/Users/<USER>/Library", "USE_DYNAMIC_NO_PIC": "YES", "USE_HEADERMAP": "YES", "USE_HEADER_SYMLINKS": "NO", "USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES": "YES", "VALIDATE_DEVELOPMENT_ASSET_PATHS": "YES_ERROR", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64 arm64e armv7 armv7s", "VERBOSE_PBXCP": "NO", "VERSION_INFO_BUILDER": "<PERSON><PERSON><PERSON><PERSON>", "VERSION_INFO_FILE": "onnxruntime-c_vers.c", "VERSION_INFO_STRING": "\"@(#)PROGRAM:onnxruntime-c  PROJECT:Pods-\"", "WORKSPACE_DIR": "/Users/<USER>/Desktop/]ig/NIENIE/Pods/Pods.xcodeproj", "WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES": "NO", "XCODE_APP_SUPPORT_DIR": "/Applications/Xcode.app/Contents/Developer/Library/Xcode", "XCODE_PRODUCT_BUILD_VERSION": "16F6", "XCODE_VERSION_ACTUAL": "1640", "XCODE_VERSION_MAJOR": "1600", "XCODE_VERSION_MINOR": "1640", "XPCSERVICES_FOLDER_PATH": "/XPCServices", "YACC": "yacc", "_DISCOVER_COMMAND_LINE_LINKER_INPUTS": "YES", "_DISCOVER_COMMAND_LINE_LINKER_INPUTS_INCLUDE_WL": "YES", "__DIAGNOSE_DEPRECATED_ARCHS": "YES", "arch": "undefined_arch", "variant": "normal"}, "allow-missing-inputs": true, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "control-enabled": false, "repair-via-ownership-analysis": true, "signature": "b233367e8b3ca52229bd47e59c7690b8"}, "P2:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/InputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-input-files-895d2e6a9bdc7486ec0417646b06ea49-resolved.xcfilelist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/InputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-input-files-895d2e6a9bdc7486ec0417646b06ea49-resolved.xcfilelist", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/InputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-input-files-895d2e6a9bdc7486ec0417646b06ea49-resolved.xcfilelist"]}, "P2:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/OutputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-output-files-656e02bc143860ae06dd67f9f8235416-resolved.xcfilelist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/OutputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-output-files-656e02bc143860ae06dd67f9f8235416-resolved.xcfilelist", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/OutputFileList-3CFBA14174592D8554CB04535DA6DCD3-onnxruntime-c-xcframeworks-output-files-656e02bc143860ae06dd67f9f8235416-resolved.xcfilelist"]}, "P2:target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Script-3CFBA14174592D8554CB04535DA6DCD3.sh": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Script-3CFBA14174592D8554CB04535DA6DCD3.sh", "inputs": ["<target-onnxruntime-c-bfdfe7dc352907fc980b868725387e983f9a8454c02452056e2eb0c5f3a362ec--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-c.build/Script-3CFBA14174592D8554CB04535DA6DCD3.sh"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc.modulemap": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc.modulemap", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc.modulemap/", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/module.modulemap"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/module.modulemap": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/module.modulemap", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/module.modulemap/", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Modules/module.modulemap"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime-objc-umbrella.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-umbrella.h": {"tool": "file-copy", "description": "CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime-objc-umbrella.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-umbrella.h", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/Target Support Files/onnxruntime-objc/onnxruntime-objc-umbrella.h/", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime-objc-umbrella.h"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/onnxruntime.h": {"tool": "file-copy", "description": "CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/onnxruntime.h", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/onnxruntime.h/", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/onnxruntime.h"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_coreml_execution_provider.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_coreml_execution_provider.h": {"tool": "file-copy", "description": "CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_coreml_execution_provider.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_coreml_execution_provider.h", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_coreml_execution_provider.h/", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_coreml_execution_provider.h"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_custom_op_registration.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_custom_op_registration.h": {"tool": "file-copy", "description": "CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_custom_op_registration.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_custom_op_registration.h", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_custom_op_registration.h/", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_custom_op_registration.h"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_enums.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_enums.h": {"tool": "file-copy", "description": "CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_enums.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_enums.h", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_enums.h/", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_enums.h"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_env.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_env.h": {"tool": "file-copy", "description": "CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_env.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_env.h", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_env.h/", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_env.h"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_session.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_session.h": {"tool": "file-copy", "description": "CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_session.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_session.h", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_session.h/", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_session.h"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_value.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_value.h": {"tool": "file-copy", "description": "CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_value.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_value.h", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_value.h/", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_value.h"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_xnnpack_execution_provider.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_xnnpack_execution_provider.h": {"tool": "file-copy", "description": "CpHeader /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_xnnpack_execution_provider.h /Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_xnnpack_execution_provider.h", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/Pods/onnxruntime-objc/objectivec/include/ort_xnnpack_execution_provider.h/", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/Headers/ort_xnnpack_execution_provider.h"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:Libtool /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/onnxruntime_objc normal": {"tool": "shell", "description": "Libtool /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/onnxruntime_objc normal", "inputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_vers.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/assert_arc_enabled.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/cxx_utils.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/error_utils.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime-objc-dummy.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_coreml_execution_provider.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_enums.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_env.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_session.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_value.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/ort_xnnpack_execution_provider.o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc.LinkFileList", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--fused-phase0-copy-headers>", "<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/onnxruntime_objc", "<Linked Binary /Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/onnxruntime_objc>", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_libtool_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool", "-static", "-arch_only", "arm64", "-D", "-s<PERSON><PERSON><PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "-L/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc", "-filelist", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc.LinkFileList", "-framework", "Foundation", "-dependency_info", "/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_libtool_dependency_info.dat", "-o", "/Users/<USER>/Desktop/]ig/NIENIE/build/Release-iphoneos/onnxruntime-objc/onnxruntime_objc.framework/onnxruntime_objc"], "env": {"IPHONEOS_DEPLOYMENT_TARGET": "12.0"}, "working-directory": "/Users/<USER>/Desktop/]ig/NIENIE/Pods", "deps": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc_libtool_dependency_info.dat"], "deps-style": "dependency-info", "signature": "4ac7fae60c9f09a6820eccb6835afbe5"}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/DerivedSources/onnxruntime_objc_vers.c": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/DerivedSources/onnxruntime_objc_vers.c", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/DerivedSources/onnxruntime_objc_vers.c"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc.LinkFileList", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/Objects-normal/arm64/onnxruntime_objc.LinkFileList"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-all-non-framework-target-headers.hmap", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-all-non-framework-target-headers.hmap"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-all-target-headers.hmap", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-all-target-headers.hmap"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-generated-files.hmap", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-generated-files.hmap"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-own-target-headers.hmap", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-own-target-headers.hmap"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-project-headers.hmap", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc-project-headers.hmap"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyMetadataFileList", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyMetadataFileList"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyStaticMetadataFileList", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.DependencyStaticMetadataFileList"]}, "P2:target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.hmap", "inputs": ["<target-onnxruntime-objc-bfdfe7dc352907fc980b868725387e98c727cc57af0fae221090eb0758cb81ff--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/NIENIE/build/Pods.build/Release-iphoneos/onnxruntime-objc.build/onnxruntime_objc.hmap"]}}}