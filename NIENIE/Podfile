
# Uncomment the next line to define a global platform for your project
platform :ios, '17.6'

target 'NIENIE' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!
  pod 'onnxruntime-objc', '~> 1.16.0'
  # Pods for NIENIE

  target 'NIENIETests' do
    inherit! :search_paths
    # Pods for testing
  end

  target 'NIENIEUITests' do
    # Pods for testing
  end

end
