import torch
import coremltools as ct
import numpy as np
from basicsr.archs.rrdbnet_arch import RRDBNet
import os

def convert_realesrgan_to_coreml():
    """
    将RealESRGAN_x4plus模型转换为CoreML格式
    """
    
    # 1. 创建模型架构
    model = RRDBNet(
        num_in_ch=3,      # 输入通道数（RGB）
        num_out_ch=3,     # 输出通道数（RGB）
        num_feat=64,      # 特征数
        num_block=23,     # RRDB块数量
        num_grow_ch=32,   # 增长通道数
        scale=2           # 放大倍数
    )
    
    # 2. 加载预训练权重
    model_path = "weights/RealESRGAN_x2plus.pth"  # 确保路径正确
    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        print("请确保RealESRGAN_x2plus.pth文件在weights目录下")
        return
    
    # 加载权重
    checkpoint = torch.load(model_path, map_location='cpu')
    
    # 处理权重键名（如果需要）
    if 'params_ema' in checkpoint:
        state_dict = checkpoint['params_ema']
    elif 'params' in checkpoint:
        state_dict = checkpoint['params']
    else:
        state_dict = checkpoint
    
    model.load_state_dict(state_dict)
    model.eval()
    
    # 3. 创建示例输入（重要：这决定了模型的输入尺寸）
    # 使用较小的输入尺寸以适应移动设备内存限制
    example_input = torch.randn(1, 3, 256, 256)  # 批次大小=1, RGB=3, 高=256, 宽=256
    
    print("开始转换模型...")
    
    # 4. 转换为CoreML
    try:
        # 使用torch.jit.trace追踪模型
        traced_model = torch.jit.trace(model, example_input)
        
        # 转换为CoreML
        coreml_model = ct.convert(
            traced_model,
            inputs=[ct.TensorType(
                name="input_image",
                shape=example_input.shape,
                dtype=np.float32
            )],
            outputs=[ct.TensorType(
                name="output_image",
                dtype=np.float32
            )],
            minimum_deployment_target=ct.target.iOS15,  # 支持iOS 15+
            compute_units=ct.ComputeUnit.ALL  # 使用所有可用计算单元（CPU+GPU+Neural Engine）
        )
        
        # 5. 设置模型元数据
        coreml_model.short_description = "RealESRGAN x2 Super Resolution Model"
        coreml_model.author = "Real-ESRGAN Team"
        coreml_model.license = "Apache License 2.0"
        coreml_model.version = "1.0"
        
        # 设置输入输出描述
        coreml_model.input_description["input_image"] = "Input RGB image (normalized to 0-1)"
        coreml_model.output_description["output_image"] = "Super-resolved RGB image (2x resolution)"
        
        # 6. 保存模型
        output_path = "DoubleOrigin.mlpackage"
        coreml_model.save(output_path)
        
        print(f"✅ 模型转换成功！")
        print(f"📁 输出文件: {output_path}")
        print(f"📊 输入尺寸: {example_input.shape}")
        print(f"💾 文件大小: {os.path.getsize(output_path) / (1024*1024):.1f} MB")
        
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔄 开始RealESRGAN模型转换...")
    
    # 检查PyTorch版本
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CoreMLTools版本: {ct.__version__}")
    
    # 转换标准模型
    success = convert_realesrgan_to_coreml()