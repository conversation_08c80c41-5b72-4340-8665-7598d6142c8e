name: PyLint

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8]

    steps:
    - uses: actions/checkout@v2
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install codespell flake8 isort yapf

    # modify the folders accordingly
    - name: Lint
      run: |
        codespell
        flake8 .
        isort --check-only --diff realesrgan/ scripts/ inference_realesrgan.py setup.py
        yapf -r -d realesrgan/ scripts/ inference_realesrgan.py setup.py
