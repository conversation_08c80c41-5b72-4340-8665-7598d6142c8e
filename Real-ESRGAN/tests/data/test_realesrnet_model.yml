scale: 4
num_gpu: 1
manual_seed: 0
is_train: True
dist: False

# ----------------- options for synthesizing training data ----------------- #
gt_usm: True  # USM the ground-truth

# the first degradation process
resize_prob: [0.2, 0.7, 0.1]  # up, down, keep
resize_range: [0.15, 1.5]
gaussian_noise_prob: 1
noise_range: [1, 30]
poisson_scale_range: [0.05, 3]
gray_noise_prob: 1
jpeg_range: [30, 95]

# the second degradation process
second_blur_prob: 1
resize_prob2: [0.3, 0.4, 0.3]  # up, down, keep
resize_range2: [0.3, 1.2]
gaussian_noise_prob2: 1
noise_range2: [1, 25]
poisson_scale_range2: [0.05, 2.5]
gray_noise_prob2: 1
jpeg_range2: [30, 95]

gt_size: 32
queue_size: 1

# network structures
network_g:
  type: RRDBNet
  num_in_ch: 3
  num_out_ch: 3
  num_feat: 4
  num_block: 1
  num_grow_ch: 2

# path
path:
  pretrain_network_g: ~
  param_key_g: params_ema
  strict_load_g: true
  resume_state: ~

# training settings
train:
  ema_decay: 0.999
  optim_g:
    type: Adam
    lr: !!float 2e-4
    weight_decay: 0
    betas: [0.9, 0.99]

  scheduler:
    type: MultiStepLR
    milestones: [1000000]
    gamma: 0.5

  total_iter: 1000000
  warmup_iter: -1  # no warm up

  # losses
  pixel_opt:
    type: L1Loss
    loss_weight: 1.0
    reduction: mean


# validation settings
val:
  val_freq: !!float 5e3
  save_img: False
