-- 创建用户会话免打扰表
CREATE TABLE IF NOT EXISTS user_conversation_mute (
    user_id INT NOT NULL,
    conversation_id VARCHAR(36) NOT NULL,
    is_muted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否开启免打扰，默认关闭',
    PRIMARY KEY (user_id, conversation_id),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES user_account(user_id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_id) REFERENCES conversation(conversation_id) ON DELETE CASCADE
);

-- 添加索引以提高查询性能
CREATE INDEX idx_user_conversation_mute_user_id ON user_conversation_mute(user_id);
CREATE INDEX idx_user_conversation_mute_conversation_id ON user_conversation_mute(conversation_id);
CREATE INDEX idx_user_conversation_mute_is_muted ON user_conversation_mute(is_muted);
