# 文件安全拷贝工具

这是一个macOS应用程序，用于在多个目标位置之间安全复制文件，并对复制的文件进行完整性校验。

## 功能

- 从一个源目录复制文件到最多三个目标目录
- 在复制完成后对文件进行完整性校验
- 显示复制和校验进度
- 列出校验失败的文件
- 支持取消操作

## 使用方法

1. 点击"选择"按钮选择源目录
2. 点击"选择"按钮选择1-3个目标目录
3. 点击"开始拷贝"按钮开始复制操作
4. 应用程序将首先复制文件到第一个目标目录，然后进行校验
5. 校验完成后，应用程序会复制文件到下一个目标目录（如果有的话）
6. 在整个过程中，您可以看到当前状态、进度和校验失败的文件列表
7. 如果需要取消操作，可以点击"取消"按钮

## 系统要求

- macOS 14.0或更高版本
- Xcode 15.0或更高版本（如果需要编译源代码）

## 编译和运行

1. 打开`FileCopyUtility.xcodeproj`文件
2. 选择目标设备为Mac
3. 点击运行按钮或使用快捷键⌘+R 


速度方面：
基础复制速度相近：
两者都使用底层系统API进行复制（FileManager.copyItem）
理论上基础文件传输速度应该接近
实际速度差异：
苹果Finder可能会更快，因为它有专门针对外部设备的优化
Finder对大量小文件的处理通常更高效，有更好的并行策略
额外操作影响：
我们的软件会执行额外的校验步骤，这会使整体操作慢于Finder
对于大文件，校验过程可能比复制本身花费更多时间

功能区别：
数据安全性：
苹果Finder：不进行数据完整性校验，潜在风险是U盘数据可能有损坏
我们的软件：复制后检查每个文件的哈希值，确保数据100%完整
适用场景：
Finder：日常快速复制
我们的软件：对数据完整性有高要求的场景（如备份重要文档、媒体文件）