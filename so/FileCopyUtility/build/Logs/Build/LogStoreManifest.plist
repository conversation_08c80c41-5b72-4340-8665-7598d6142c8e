<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>45EF2972-969C-432D-B839-B1CB75218C0D</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>45EF2972-969C-432D-B839-B1CB75218C0D.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FileCopyUtility project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FileCopyUtility</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>777202632.55863798</real>
			<key>timeStoppedRecording</key>
			<real>777202632.74071205</real>
			<key>title</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>45EF2972-969C-432D-B839-B1CB75218C0D</string>
		</dict>
		<key>5CCBA961-1211-4C47-863E-8A0DCFBB26A6</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>5CCBA961-1211-4C47-863E-8A0DCFBB26A6.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FileCopyUtility project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FileCopyUtility</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>777202571.53117204</real>
			<key>timeStoppedRecording</key>
			<real>777202571.75933897</real>
			<key>title</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>5CCBA961-1211-4C47-863E-8A0DCFBB26A6</string>
		</dict>
	</dict>
</dict>
</plist>
