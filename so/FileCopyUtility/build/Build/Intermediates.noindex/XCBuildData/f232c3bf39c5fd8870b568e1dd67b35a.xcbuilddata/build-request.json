{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "254c41a42a1e5cf406a5f0733b04dc2f759f6aad0b0c06aad02934f01d0ae6b8"}], "containerPath": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "macosx", "sdk": "macosx15.5", "sdkVariant": "macos", "supportedArchitectures": ["arm64e", "arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products", "derivedDataPath": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build", "indexDataStoreFolderPath": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Release", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "130", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}