"/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/ContentView.swift":
  swiftsourceinfo: "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftsourceinfo"
  abi-baseline-json: "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.abi.json"
  objc-header: "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-Swift.h"
  swiftmodule: "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule"
  const-values: "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-master.swiftconstvalues"
  diagnostics: "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-master.dia"
  swiftdoc: "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftdoc"
  dependencies: "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-master.d"
