// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		7A001FE12B2A4FD000123456 /* FileCopyUtilityApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7A001FE02B2A4FD000123456 /* FileCopyUtilityApp.swift */; };
		7A001FE32B2A4FD000123456 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7A001FE22B2A4FD000123456 /* ContentView.swift */; };
		7A001FE52B2A4FD100123456 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 7A001FE42B2A4FD100123456 /* Assets.xcassets */; };
		7A001FE82B2A4FD100123456 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 7A001FE72B2A4FD100123456 /* Preview Assets.xcassets */; };
		7A001FF02B2A501700123456 /* FileCopyViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7A001FEF2B2A501700123456 /* FileCopyViewModel.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		7A001FDD2B2A4FD000123456 /* FileCopyUtility.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FileCopyUtility.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7A001FE02B2A4FD000123456 /* FileCopyUtilityApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileCopyUtilityApp.swift; sourceTree = "<group>"; };
		7A001FE22B2A4FD000123456 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		7A001FE42B2A4FD100123456 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		7A001FE72B2A4FD100123456 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		7A001FE92B2A4FD100123456 /* FileCopyUtility.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = FileCopyUtility.entitlements; sourceTree = "<group>"; };
		7A001FEF2B2A501700123456 /* FileCopyViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileCopyViewModel.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7A001FDA2B2A4FD000123456 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7A001FD42B2A4FD000123456 = {
			isa = PBXGroup;
			children = (
				7A001FDF2B2A4FD000123456 /* FileCopyUtility */,
				7A001FDE2B2A4FD000123456 /* Products */,
			);
			sourceTree = "<group>";
		};
		7A001FDE2B2A4FD000123456 /* Products */ = {
			isa = PBXGroup;
			children = (
				7A001FDD2B2A4FD000123456 /* FileCopyUtility.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7A001FDF2B2A4FD000123456 /* FileCopyUtility */ = {
			isa = PBXGroup;
			children = (
				7A001FE02B2A4FD000123456 /* FileCopyUtilityApp.swift */,
				7A001FE22B2A4FD000123456 /* ContentView.swift */,
				7A001FEF2B2A501700123456 /* FileCopyViewModel.swift */,
				7A001FE42B2A4FD100123456 /* Assets.xcassets */,
				7A001FE92B2A4FD100123456 /* FileCopyUtility.entitlements */,
				7A001FE62B2A4FD100123456 /* Preview Content */,
			);
			path = FileCopyUtility;
			sourceTree = "<group>";
		};
		7A001FE62B2A4FD100123456 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				7A001FE72B2A4FD100123456 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7A001FDC2B2A4FD000123456 /* FileCopyUtility */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7A001FEC2B2A4FD100123456 /* Build configuration list for PBXNativeTarget "FileCopyUtility" */;
			buildPhases = (
				7A001FD92B2A4FD000123456 /* Sources */,
				7A001FDA2B2A4FD000123456 /* Frameworks */,
				7A001FDB2B2A4FD000123456 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = FileCopyUtility;
			productName = FileCopyUtility;
			productReference = 7A001FDD2B2A4FD000123456 /* FileCopyUtility.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7A001FD52B2A4FD000123456 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1510;
				LastUpgradeCheck = 1510;
				TargetAttributes = {
					7A001FDC2B2A4FD000123456 = {
						CreatedOnToolsVersion = 15.1;
					};
				};
			};
			buildConfigurationList = 7A001FD82B2A4FD000123456 /* Build configuration list for PBXProject "FileCopyUtility" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 7A001FD42B2A4FD000123456;
			productRefGroup = 7A001FDE2B2A4FD000123456 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7A001FDC2B2A4FD000123456 /* FileCopyUtility */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7A001FDB2B2A4FD000123456 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7A001FE82B2A4FD100123456 /* Preview Assets.xcassets in Resources */,
				7A001FE52B2A4FD100123456 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7A001FD92B2A4FD000123456 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7A001FE32B2A4FD000123456 /* ContentView.swift in Sources */,
				7A001FF02B2A501700123456 /* FileCopyViewModel.swift in Sources */,
				7A001FE12B2A4FD000123456 /* FileCopyUtilityApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		7A001FEA2B2A4FD100123456 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7A001FEB2B2A4FD100123456 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		7A001FED2B2A4FD100123456 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = FileCopyUtility/FileCopyUtility.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"FileCopyUtility/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.FileCopyUtility;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		7A001FEE2B2A4FD100123456 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = FileCopyUtility/FileCopyUtility.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"FileCopyUtility/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.FileCopyUtility;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7A001FD82B2A4FD000123456 /* Build configuration list for PBXProject "FileCopyUtility" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7A001FEA2B2A4FD100123456 /* Debug */,
				7A001FEB2B2A4FD100123456 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7A001FEC2B2A4FD100123456 /* Build configuration list for PBXNativeTarget "FileCopyUtility" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7A001FED2B2A4FD100123456 /* Debug */,
				7A001FEE2B2A4FD100123456 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7A001FD52B2A4FD000123456 /* Project object */;
} 