import SwiftUI

struct ContentView: View {
    @StateObject private var viewModel = FileCopyViewModel()
    
    var body: some View {
        VStack(spacing: 15) {
            // 标题
            Text("数联天工文件备份工具")
                .font(.largeTitle)
                .padding(.top, 10)
            
            // 源文件选择
            HStack {
                Text("源目录:")
                    .frame(width: 100, alignment: .leading)

                TextField("", text: $viewModel.sourcePath)
                    .disabled(true)
                    .frame(maxWidth: .infinity) // 让路径区域占用剩余空间

                Button("选择") {
                    viewModel.selectSourceFolder()
                }
                .disabled(viewModel.isProcessing)
                .frame(width: 60) // 固定按钮宽度
            }
            .padding(.horizontal)
            
            // 目标文件夹选择
            VStack(spacing: 8) {
                ForEach(0..<3) { index in
                    HStack {
                        Text("目标目录\(index + 1):")
                            .frame(width: 100, alignment: .leading)

                        // 目标路径显示区域 - 与源目录保持一致的布局
                        HStack(spacing: 8) {
                            TextField("", text: Binding(
                                get: { viewModel.destinationPaths.count > index ? viewModel.destinationPaths[index] : "" },
                                set: { newValue in
                                    while viewModel.destinationPaths.count <= index {
                                        viewModel.destinationPaths.append("")
                                    }
                                    viewModel.destinationPaths[index] = newValue
                                }
                            ))
                            .disabled(true)

                            // 文件名输入框
                            TextField("文件名", text: Binding(
                                get: {
                                    viewModel.destinationFileNames.count > index ? viewModel.destinationFileNames[index] : viewModel.getSourceFileName()
                                },
                                set: { newValue in
                                    viewModel.setDestinationFileName(at: index, fileName: newValue)
                                }
                            ))
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .frame(width: 150) // 固定文件名输入框宽度
                            .disabled(viewModel.isProcessing)
                        }
                        .frame(maxWidth: .infinity) // 让整个中间区域占用剩余空间

                        Button("选择") {
                            viewModel.selectDestinationFolder(at: index)
                        }
                        .disabled(viewModel.isProcessing)
                        .frame(width: 60) // 与源目录按钮宽度一致
                    }
                }
            }
            .padding(.horizontal)
            
            // 状态信息
            HStack {
                Text("当前状态: \(viewModel.statusMessage)")
                Spacer()
            }
            .padding(.horizontal)
            .padding(.vertical, 5)
            
            // 进度条
            VStack(spacing: 5) {
                ProgressView(value: viewModel.progress)
                    .padding(.horizontal)

                HStack {
                    Text("\(Int(viewModel.progress * 100))% 完成")
                    Spacer()
                    Text("用时: \(viewModel.formattedElapsedTime)")
                        .font(.system(size: 14, design: .monospaced))
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal)
            }
            
            // 失败文件列表框
            VStack(alignment: .leading, spacing: 5) {
                HStack {
                    Text("校验失败的文件:")
                        .fontWeight(.bold)
                    
                    Spacer()
                }
                .padding(.bottom, 5)
                
                if viewModel.failedFiles.isEmpty {
                    Text("暂无失败文件")
                        .foregroundColor(.gray)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding(.vertical, 8)
                } else {
                    ScrollView {
                        VStack(alignment: .leading, spacing: 5) {
                            ForEach(viewModel.failedFiles, id: \.self) { file in
                                Text("• \(file)")
                                    .foregroundColor(.red)
                            }
                        }
                    }
                }
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 8)
            .frame(height: 120)
            .frame(maxWidth: .infinity)
            .background(Color(NSColor.textBackgroundColor))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.gray, lineWidth: 1)
            )
            .padding(.horizontal)
            
            // 操作按钮 - 更紧凑的样式
            HStack(spacing: 20) {
                Button(action: {
                    viewModel.startCopy()
                }) {
                    Text("开始拷贝")
                        .padding(.vertical, 6)
                        .padding(.horizontal, 12)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(6)
                }
                .disabled(viewModel.isProcessing || viewModel.sourcePath.isEmpty || viewModel.activeDestinationPaths.isEmpty)
                
                Button(action: {
                    viewModel.cancelOperation()
                }) {
                    Text("取消")
                        .padding(.vertical, 6)
                        .padding(.horizontal, 12)
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(6)
                }
                .disabled(!viewModel.isProcessing)
                
                Button(action: {
                    viewModel.clearAll()
                }) {
                    Text("清除全部")
                        .padding(.vertical, 6)
                        .padding(.horizontal, 12)
                        .background(Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(6)
                }
                .disabled(viewModel.isProcessing)
            }
            .padding(.top, 5)
            .padding(.bottom, 10)
        }
        .padding(.vertical, 5)
        .frame(minWidth: 600, idealHeight: 560, maxHeight: 560)
    }
}

#Preview {
    ContentView()
} 
