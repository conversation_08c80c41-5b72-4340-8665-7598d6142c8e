#!/bin/bash

# 设置变量
APP_NAME="FileCopyUtility"
DISPLAY_NAME="数联天工文件备份工具"
ICON_PATH="${WORKSPACE_DIR}/AppIcon.icns"
WORKSPACE_DIR="$(dirname "$(cd "$(dirname "$0")" && pwd)")"
BUILD_DIR="${WORKSPACE_DIR}/build"
DMG_TEMP_DIR="${WORKSPACE_DIR}/dmg_temp"
DMG_DIR="${WORKSPACE_DIR}/dmg_build"
COMPANY_LOGO="${WORKSPACE_DIR}/logo.png"  # 公司logo

# 打印路径信息便于调试
echo "项目目录: ${WORKSPACE_DIR}"
echo "构建目录: ${BUILD_DIR}"
echo "DMG输出目录: ${DMG_DIR}"
echo "图标路径: ${ICON_PATH}"

# 创建必要的目录并清理之前的数据
echo "===== 清理和创建目录 ====="
rm -rf "${DMG_TEMP_DIR}" "${DMG_DIR}"
mkdir -p "${BUILD_DIR}" "${DMG_TEMP_DIR}" "${DMG_DIR}"

# 构建应用程序
echo "===== 构建应用程序 ====="
xcodebuild -project "${WORKSPACE_DIR}/${APP_NAME}.xcodeproj" -scheme "${APP_NAME}" -configuration Release -derivedDataPath "${BUILD_DIR}" clean build

# 如果构建失败则退出
if [ $? -ne 0 ]; then
    echo "应用构建失败"
    exit 1
fi

# 复制应用到临时目录并重命名
echo "===== 准备打包 ====="
APP_PATH="${BUILD_DIR}/Build/Products/Release/${APP_NAME}.app"
TEMP_APP_PATH="${DMG_TEMP_DIR}/${DISPLAY_NAME}.app"

# 重命名并复制应用
cp -R "${APP_PATH}" "${TEMP_APP_PATH}"

# 更新Info.plist
BUILT_INFO_PLIST="${TEMP_APP_PATH}/Contents/Info.plist"
echo "更新应用Info.plist: ${BUILT_INFO_PLIST}"
if [ -f "${BUILT_INFO_PLIST}" ]; then
    # 修改应用显示名称
    /usr/libexec/PlistBuddy -c "Set :CFBundleDisplayName ${DISPLAY_NAME}" "${BUILT_INFO_PLIST}" 2>/dev/null || \
    /usr/libexec/PlistBuddy -c "Add :CFBundleDisplayName string ${DISPLAY_NAME}" "${BUILT_INFO_PLIST}"
    
    /usr/libexec/PlistBuddy -c "Set :CFBundleName ${DISPLAY_NAME}" "${BUILT_INFO_PLIST}" 2>/dev/null || \
    /usr/libexec/PlistBuddy -c "Add :CFBundleName string ${DISPLAY_NAME}" "${BUILT_INFO_PLIST}"
    
    # 显示修改后的值以确认
    echo "应用名称设置为:"
    /usr/libexec/PlistBuddy -c "Print :CFBundleName" "${BUILT_INFO_PLIST}"
    /usr/libexec/PlistBuddy -c "Print :CFBundleDisplayName" "${BUILT_INFO_PLIST}"
else
    echo "错误: 找不到构建后的Info.plist: ${BUILT_INFO_PLIST}"
    exit 1
fi

# 替换应用图标
echo "===== 更新应用图标 ====="
if [ -f "${ICON_PATH}" ]; then
    # 确保目标目录存在
    ICONS_DIR="${TEMP_APP_PATH}/Contents/Resources"
    mkdir -p "${ICONS_DIR}"
    
    # 复制图标到应用包
    cp "${ICON_PATH}" "${ICONS_DIR}/AppIcon.icns"
    
    # 更新Info.plist中的图标引用
    /usr/libexec/PlistBuddy -c "Set :CFBundleIconFile AppIcon.icns" "${BUILT_INFO_PLIST}" 2>/dev/null || \
    /usr/libexec/PlistBuddy -c "Add :CFBundleIconFile string AppIcon.icns" "${BUILT_INFO_PLIST}"
    
    echo "应用图标已更新"
else
    echo "警告: 找不到图标文件: ${ICON_PATH}"
fi

# 清理资源分支和Finder信息
echo "===== 清理资源分支和Finder信息 ====="
xattr -cr "${TEMP_APP_PATH}"
find "${TEMP_APP_PATH}" -type f -name "._*" -delete
find "${TEMP_APP_PATH}" -type f -name ".DS_Store" -delete

# 对应用进行临时自签名
echo "===== 对应用进行临时自签名 ====="
# 移除现有签名
codesign --remove-signature "${TEMP_APP_PATH}" 2>/dev/null

# 使用临时自签名
codesign --force --deep --sign - --timestamp=none "${TEMP_APP_PATH}"

if [ $? -ne 0 ]; then
    echo "应用签名失败"
    exit 1
else
    echo "应用已成功签名"
fi

# 创建一个符号链接到 /Applications 
ln -s /Applications "${DMG_TEMP_DIR}/Applications"

# 复制公司logo到DMG临时目录（如果存在）
if [ -f "${COMPANY_LOGO}" ]; then
    cp "${COMPANY_LOGO}" "${DMG_TEMP_DIR}/数联天工"
    echo "公司logo已添加到DMG中"
fi

# 创建前清理资源分支
xattr -cr "${DMG_TEMP_DIR}"
find "${DMG_TEMP_DIR}" -type f -name "._*" -delete
find "${DMG_TEMP_DIR}" -type f -name ".DS_Store" -delete

# 创建DMG文件
echo "===== 创建DMG文件 ====="
hdiutil create -volname "${DISPLAY_NAME}" -srcfolder "${DMG_TEMP_DIR}" -ov -format UDZO "${DMG_DIR}/${DISPLAY_NAME}.dmg"

# 设置DMG图标
echo "===== 设置DMG图标 ====="
if [ -f "${ICON_PATH}" ]; then
    # 应用图标到DMG
    sips -i "${ICON_PATH}" > /dev/null 2>&1
    
    # 使用DeRez提取图标资源
    DeRez -only icns "${ICON_PATH}" > /tmp/tmp.rsrc
    
    # 使用Rez将图标资源应用到DMG
    Rez -append /tmp/tmp.rsrc -o "${DMG_DIR}/${DISPLAY_NAME}.dmg"
    
    # 清理
    rm -f /tmp/tmp.rsrc
    
    echo "DMG图标已设置"
else
    echo "警告: 找不到图标文件，无法设置DMG图标: ${ICON_PATH}"
fi

echo "===== DMG创建完成 ====="
echo "DMG文件位置: ${DMG_DIR}/${DISPLAY_NAME}.dmg"
echo ""
echo "注意: 这是一个临时自签名的应用。"
echo "当用户第一次尝试打开应用时，会看到一个警告。"
echo "用户需要在系统偏好设置 > 安全性与隐私 中手动允许应用运行。"
echo ""

# 打开DMG文件所在目录
open "${DMG_DIR}"