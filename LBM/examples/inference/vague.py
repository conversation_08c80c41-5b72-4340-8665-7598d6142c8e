import cv2
import numpy as np
import torch
from torchvision.transforms import ToTensor

# ----------------------------
# 深度估计模块（使用MiDaS轻量模型）
# ----------------------------
def init_depth_model():
    model = torch.hub.load(
        repo_or_dir='/data/lsc/IClight/LBM/examples/inference/vague_model',
        model='metric3d_vit_giant2', 
        source='local',
        pretrained=False  # 禁用自动下载检查点
    )
    # 加载本地权重（路径需匹配）
    ckpt = torch.load('/data/lsc/IClight/LBM/examples/inference/vague_model/weight/metric_depth_vit_giant2_800k.pth')
    model.load_state_dict(ckpt['model_state_dict'], strict=False)
    return model.to('cuda').eval()

def get_depth_map(model, image):
    input_tensor = ToTensor()(image).unsqueeze(0)
    input_tensor = input_tensor.to(next(model.parameters()).device)
    with torch.no_grad():
        pred_depth, confidence, output_dict = model.inference({'input': input_tensor})
    return pred_depth.squeeze().cpu().numpy()

# ----------------------------
# 核心处理函数
# ----------------------------
def adaptive_background_blur(image, mask, x, y, F_stop, sensor_width_mm=36):
    """
    参数：
    - image: BGR格式的输入图像(numpy数组)
    - mask: 人物掩膜(0为背景，255为前景)
    - x,y: 手动标记的焦点坐标
    - F_stop: 光圈值(如2.8)
    - sensor_width_mm: 传感器物理宽度(mm)
    """
    # 转换为RGB格式供深度模型使用
    rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    h, w = image.shape[:2]
    
    # 初始化深度模型并获取深度图
    depth_model = init_depth_model()
    depth_map = get_depth_map(depth_model, rgb_image)
    
    # 计算等效焦距（假设传感器宽度已知）
    Z = depth_map[y, x]  # 焦点处深度值
    sensor_width_pixels = w  # 假设图像宽度等于传感器宽度
    f_mm = (Z * sensor_width_mm) / sensor_width_pixels  # 物理焦距(mm)
    f_pixels = f_mm * (w / sensor_width_mm)  # 转换为像素单位
    
    # 计算模糊半径（简化景深公式）
    L = Z  # 对焦距离≈深度值
    blur_radius = int((f_pixels ** 2) / (F_stop * L))
    blur_radius = max(5, min(blur_radius, 100))  # 限制范围
    
    # 分离前景与背景
    foreground = cv2.bitwise_and(image, image, mask=mask)
    background = cv2.bitwise_and(image, image, mask=cv2.bitwise_not(mask))
    
    # 动态高斯模糊背景
    kernel_size = (blur_radius*2+1, blur_radius*2+1)
    blurred_bg = cv2.GaussianBlur(background, kernel_size, 0)
    
    # 合并结果并优化边缘
    result = cv2.add(foreground, blurred_bg)
    return result

# ----------------------------
# 使用示例
# ----------------------------
if __name__ == "__main__":
    # 输入参数
    image = cv2.imread("/data5/IC-Light/output/IC/bf584180-b1b3-4ddd-97fb-767ea5fd6468_output_2587_1728.webp")  # 合成后的图像
    mask = cv2.imread("/data5/IC-Light/output/IC/bf584180-b1b3-4ddd-97fb-767ea5fd6468_mask_2587_1728.png", cv2.IMREAD_GRAYSCALE)
    x, y = 2000,698  # 手动标记的焦点坐标
    F_stop = 14     # 光圈值
    
    # 执行模糊处理
    output = adaptive_background_blur(image, mask, x, y, F_stop)
    
    # 保存结果
    cv2.imwrite("/data/lsc/IClight/LBM/img/out/ut.jpg", output)
    