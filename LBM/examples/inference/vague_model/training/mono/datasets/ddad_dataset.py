import os
import json
import torch
import torchvision.transforms as transforms
import os.path
import numpy as np
import cv2
from torch.utils.data import Dataset
import random
from .__base_dataset__ import BaseDataset


class DDADDataset(BaseDataset):
    def __init__(self, cfg, phase, **kwargs):
        super(DDADDataset, self).__init__(
            cfg=cfg,
            phase=phase,
            **kwargs)
        self.metric_scale = cfg.metric_scale
        #self.cap_range = self.depth_range # in meter
    
    
    def process_depth(self, depth, rgb):
        depth[depth>65500] = 0
        depth /= 200.0
        # depth[(depth>self.cap_range[1]) | (depth<self.cap_range[0])] = -1
        # depth /= self.cap_range[1]
        return depth



if __name__ == '__main__':
    from mmcv.utils import Config 
    cfg = Config.fromfile('mono/configs/Apolloscape_DDAD/convnext_base.cascade.1m.sgd.mae.py')
    dataset_i = ApolloscapeDataset(cfg['Apolloscape'], 'train', **cfg.data_basic)
    print(dataset_i)
    