import random
import json
from typing import Dict, Any, List
from tencentcloud.common import credential
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.sms.v20210111 import sms_client, models

# 直接导入
from config import SMS_CONFIG

def generate_verification_code() -> str:
    """
    生成6位数字验证码
    """
    return str(random.randint(100000, 999999))

def send_verification_code(phone: str, code: str) -> Dict[str, Any]:
    """
    发送验证码到指定手机号
    使用腾讯云短信SDK发送
    """
    try:
        # 实例化一个认证对象，入参需要传入腾讯云账户secretId，secretKey
        cred = credential.Credential(SMS_CONFIG["secret_id"], SMS_CONFIG["secret_key"])
        
        # 实例化要请求产品的client对象，以smsClient为例
        client = sms_client.SmsClient(cred, SMS_CONFIG["region"])
        
        # 实例化一个请求对象，根据调用的接口和实际情况，可以进一步设置请求参数
        req = models.SendSmsRequest()
        
        # 设置短信应用ID
        req.SmsSdkAppId = SMS_CONFIG["app_id"]
        
        # 设置签名内容
        req.SignName = SMS_CONFIG["sign_name"]
        
        # 设置模板ID
        req.TemplateId = SMS_CONFIG["template_id"]
        
        # 设置模板参数，验证码
        req.TemplateParamSet = [code]
        
        # 设置下发手机号码，采用 E.164 标准，+[国家或地区码][手机号]
        req.PhoneNumberSet = [f"+86{phone}"]
        
        # 通过client对象调用SendSms方法发起请求，返回响应
        resp = client.SendSms(req)
        
        # 处理响应
        return parse_sms_response(resp)
    except TencentCloudSDKException as err:
        return {"success": False, "message": f"短信发送失败: {err}"}

def parse_sms_response(response) -> Dict[str, Any]:
    """
    解析腾讯云短信API响应
    """
    # SDK返回的是对象，需要转换为字典
    if hasattr(response, 'SendStatusSet') and response.SendStatusSet:
        status_set = response.SendStatusSet
        if status_set and status_set[0].Code == "Ok":
            return {"success": True, "message": "短信发送成功"}
        else:
            message = status_set[0].Message if status_set else "未知错误"
            return {"success": False, "message": message}
    
    return {"success": False, "message": "未知错误"} 