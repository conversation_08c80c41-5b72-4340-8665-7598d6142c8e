import jwt
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# 直接导入
from config import TOKEN_CONFIG

def generate_token(user_id: int) -> str:
    """
    生成JWT令牌
    """
    # 生成一个唯一标识符作为令牌ID
    token_id = str(uuid.uuid4())
    
    # 设置过期时间（30天）
    expiration = datetime.utcnow() + timedelta(days=TOKEN_CONFIG["expire_days"])
    
    # 创建令牌载荷
    payload = {
        "sub": str(user_id),  # 用户ID
        "jti": token_id,  # 令牌ID
        "exp": expiration  # 过期时间
    }
    
    # 生成令牌
    token = jwt.encode(
        payload,
        TOKEN_CONFIG["secret_key"],
        algorithm=TOKEN_CONFIG["algorithm"]
    )
    
    return token

def verify_token(token: str) -> Dict[str, Any]:
    """
    验证JWT令牌
    """
    try:
        # 解码令牌
        payload = jwt.decode(
            token,
            TOKEN_CONFIG["secret_key"],
            algorithms=[TOKEN_CONFIG["algorithm"]]
        )
        
        # 检查过期时间
        exp_timestamp = payload.get("exp")
        if exp_timestamp is None:
            return {"valid": False, "message": "令牌缺少过期时间"}
        
        # 将时间戳转换为datetime对象
        exp_datetime = datetime.fromtimestamp(exp_timestamp)
        
        # 检查是否过期
        if datetime.utcnow() > exp_datetime:
            return {"valid": False, "message": "令牌已过期"}
        
        # 提取用户ID
        user_id = payload.get("sub")
        if user_id is None:
            return {"valid": False, "message": "令牌缺少用户ID"}
        
        return {
            "valid": True,
            "user_id": int(user_id),
            "exp": exp_datetime
        }
    
    except jwt.ExpiredSignatureError:
        return {"valid": False, "message": "令牌已过期"}
    except jwt.InvalidTokenError:
        return {"valid": False, "message": "无效的令牌"}
    except Exception as e:
        return {"valid": False, "message": f"验证令牌时出错: {str(e)}"} 