import redis
import json
from typing import Any, Dict, List, Optional

# 直接导入
from config import REDIS_CONFIG

# 创建Redis连接
redis_client = redis.Redis(
    host=REDIS_CONFIG["host"],
    port=REDIS_CONFIG["port"],
    db=REDIS_CONFIG["db"],
    password=REDIS_CONFIG["password"],
    decode_responses=True
)

# 验证码相关操作
def set_verification_code(phone: str, code: str, expire_seconds: int = 300) -> bool:
    """
    设置验证码到Redis，默认5分钟过期
    """
    key = f"verification_code:{phone}"
    return redis_client.setex(key, expire_seconds, code)

def get_verification_code(phone: str) -> Optional[str]:
    """
    获取验证码
    """
    key = f"verification_code:{phone}"
    return redis_client.get(key)

def verify_code(phone: str, code: str) -> bool:
    """
    验证验证码是否正确
    """
    stored_code = get_verification_code(phone)
    if not stored_code:
        return False
    return stored_code == code

# 首页分页缓存相关操作
def set_home_page_cache(cache_key: str, data: List[Dict[str, Any]], expire_seconds: int = 600) -> bool:
    """
    缓存首页分页数据，默认10分钟过期
    """
    key = f"home_page:{cache_key}"
    return redis_client.setex(key, expire_seconds, json.dumps(data))

def get_home_page_cache(cache_key: str) -> Optional[List[Dict[str, Any]]]:
    """
    获取缓存的首页分页数据
    """
    key = f"home_page:{cache_key}"
    data = redis_client.get(key)
    if data:
        return json.loads(data)
    return None

# 清除缓存
def clear_home_page_cache() -> bool:
    """
    清除所有首页缓存（当有新图片上传时调用）
    """
    keys = redis_client.keys("home_page:*")
    if keys:
        return redis_client.delete(*keys) > 0
    return True 