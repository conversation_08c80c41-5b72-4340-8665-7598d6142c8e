#!/usr/bin/env python3
"""
WebSocket消息系统测试脚本
用于验证新的消息系统的功能和性能
"""

import asyncio
import websockets
import json
import requests
import time
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8888"
WS_URL = "ws://localhost:8888/ws/messages"

class MessageSystemTester:
    def __init__(self):
        self.test_users = [1, 2]  # 测试用户ID
        self.websockets = {}
        self.received_messages = []
        
    async def connect_websocket(self, user_id):
        """连接WebSocket"""
        try:
            uri = f"{WS_URL}/{user_id}"
            websocket = await websockets.connect(uri)
            self.websockets[user_id] = websocket
            print(f"用户 {user_id} WebSocket连接成功")
            
            # 启动消息监听
            asyncio.create_task(self.listen_messages(user_id, websocket))
            
            # 发送页面状态
            await self.send_page_status(user_id, "ChatView", "1_2")
            
        except Exception as e:
            print(f"用户 {user_id} WebSocket连接失败: {e}")
    
    async def listen_messages(self, user_id, websocket):
        """监听WebSocket消息"""
        try:
            async for message in websocket:
                data = json.loads(message)
                self.received_messages.append({
                    "user_id": user_id,
                    "timestamp": datetime.now(),
                    "data": data
                })
                print(f"用户 {user_id} 收到消息: {data.get('type', 'unknown')}")
                
        except websockets.exceptions.ConnectionClosed:
            print(f"用户 {user_id} WebSocket连接已关闭")
        except Exception as e:
            print(f"用户 {user_id} 消息监听出错: {e}")
    
    async def send_page_status(self, user_id, page, conversation_id=""):
        """发送页面状态"""
        if user_id in self.websockets:
            message = {
                "type": "page_status",
                "page": page,
                "conversation_id": conversation_id
            }
            await self.websockets[user_id].send(json.dumps(message))
            print(f"用户 {user_id} 页面状态已发送: {page}")
    
    def send_http_message(self, sender_id, receiver_id, content):
        """通过HTTP API发送消息"""
        url = f"{BASE_URL}/messages/send"
        data = {
            "sender_id": sender_id,
            "receiver_id": receiver_id,
            "content": content
        }
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    print(f"HTTP消息发送成功: {sender_id} -> {receiver_id}: {content}")
                    return True
                else:
                    print(f"HTTP消息发送失败: {result.get('message')}")
            else:
                print(f"HTTP请求失败: {response.status_code}")
        except Exception as e:
            print(f"HTTP消息发送异常: {e}")
        
        return False
    
    def get_online_users(self):
        """获取在线用户列表"""
        try:
            response = requests.get(f"{BASE_URL}/messages/online_users")
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    return result.get("data", {}).get("online_users", [])
        except Exception as e:
            print(f"获取在线用户失败: {e}")
        return []
    
    def get_user_status(self, user_id):
        """获取用户状态"""
        try:
            response = requests.get(f"{BASE_URL}/messages/user_status/{user_id}")
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    return result.get("data", {})
        except Exception as e:
            print(f"获取用户状态失败: {e}")
        return {}
    
    async def run_basic_test(self):
        """运行基本功能测试"""
        print("=== 开始基本功能测试 ===")
        
        # 1. 连接WebSocket
        print("1. 连接WebSocket...")
        for user_id in self.test_users:
            await self.connect_websocket(user_id)
        
        await asyncio.sleep(2)
        
        # 2. 检查在线用户
        print("2. 检查在线用户...")
        online_users = self.get_online_users()
        print(f"在线用户: {online_users}")
        
        # 3. 检查用户状态
        print("3. 检查用户状态...")
        for user_id in self.test_users:
            status = self.get_user_status(user_id)
            print(f"用户 {user_id} 状态: {status}")
        
        # 4. 发送测试消息
        print("4. 发送测试消息...")
        test_messages = [
            (1, 2, "Hello from user 1"),
            (2, 1, "Hello back from user 2"),
            (1, 2, "How are you?"),
            (2, 1, "I'm fine, thanks!")
        ]
        
        for sender, receiver, content in test_messages:
            success = self.send_http_message(sender, receiver, content)
            if success:
                await asyncio.sleep(1)  # 等待消息处理
        
        # 5. 检查接收到的消息
        print("5. 检查接收到的消息...")
        print(f"总共接收到 {len(self.received_messages)} 条WebSocket消息")
        for msg in self.received_messages:
            print(f"  用户 {msg['user_id']}: {msg['data'].get('type')} - {msg['timestamp']}")
        
        # 6. 关闭连接
        print("6. 关闭WebSocket连接...")
        for user_id, websocket in self.websockets.items():
            await websocket.close()
        
        print("=== 基本功能测试完成 ===")
    
    async def run_performance_test(self):
        """运行性能测试"""
        print("=== 开始性能测试 ===")
        
        # 连接WebSocket
        for user_id in self.test_users:
            await self.connect_websocket(user_id)
        
        await asyncio.sleep(1)
        
        # 发送大量消息测试
        start_time = time.time()
        message_count = 50
        
        print(f"发送 {message_count} 条消息...")
        for i in range(message_count):
            sender = 1 if i % 2 == 0 else 2
            receiver = 2 if i % 2 == 0 else 1
            content = f"Performance test message {i+1}"
            
            self.send_http_message(sender, receiver, content)
            await asyncio.sleep(0.1)  # 短暂延迟
        
        # 等待所有消息处理完成
        await asyncio.sleep(5)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"性能测试结果:")
        print(f"  发送消息数: {message_count}")
        print(f"  总耗时: {duration:.2f}秒")
        print(f"  平均每条消息: {duration/message_count:.3f}秒")
        print(f"  接收到的WebSocket消息: {len(self.received_messages)}")
        
        # 关闭连接
        for user_id, websocket in self.websockets.items():
            await websocket.close()
        
        print("=== 性能测试完成 ===")

async def main():
    """主函数"""
    tester = MessageSystemTester()
    
    print("WebSocket消息系统测试开始")
    print("请确保服务器正在运行在 localhost:8888")
    
    try:
        # 运行基本功能测试
        await tester.run_basic_test()
        
        print("\n" + "="*50 + "\n")
        
        # 运行性能测试
        await tester.run_performance_test()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
    
    print("测试结束")

if __name__ == "__main__":
    asyncio.run(main())
