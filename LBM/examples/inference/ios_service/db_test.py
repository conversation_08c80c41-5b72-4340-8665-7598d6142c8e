#!/usr/bin/env python
"""
数据库测试脚本 - 验证数据库连接和补全用户信息
"""
import os
import sys
from datetime import datetime, timedelta
import shutil
from PIL import Image
# 设置环境变量
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../../"))
sys.path.insert(0, project_root)

# 导入所需模块
from sqlalchemy.orm import Session
from examples.inference.ios_service.models import SessionLocal, UserAccount, ImageData, ForegroundImage
from examples.inference.ios_service.utils.token_utils import generate_token
from examples.inference.ios_service.config import TOKEN_CONFIG

def test_database_connection():
    """测试数据库连接"""
    try:
        db = SessionLocal()
        print("数据库连接成功！")
        return db
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        return None

def update_user_info(db: Session):
    """更新用户信息"""
    # 查找用户ID为1的用户
    user = db.query(UserAccount).filter(UserAccount.user_id == 1).first()
    
    if not user:
        print("未找到用户ID为1的用户，将创建新用户")
        # 创建新用户
        user = UserAccount(
            user_id=1,
            username="ldz",
            phone_number="***********",
            balance=1000  # 设置一个初始余额
        )
        db.add(user)
    else:
        print(f"找到用户: {user.username}")
        # 更新用户信息
        user.phone_number = "***********"
    
    # 生成新token
    token = generate_token(user.user_id)
    validity_period = datetime.utcnow() + timedelta(days=TOKEN_CONFIG["expire_days"])
    
    # 更新用户token
    user.token = token
    user.validity_period = validity_period
    
    db.commit()
    db.refresh(user)
    
    print(f"用户信息已更新:")
    print(f"  用户ID: {user.user_id}")
    print(f"  用户名: {user.username}")
    print(f"  手机号: {user.phone_number}")
    print(f"  余额: {user.balance}")
    print(f"  Token: {user.token}")
    print(f"  Token有效期: {user.validity_period}")
    
    return user

def add_image_data(db: Session, user_id: int):
    """为用户添加图片信息"""
    # 源图片路径
    src_output_path = "/data/lsc/IClight/LBM/img/out/1.webp"
    src_fg_path = "/data/lsc/IClight/LBM/img/out/1_b.webp"
    
    # 目标图片路径
    output_dir = "/data5/IC-Light/output/IC"
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建低分辨率版本的图片
    low_res_dir = os.path.join(output_dir, "low")
    os.makedirs(low_res_dir, exist_ok=True)
    
    low_res_path = os.path.join(low_res_dir, "1.webp")
    
    # 打开原图并创建低分辨率版本
    try:
        with Image.open(src_output_path) as img:
            # 获取原始尺寸
            width, height = img.size

            # 等比例缩放图片的宽度为400
            new_width = 400
            new_height = int(height * (400 / width))

            # 调整大小
            resized_img = img.resize((new_width, new_height), Image.LANCZOS)

            # 保存低分辨率图片
            resized_img.save(low_res_path, 'WEBP', quality=85)

    except Exception as e:
        print(f"创建低分辨率图片失败: {str(e)}")

    
    # 复制图片到输出目录
    output_filename = os.path.basename(src_output_path)
    fg_filename = os.path.basename(src_fg_path)
    
    dest_output_path = os.path.join(output_dir, output_filename)
    dest_fg_path = os.path.join(output_dir, fg_filename)
    
    try:
        # 复制图片文件
        if os.path.exists(src_output_path):
            shutil.copy2(src_output_path, dest_output_path)
            print(f"已复制图片: {src_output_path} -> {dest_output_path}")
        else:
            print(f"警告: 源图片不存在: {src_output_path}")
            
        if os.path.exists(src_fg_path):
            shutil.copy2(src_fg_path, dest_fg_path)
            print(f"已复制前景图: {src_fg_path} -> {dest_fg_path}")
        else:
            print(f"警告: 源前景图不存在: {src_fg_path}")
    except Exception as e:
        print(f"复制图片文件时出错: {str(e)}")
    
    # 检查是否已存在相同图片记录
    existing_image = db.query(ImageData).filter(
        ImageData.user_id == user_id,
        ImageData.storage_path == output_filename
    ).first()
    
    if existing_image:
        print(f"图片记录已存在，ID: {existing_image.image_id}")
        image_id = existing_image.image_id
    else:
        # 创建图片数据记录
        new_image = ImageData(
            user_id=user_id,
            storage_path=output_filename,
            like_count=0
        )
        
        db.add(new_image)
        db.commit()
        db.refresh(new_image)
        
        print(f"已创建图片记录，ID: {new_image.image_id}")
        image_id = new_image.image_id
    
    # 检查是否已存在相同前景图记录
    existing_fg = db.query(ForegroundImage).filter(
        ForegroundImage.background_id == image_id
    ).first()
    
    if existing_fg:
        print(f"前景图记录已存在，ID: {existing_fg.foreground_id}")
        existing_fg.storage_path = fg_filename
        db.commit()
    else:
        # 创建前景图记录
        new_fg = ForegroundImage(
            background_id=image_id,
            storage_path=fg_filename
        )
        
        db.add(new_fg)
        db.commit()
        db.refresh(new_fg)
        
        print(f"已创建前景图记录，ID: {new_fg.foreground_id}")
    
    # 获取并显示完整记录
    image = db.query(ImageData).filter(ImageData.image_id == image_id).first()
    foreground = db.query(ForegroundImage).filter(ForegroundImage.background_id == image_id).first()
    
    print("\n图片信息:")
    print(f"  图片ID: {image.image_id}")
    print(f"  用户ID: {image.user_id}")
    print(f"  存储路径: {image.storage_path}")
    print(f"  点赞数: {image.like_count}")
    print(f"  创建时间: {image.created_at}")
    
    print("\n前景图信息:")
    print(f"  前景图ID: {foreground.foreground_id}")
    print(f"  背景图ID: {foreground.background_id}")
    print(f"  存储路径: {foreground.storage_path}")
    
    return image, foreground

if __name__ == "__main__":
    # 测试数据库连接
    db = test_database_connection()
    
    if db:
        try:
            # 更新用户信息
            user = update_user_info(db)
            
            # 添加图片信息
            image, foreground = add_image_data(db, user.user_id)
            
            print("\n数据库测试完成！")
        except Exception as e:
            print(f"测试过程中出错: {str(e)}")
        finally:
            db.close()
    else:
        print("由于数据库连接失败，测试终止。") 