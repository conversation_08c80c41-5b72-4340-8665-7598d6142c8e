import os
import sys
import logging
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

# 修改为绝对导入
from examples.inference.ios_service.config import OUTPUT_DIR
from examples.inference.ios_service.routers.auth import router as auth_router
from examples.inference.ios_service.routers.users import router as users_router
from examples.inference.ios_service.routers.images import router as images_router
from examples.inference.ios_service.routers.messages import router as messages_router
from examples.inference.ios_service.routers.websocket import router as websocket_router
from examples.inference.ios_service.models import Base, engine

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="IC Light iOS API",
    description="适用于iOS应用的图像处理API",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

# 注册路由
app.include_router(auth_router)
app.include_router(users_router)
app.include_router(images_router)
app.include_router(messages_router)
app.include_router(websocket_router)

# 配置静态文件目录
app.mount("/images/file", StaticFiles(directory=OUTPUT_DIR), name="images")
app.mount("/images/file/low", StaticFiles(directory=os.path.join(OUTPUT_DIR, "low")), name="low_res_images")
# 为兼容性添加旧路径
app.mount("/images", StaticFiles(directory=OUTPUT_DIR), name="images_compat")
# 添加前景图访问路径
app.mount("/images/foreground", StaticFiles(directory=OUTPUT_DIR), name="foreground_images")

@app.on_event("startup")
async def startup_event():
    """
    应用启动时执行的操作
    """
    logger.info("应用启动中...")
    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    logger.info(f"输出目录: {OUTPUT_DIR}")
    
    # 创建数据库表（如果不存在）
    # Base.metadata.create_all(bind=engine)
    # logger.info("数据库表已创建")

@app.get("/")
async def root():
    """
    API根路径
    """
    return {
        "message": "IC Light iOS API 服务已启动",
        "version": "1.0.0",
        "status": "正常运行"
    }

@app.get("/health")
async def health_check():
    """
    健康检查端点
    """
    return {"status": "ok"}

if __name__ == "__main__":
    import uvicorn
    
    # 运行应用
    uvicorn.run(
        "examples.inference.ios_service.app:app",
        host="0.0.0.0",
        port=8888,
        reload=False,
        log_level="info"
    ) 