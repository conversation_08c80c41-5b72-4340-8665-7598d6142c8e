from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

# 直接导入
from models.models import UserAccount, ImageData, UserImageLike, Comment
from utils.sms_utils import generate_verification_code, send_verification_code
from utils.redis_utils import set_verification_code, verify_code
from utils.token_utils import generate_token
from config import INITIAL_BALANCE, TOKEN_CONFIG

def send_sms_code(phone: str) -> Dict[str, Any]:
    """
    发送短信验证码
    """
    # 生成验证码
    code = generate_verification_code()
    
    # 发送验证码
    response = send_verification_code(phone, code)
    
    # 存储验证码到Redis
    set_verification_code(phone, code)
    
    return {"success": True, "message": "验证码已发送"}

def verify_sms_code(phone: str, code: str) -> Dict[str, Any]:
    """
    验证短信验证码
    """
    # 测试用，后续删除
    if code == "123456":
        return {"success": True, "message": "验证码验证成功"}
    if not verify_code(phone, code):
        return {"success": False, "message": "验证码错误或已过期"}
    
    return {"success": True, "message": "验证码验证成功"}

def register_user(db: Session, phone: str, username: str, avatar: str) -> Dict[str, Any]:
    """
    注册新用户
    """
    # 检查用户是否已存在
    existing_user = db.query(UserAccount).filter(UserAccount.phone_number == phone).first()
    if existing_user:
        return {"success": False, "message": "该手机号已注册"}
    
    # 检查用户名是否已存在
    existing_username = db.query(UserAccount).filter(UserAccount.username == username).first()
    if existing_username:
        return {"success": False, "message": "该用户名已被使用"}
    
    # 创建新用户
    token = generate_token(0)  # 临时用户ID，后面会更新
    validity_period = datetime.utcnow() + timedelta(days=TOKEN_CONFIG["expire_days"])
    
    new_user = UserAccount(
        username=username,
        phone_number=phone,
        avatar=avatar,
        balance=INITIAL_BALANCE,
        token=token,
        validity_period=validity_period
    )
    
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    
    # 使用正确的用户ID生成token
    token = generate_token(new_user.user_id)
    new_user.token = token
    db.commit()
    
    return {
        "success": True,
        "message": "注册成功",
        "user_id": new_user.user_id,
        "username": new_user.username,
        "avatar": avatar,  # 使用传入的avatar值
        "token": token,
        "balance": new_user.balance
    }

def login_user(db: Session, phone: str) -> Dict[str, Any]:
    """
    用户登录
    """
    # 查找用户
    user = db.query(UserAccount).filter(UserAccount.phone_number == phone).first()
    
    if not user:
        return {"success": False, "message": "用户不存在"}
    
    # 生成新token
    token = generate_token(user.user_id)
    validity_period = datetime.utcnow() + timedelta(days=TOKEN_CONFIG["expire_days"])
    
    # 更新用户token
    user.token = token
    user.validity_period = validity_period
    db.commit()
    
    return {
        "success": True,
        "message": "登录成功",
        "user_id": user.user_id,
        "username": user.username,
        "avatar": user.avatar,
        "token": token,
        "balance": user.balance
    }

def validate_token(db: Session, user_id: int, token: str) -> Dict[str, Any]:
    """
    验证用户token
    """
    # 查找用户
    user = db.query(UserAccount).filter(UserAccount.user_id == user_id).first()
    
    if not user:
        return {"valid": False, "message": "用户不存在"}
    
    # 检查token是否匹配
    if user.token != token:
        return {"valid": False, "message": "无效的令牌"}
    
    # 检查token是否过期
    if not user.validity_period or datetime.utcnow() > user.validity_period:
        return {"valid": False, "message": "令牌已过期"}
    
    return {
        "valid": True,
        "user_id": user.user_id,
        "username": user.username,
        "avatar": user.avatar,
        "balance": user.balance  # 添加余额返回
    } 

def delete_account(db: Session, user_id: int) -> Dict[str, Any]:
    """
    注销用户账户
    
    删除用户账户及相关数据，包括：
    1. 用户上传的图片信息
    2. 用户点赞记录
    3. 用户评论及其子评论
    4. 用户账户信息
    """
    # 查找用户
    user = db.query(UserAccount).filter(UserAccount.user_id == user_id).first()
    
    if not user:
        return {"success": False, "message": "用户不存在"}
    
    try:
        # 找到用户发表的所有评论
        user_comments = db.query(Comment).filter(Comment.user_id == user_id).all()
        
        # 递归删除用户评论及其子评论
        for comment in user_comments:
            # 递归删除所有子评论
            delete_comment_and_replies(db, comment.comment_id)
        
        # 删除用户的点赞记录
        db.query(UserImageLike).filter(UserImageLike.user_id == user_id).delete()
        
        # 删除用户的图片
        db.query(ImageData).filter(ImageData.user_id == user_id).delete()
        
        # 最后删除用户账户
        db.delete(user)
        db.commit()
        
        return {"success": True, "message": "账户已成功注销"}
    except Exception as e:
        db.rollback()
        return {"success": False, "message": f"注销账户失败: {str(e)}"}

def delete_comment_and_replies(db: Session, comment_id: int):
    """
    递归删除评论及其所有子评论
    
    Args:
        db: 数据库会话
        comment_id: 要删除的评论ID
    """
    # 查找所有子评论
    replies = db.query(Comment).filter(Comment.parent_comment_id == comment_id).all()
    
    # 递归删除每个子评论
    for reply in replies:
        delete_comment_and_replies(db, reply.comment_id)
    
    # 删除当前评论
    db.query(Comment).filter(Comment.comment_id == comment_id).delete() 