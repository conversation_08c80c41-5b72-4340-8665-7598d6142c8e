import requests
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 直接导入
from models.models import UserAccount, ImageData, UserImageLike

def get_user_balance(db: Session, user_id: int) -> Dict[str, Any]:
    """
    获取用户积分余额
    """
    user = db.query(UserAccount).filter(UserAccount.user_id == user_id).first()
    
    if not user:
        return {"success": False, "message": "用户不存在"}
    
    return {
        "success": True,
        "user_id": user.user_id,
        "username": user.username,
        "balance": user.balance
    }

def update_user_balance(db: Session, user_id: int, change: int) -> Dict[str, Any]:
    """
    更新用户积分余额
    """
    user = db.query(UserAccount).filter(UserAccount.user_id == user_id).first()
    
    if not user:
        return {"success": False, "message": "用户不存在"}
    
    # 更新余额
    user.balance += change
    
    # 确保余额不为负数
    if user.balance < 0:
        user.balance = 0
    
    db.commit()
    
    return {
        "success": True,
        "user_id": user.user_id,
        "balance": user.balance,
        "change": change
    }

def verify_purchase(db: Session, user_id: int, product_id: str, receipt_data: str, is_sandbox: bool = False, transaction_id: str = None, original_transaction_id: str = None) -> Dict[str, Any]:
    """
    验证App内购买并更新用户余额
    
    参数:
    - user_id: 用户ID
    - product_id: 产品ID，例如 'com.nienie.nn10'
    - receipt_data: 收据数据，在正式环境中需要发送给Apple验证
    - is_sandbox: 是否是沙盒环境（前端传入）
    - transaction_id: 交易ID（可选）
    - original_transaction_id: 原始交易ID（可选）
    
    返回:
    - 包含验证结果和更新后的余额信息
    """
    user = db.query(UserAccount).filter(UserAccount.user_id == user_id).first()
    
    if not user:
        return {"success": False, "message": "用户不存在"}
    
    logger.info(f"验证购买: user_id={user_id}, product_id={product_id}, is_sandbox={is_sandbox}")
    
    # 尝试解析receipt_data是否为JSON字符串
    try:
        # 检查是否是JSON字符串
        receipt_json = json.loads(receipt_data)
        # 如果是JSON，可能包含交易ID等信息
        if "transaction_id" in receipt_json:
            transaction_id = receipt_json.get("transaction_id")
        if "original_transaction_id" in receipt_json:
            original_transaction_id = receipt_json.get("original_transaction_id")
        if "product_id" in receipt_json and not product_id:
            product_id = receipt_json.get("product_id")
        
        logger.info(f"从JSON中提取信息: transaction_id={transaction_id}, product_id={product_id}")
    except (json.JSONDecodeError, TypeError):
        # 不是JSON字符串，继续处理
        pass
    
    # 如果有交易ID但没有有效的收据数据，使用简化验证
    if transaction_id and (not receipt_data or receipt_data == "{}" or is_sandbox):
        logger.info(f"使用交易ID简化验证: transaction_id={transaction_id}, product_id={product_id}")
        return verify_purchase_simplified(db, user_id, product_id)
    
    # 如果明确指定是沙盒环境，使用简化验证
    if is_sandbox:
        logger.info("使用沙盒环境简化验证")
        return verify_purchase_simplified(db, user_id, product_id)
    
    # 首先尝试生产环境验证
    logger.info("尝试生产环境验证")
    production_result = verify_with_apple(db, user_id, product_id, receipt_data, sandbox=False)
    
    # 检查是否需要切换到沙盒环境
    if not production_result["success"]:
        status = production_result.get("status", 0)
        if status == 21007:  # 沙盒收据
            logger.info("收据来自沙盒环境，切换到沙盒验证")
            return verify_with_apple(db, user_id, product_id, receipt_data, sandbox=True)
        elif status == 21002:  # 收据数据无效
            logger.info("收据数据无效，尝试使用简化验证")
            return verify_purchase_simplified(db, user_id, product_id)
    
    return production_result

def verify_with_apple(db: Session, user_id: int, product_id: str, receipt_data: str, sandbox: bool = False) -> Dict[str, Any]:
    """
    向Apple验证服务器验证收据
    
    参数:
    - user_id: 用户ID
    - product_id: 产品ID
    - receipt_data: 收据数据
    - sandbox: 是否使用沙盒环境
    
    返回:
    - 验证结果
    """
    # Apple验证URL
    if sandbox:
        verify_url = "https://sandbox.itunes.apple.com/verifyReceipt"
    else:
        verify_url = "https://buy.itunes.apple.com/verifyReceipt"
    
    # 准备请求数据
    request_data = {
        "receipt-data": receipt_data,
        "password": "YOUR_APP_SPECIFIC_SHARED_SECRET"  # 替换为您的App专用共享密钥
    }
    
    try:
        # 发送验证请求
        logger.info(f"发送验证请求到: {verify_url}")
        response = requests.post(verify_url, json=request_data, timeout=30)
        response_data = response.json()
        
        # 检查响应状态
        status = response_data.get("status", 999)
        logger.info(f"Apple验证响应状态: {status}")
        
        if status == 0:  # 成功
            # 解析收据信息
            receipt = response_data.get("receipt", {})
            in_app = receipt.get("in_app", [])
            
            # 查找匹配的产品
            for item in in_app:
                if item.get("product_id") == product_id:
                    # 验证成功，更新用户余额
                    coin_amount = extract_coin_amount(product_id)
                    if coin_amount > 0:
                        user = db.query(UserAccount).filter(UserAccount.user_id == user_id).first()
                        user.balance += coin_amount
                        db.commit()
                        
                        return {
                            "success": True,
                            "user_id": user_id,
                            "balance": user.balance,
                            "product_id": product_id,
                            "coin_amount": coin_amount,
                            "message": "充值成功"
                        }
            
            # 没有找到匹配的产品
            return {
                "success": False,
                "message": "收据验证成功，但未找到匹配的产品",
                "status": status
            }
        elif status == 21007:
            # 收据来自沙盒环境
            return {
                "success": False,
                "message": "收据来自沙盒环境",
                "status": status
            }
        elif status == 21002:
            # 收据数据无效，可能是格式问题
            return {
                "success": False,
                "message": "收据数据无效或格式不正确",
                "status": status
            }
        else:
            # 其他错误
            return {
                "success": False,
                "message": f"收据验证失败，状态码: {status}",
                "status": status
            }
    except Exception as e:
        logger.error(f"验证过程中出错: {str(e)}")
        # 如果验证过程中出错，回退到简化验证
        return verify_purchase_simplified(db, user_id, product_id)

def verify_purchase_simplified(db: Session, user_id: int, product_id: str) -> Dict[str, Any]:
    """
    简化的购买验证逻辑，用于开发环境或验证失败时的回退
    
    参数:
    - user_id: 用户ID
    - product_id: 产品ID
    
    返回:
    - 验证结果
    """
    logger.info(f"使用简化验证: user_id={user_id}, product_id={product_id}")
    
    # 解析产品ID，获取充值金额
    coin_amount = extract_coin_amount(product_id)
    
    if coin_amount <= 0:
        return {"success": False, "message": "无效的产品ID"}
    
    # 更新用户余额
    user = db.query(UserAccount).filter(UserAccount.user_id == user_id).first()
    user.balance += coin_amount
    db.commit()
    
    return {
        "success": True,
        "user_id": user.user_id,
        "balance": user.balance,
        "product_id": product_id,
        "coin_amount": coin_amount,
        "message": "充值成功"
    }

def extract_coin_amount(product_id: str) -> int:
    """
    从产品ID中提取捏币金额
    
    参数:
    - product_id: 产品ID，例如 'com.nienie.nn10'
    
    返回:
    - 捏币金额
    """
    # 产品ID与捏币金额的映射
    product_mapping = {
        "com.nienie.nn10": 10,
        "com.nienie.nn30": 30,
        "com.nienie.nn50": 50,
        "com.nienie.nn100": 100,
        "com.nienie.nn200": 200,
        "com.nienie.nn500": 500
    }
    
    # 直接从映射中获取
    if product_id in product_mapping:
        return product_mapping[product_id]
    
    # 尝试解析产品ID格式
    try:
        if product_id.startswith("com.nienie.nn"):
            amount_str = product_id[len("com.nienie.nn"):]
            return int(amount_str)
    except (ValueError, AttributeError):
        pass
    
    return 0

def check_balance_sufficient(db: Session, user_id: int, required: int) -> Dict[str, Any]:
    """
    检查用户积分是否足够
    """
    user = db.query(UserAccount).filter(UserAccount.user_id == user_id).first()
    
    if not user:
        return {"success": False, "message": "用户不存在"}
    
    if user.balance < required:
        return {
            "success": False,
            "message": f"积分不足，当前积分：{user.balance}，需要积分：{required}"
        }
    
    return {
        "success": True,
        "user_id": user.user_id,
        "balance": user.balance
    }

def get_user_images(db: Session, user_id: int, skip: int = 0, limit: int = 20) -> Dict[str, Any]:
    """
    获取用户上传的图片
    """
    user = db.query(UserAccount).filter(UserAccount.user_id == user_id).first()
    
    if not user:
        return {"success": False, "message": "用户不存在"}
    
    # 查询用户图片
    images = db.query(ImageData).filter(ImageData.user_id == user_id)\
               .order_by(ImageData.created_at.desc())\
               .offset(skip).limit(limit).all()
    
    # 查询图片总数
    total = db.query(func.count(ImageData.image_id))\
              .filter(ImageData.user_id == user_id).scalar()
    
    # 如果没有更多图片，返回空列表
    if not images:
        return {
            "success": True,
            "user_id": user_id,
            "images": [],
            "total": total,
            "skip": skip,
            "limit": limit
        }
    
    # 转换为字典并添加点赞状态
    result = []
    for image in images:
        image_dict = image.to_dict()
        
        # 检查用户是否点赞过该图片
        like_record = db.query(UserImageLike).filter(
            UserImageLike.user_id == user_id,
            UserImageLike.image_id == image.image_id,
            UserImageLike.liked_status == 1
        ).first()
        image_dict["liked"] = like_record is not None
        
        result.append(image_dict)
    
    return {
        "success": True,
        "user_id": user_id,
        "images": result,
        "total": total,
        "skip": skip,
        "limit": limit
    } 