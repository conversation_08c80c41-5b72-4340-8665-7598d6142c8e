from sqlalchemy.orm import Session
from sqlalchemy import desc
from models.models import Message, Conversation, UserAccount, DeviceToken, UserConversationMute
from typing import Dict, List, Any, Optional
import uuid
import json
import os
import asyncio
from datetime import datetime
from aioapns import APNs, NotificationRequest, PushType
from fastapi import WebSocket
import logging

# 配置日志
logger = logging.getLogger(__name__)

# APNs 配置
APNS_KEY_PATH = os.environ.get('APNS_KEY_PATH', '/data/lsc/IClight/AuthKey_CFA38DCM2S.p8')
APNS_KEY_ID = os.environ.get('APNS_KEY_ID', 'CFA38DCM2S')
APNS_TEAM_ID = os.environ.get('APNS_TEAM_ID', 'F3SY3YNWK9')
APNS_BUNDLE_ID = os.environ.get('APNS_BUNDLE_ID', 'com.sorealhuman.NIENIE')
APNS_MODE = os.environ.get('APNS_MODE', 'sandbox')  # 'production' 或 'sandbox'

class WebSocketManager:
    """WebSocket连接管理器"""

    def __init__(self):
        # 存储用户ID到WebSocket连接的映射
        self.active_connections: Dict[int, WebSocket] = {}
        # 存储用户当前页面状态
        self.user_page_status: Dict[int, Dict[str, str]] = {}

    async def connect(self, websocket: WebSocket, user_id: int):
        """建立WebSocket连接"""
        await websocket.accept()
        self.active_connections[user_id] = websocket
        #logger.info(f"用户 {user_id} WebSocket连接已建立")

    def disconnect(self, user_id: int):
        """断开WebSocket连接"""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        if user_id in self.user_page_status:
            del self.user_page_status[user_id]
        #logger.info(f"用户 {user_id} WebSocket连接已断开")

    def update_user_status(self, user_id: int, page: str, conversation_id: str = ""):
        """更新用户页面状态"""
        self.user_page_status[user_id] = {
            "page": page,  # "ConversationListView" 或 "ChatView"
            "conversation_id": conversation_id
        }
        #logger.info(f"用户 {user_id} 页面状态更新: {page}, 会话: {conversation_id}")

    async def send_message_to_user(self, user_id: int, message: Dict[str, Any]):
        """向特定用户发送WebSocket消息"""
        if user_id in self.active_connections:
            try:
                websocket = self.active_connections[user_id]
                await websocket.send_text(json.dumps(message))
                #logger.info(f"WebSocket消息已发送给用户 {user_id}")
                return True
            except Exception as e:
                #logger.error(f"发送WebSocket消息失败: {str(e)}")
                # 连接可能已断开，清理连接
                self.disconnect(user_id)
                return False
        return False

    def is_user_online(self, user_id: int) -> bool:
        """检查用户是否在线"""
        return user_id in self.active_connections

    def is_user_in_conversation(self, user_id: int, conversation_id: str) -> bool:
        """检查用户是否在特定会话页面"""
        if user_id not in self.user_page_status:
            return False
        status = self.user_page_status[user_id]
        return (status.get("page") == "ChatView" and
                status.get("conversation_id") == conversation_id)

    def is_user_in_conversation_list(self, user_id: int) -> bool:
        """检查用户是否在会话列表页面"""
        if user_id not in self.user_page_status:
            return False
        return self.user_page_status[user_id].get("page") == "ConversationListView"

# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()

def get_conversation_id(user1_id: int, user2_id: int) -> str:
    """
    生成会话ID：较小ID_较大ID
    """
    if user1_id < user2_id:
        return f"{user1_id}_{user2_id}"
    else:
        return f"{user2_id}_{user1_id}"

def get_user_conversations(db: Session, user_id: int, read_message_ids: Dict[str, int] = None, skip: int = 0, limit: int = 20) -> Dict[str, Any]:
    """
    获取用户所有私信会话列表

    Args:
        db: 数据库会话
        user_id: 用户ID
        read_message_ids: 每个会话已读到的最后消息ID字典 {conversation_id: last_read_message_id}
        skip: 跳过的数量
        limit: 限制数量
    """
    try:
        # 如果没有传入已读消息ID字典，使用空字典
        if read_message_ids is None:
            read_message_ids = {}

        # 查找用户参与的所有会话
        conversations = db.query(Conversation).filter(
            ((Conversation.user1_id == user_id) | (Conversation.user2_id == user_id))
        ).all()

        # 收集会话数据和排序信息
        conversation_data_list = []
        for conv in conversations:
            # 获取对话的另一方用户
            other_user_id = conv.user1_id if conv.user1_id != user_id else conv.user2_id
            other_user = db.query(UserAccount).filter(UserAccount.user_id == other_user_id).first()

            if not other_user:
                continue

            # 获取该会话中的最新一条消息（不再考虑删除状态）
            latest_message = db.query(Message).filter(
                Message.conversation_id == conv.conversation_id
            ).order_by(desc(Message.created_at)).first()

            # 计算未读消息数：基于前端传来的已读消息ID
            last_read_message_id = read_message_ids.get(conv.conversation_id, 0)

            # 如果会话ID不在前端传来的列表中，说明是新对话，已读消息ID为0
            is_new_conversation = conv.conversation_id not in read_message_ids

            unread_count = db.query(Message).filter(
                Message.conversation_id == conv.conversation_id,
                Message.receiver_id == user_id,  # 只计算接收到的消息
                Message.message_id > last_read_message_id  # ID大于已读消息ID的消息
            ).count()

            # 调试日志
            if is_new_conversation and unread_count > 0:
                logger.info(f"新对话 {conv.conversation_id}，未读消息数: {unread_count}")
            elif unread_count > 0:
                logger.info(f"已有对话 {conv.conversation_id}，已读到消息ID: {last_read_message_id}，未读消息数: {unread_count}")

            # 获取免打扰状态
            mute_setting = db.query(UserConversationMute).filter(
                UserConversationMute.user_id == user_id,
                UserConversationMute.conversation_id == conv.conversation_id
            ).first()
            is_muted = mute_setting.is_muted if mute_setting else False

            # 构建会话信息
            conversation_data = {
                "conversation_id": conv.conversation_id,
                "user_id": other_user.user_id,
                "username": other_user.username,
                "avatar_url": other_user.avatar,
                "unread_count": unread_count,
                "is_muted": is_muted
            }

            # 用于排序的时间戳
            sort_timestamp = datetime.fromtimestamp(0)  # 默认时间

            # 如果有最新消息，添加消息信息；否则显示默认信息
            if latest_message:
                time_str = latest_message.created_at.isoformat()
                conversation_data.update({
                    "latest_message": latest_message.content,
                    "latest_message_time": time_str
                })
                sort_timestamp = latest_message.created_at
            else:
                # 没有消息时显示默认信息
                conversation_data.update({
                    "latest_message": "暂无消息",
                    "latest_message_time": datetime.fromtimestamp(0).isoformat()  # 使用1970年作为默认时间
                })

            conversation_data_list.append({
                "data": conversation_data,
                "sort_time": sort_timestamp
            })

        # 按最新消息时间降序排序
        conversation_data_list.sort(key=lambda x: x["sort_time"], reverse=True)

        # 应用分页并提取数据
        paginated_data = conversation_data_list[skip:skip + limit]
        result = [item["data"] for item in paginated_data]

        # 即使没有会话，也返回成功和空数组
        return {
            "success": True,
            "data": {
                "conversations": result,
                "total": len(result)
            }
        }
    except Exception as e:
        logger.error(f"获取会话列表失败，用户ID: {user_id}, 错误: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return {
            "success": False,
            "message": f"获取会话列表失败: {str(e)}"
        }

def get_conversation_messages(db: Session, conversation_id: str, user_id: int, last_message_id: int = 0) -> Dict[str, Any]:
    """
    获取特定会话的新消息列表（基于最后一条消息ID的增量获取）

    Args:
        db: 数据库会话
        conversation_id: 会话ID
        user_id: 用户ID
        last_message_id: 客户端已有的最后一条消息ID，返回ID大于此值的新消息

    Returns:
        包含新消息列表和对话方信息的字典
    """
    try:
        # 验证用户是否是会话的参与者
        conversation = db.query(Conversation).filter(Conversation.conversation_id == conversation_id).first()
        if not conversation:
            # 会话不存在，返回空消息列表而不是错误
            return {
                "success": True,
                "data": {
                    "messages": [],
                    "other_user": {},
                    "total": 0
                }
            }

        if conversation.user1_id != user_id and conversation.user2_id != user_id:
            return {"success": False, "message": "无权访问此会话"}

        # 获取ID大于last_message_id的新消息，按时间正序（最新消息在下面）
        messages = db.query(Message).filter(
            Message.conversation_id == conversation_id,
            Message.message_id > last_message_id
        ).order_by(Message.created_at).all()

        # 格式化消息（不再处理消息状态）
        result = []
        for msg in messages:
            result.append({
                "message_id": msg.message_id,
                "sender_id": msg.sender_id,
                "receiver_id": msg.receiver_id,
                "content": msg.content,
                "created_at": msg.created_at.isoformat(),
                "is_self": msg.sender_id == user_id
            })

        # 获取对话的另一方用户信息
        other_user_id = conversation.user1_id if conversation.user1_id != user_id else conversation.user2_id
        other_user = db.query(UserAccount).filter(UserAccount.user_id == other_user_id).first()

        other_user_info = {
            "user_id": other_user.user_id,
            "username": other_user.username,
            "avatar_url": other_user.avatar
        } if other_user else {}

        return {
            "success": True,
            "data": {
                "messages": result,
                "other_user": other_user_info,
                "total": len(result)
            }
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"获取消息列表失败: {str(e)}"
        }

def send_message(db: Session, sender_id: int, receiver_id: int, content: str) -> Dict[str, Any]:
    """
    发送私信消息
    """
    try:
        if sender_id == receiver_id:
            return {"success": False, "message": "不能给自己发消息"}
        
        # 验证发送方和接收方用户是否存在
        sender = db.query(UserAccount).filter(UserAccount.user_id == sender_id).first()
        receiver = db.query(UserAccount).filter(UserAccount.user_id == receiver_id).first()
        
        if not sender or not receiver:
            return {"success": False, "message": "用户不存在"}
        
        # 获取或创建会话
        conversation_id = get_conversation_id(sender_id, receiver_id)
        conversation = db.query(Conversation).filter(Conversation.conversation_id == conversation_id).first()
        
        if not conversation:
            # 确保user1_id是较小的ID
            user1_id = min(sender_id, receiver_id)
            user2_id = max(sender_id, receiver_id)
            
            conversation = Conversation(
                conversation_id=conversation_id,
                user1_id=user1_id,
                user2_id=user2_id
            )
            db.add(conversation)
            db.flush()
        
        # 创建新消息
        new_message = Message(
            conversation_id=conversation_id,
            sender_id=sender_id,
            receiver_id=receiver_id,
            content=content,
            status=1  # 未读状态
        )
        db.add(new_message)
        db.flush()
        
        # 更新会话的最新消息ID
        conversation.latest_message_id = new_message.message_id
        db.commit()

        # 向接收方发送实时消息推送
        asyncio.create_task(send_real_time_message(
            db, receiver_id, sender.username, new_message, conversation_id
        ))
        
        return {
            "success": True,
            "data": {
                "message_id": new_message.message_id,
                "conversation_id": conversation_id,
                "created_at": new_message.created_at.isoformat()
            }
        }
    except Exception as e:
        db.rollback()
        return {
            "success": False,
            "message": f"发送消息失败: {str(e)}"
        }

async def send_real_time_message(db: Session, receiver_id: int, sender_name: str, message: Message, conversation_id: str):
    """
    发送实时消息推送（WebSocket优先，推送通知备用）
    """
    try:
        # 检查接收方是否开启了免打扰
        mute_setting = db.query(UserConversationMute).filter(
            UserConversationMute.user_id == receiver_id,
            UserConversationMute.conversation_id == conversation_id
        ).first()

        is_muted = mute_setting.is_muted if mute_setting else False

        # 构建消息数据
        message_data = {
            "type": "new_message",
            "data": {
                "message_id": message.message_id,
                "conversation_id": conversation_id,
                "sender_id": message.sender_id,
                "receiver_id": message.receiver_id,
                "content": message.content,
                "status": message.status,  # 使用更新后的状态
                "created_at": message.created_at.isoformat(),
                "sender_name": sender_name,
                "is_self": False
            }
        }

        # 检查接收方是否在线
        if websocket_manager.is_user_online(receiver_id):
            # 用户在线，发送WebSocket消息
            success = await websocket_manager.send_message_to_user(receiver_id, message_data)

            if success:
                # 检查用户是否在相关页面，如果在则不发送推送通知
                if (websocket_manager.is_user_in_conversation(receiver_id, conversation_id) or
                    websocket_manager.is_user_in_conversation_list(receiver_id)):
                    #logger.info(f"用户 {receiver_id} 在相关页面，跳过推送通知")
                    return
                else:
                    # 用户在其他页面，检查是否开启免打扰
                    if is_muted:
                        #logger.info(f"用户 {receiver_id} 开启了免打扰，跳过推送通知")
                        return
                    else:
                        # 用户在其他页面且未开启免打扰，发送推送通知
                        #logger.info(f"用户 {receiver_id} 在其他页面，发送推送通知")
                        await send_push_notification(db, receiver_id, sender_name, message.content, conversation_id, message.message_id)
            else:
                # WebSocket发送失败，检查是否开启免打扰
                if not is_muted:
                    await send_push_notification(db, receiver_id, sender_name, message.content, conversation_id, message.message_id)
        else:
            # 用户不在线，检查是否开启免打扰
            if not is_muted:
                #logger.info(f"用户 {receiver_id} 不在线，发送推送通知")
                await send_push_notification(db, receiver_id, sender_name, message.content, conversation_id, message.message_id)
            else:
                # 用户不在线但开启了免打扰，只发送WebSocket消息（如果用户稍后上线会收到）
                #logger.info(f"用户 {receiver_id} 不在线且开启了免打扰，跳过推送通知")
                pass

    except Exception as e:
        logger.error(f"发送实时消息失败: {str(e)}")
        # 出错时检查是否开启免打扰，如果没有开启则发送推送通知作为备用
        try:
            mute_setting = db.query(UserConversationMute).filter(
                UserConversationMute.user_id == receiver_id,
                UserConversationMute.conversation_id == conversation_id
            ).first()
            is_muted = mute_setting.is_muted if mute_setting else False

            if not is_muted:
                await send_push_notification(db, receiver_id, sender_name, message.content, conversation_id, message.message_id)
        except Exception as inner_e:
            logger.error(f"检查免打扰状态失败: {str(inner_e)}")
            # 如果连检查免打扰状态都失败了，则发送推送通知作为最后的备用方案
            await send_push_notification(db, receiver_id, sender_name, message.content, conversation_id, message.message_id)

async def send_push_notification(db: Session, receiver_id: int, sender_name: str, message_content: str, conversation_id: str, message_id: int):
    """
    向接收方发送推送通知（使用aioapns库）
    """
    try:
        # 获取接收方的设备令牌
        device_tokens = db.query(DeviceToken).filter(
            DeviceToken.user_id == receiver_id,
            DeviceToken.is_active == True
        ).all()
        
        if not device_tokens:
            return  # 用户没有注册设备令牌，无法发送推送
        
        # 准备推送通知
        try:
            # 消息内容
            alert = f"{sender_name}: {message_content}"
            if len(alert) > 100:
                alert = alert[:97] + "..."
            
            # 读取APNs密钥文件
            with open(APNS_KEY_PATH, "r") as f:
                key = f.read()
            
            # 初始化APNs客户端
            client = APNs(
                key_id=APNS_KEY_ID,
                team_id=APNS_TEAM_ID,
                key=key,
                topic=APNS_BUNDLE_ID,
                use_sandbox=APNS_MODE == 'sandbox'
            )
            
            # 向所有设备发送通知
            for token in device_tokens:
                try:
                    # 准备通知请求
                    request = NotificationRequest(
                        device_token=token.device_token,
                        message={
                            'aps': {
                                'alert': alert,
                                'sound': 'default',
                                'badge': 1,
                            },
                            'conversation_id': conversation_id,
                            'message_id': message_id,
                            'sender_id': receiver_id
                        },
                        push_type=PushType.ALERT
                    )
                    
                    # 发送通知
                    result = await client.send_notification(request)
                    
                    # 检查结果
                    if not result.is_successful:
                        print(f"发送通知失败: {result.description}")
                        
                        # 如果设备令牌无效，标记为非活动
                        if "BadDeviceToken" in result.description:
                            token.is_active = False
                            db.commit()
                    
                except Exception as e:
                    print(f"向设备 {token.device_token} 发送通知失败: {str(e)}")
                        
        except Exception as e:
            print(f"初始化APNs客户端失败: {str(e)}")
            
    except Exception as e:
        print(f"发送推送通知失败: {str(e)}")

def delete_message(db: Session, user_id: int, message_id: int) -> Dict[str, Any]:
    """
    删除消息（仅对当前用户）
    """
    try:
        message = db.query(Message).filter(Message.message_id == message_id).first()
        
        if not message:
            return {"success": False, "message": "消息不存在"}
        
        # 检查用户是否有权限删除
        if message.sender_id != user_id and message.receiver_id != user_id:
            return {"success": False, "message": "无权删除此消息"}
        
        # 如果是发送方删除
        if message.sender_id == user_id:
            if message.status == 3 or message.status == 4:
                # 如果接收方也已删除，则物理删除
                db.delete(message)
            else:
                # 标记为发送方删除
                message.status = 3
        
        # 如果是接收方删除
        elif message.receiver_id == user_id:
            if message.status == 3 or message.status == 4:
                # 如果发送方也已删除，则物理删除
                db.delete(message)
            else:
                # 标记为接收方删除
                message.status = 4
        
        db.commit()
        
        return {"success": True, "message": "删除成功"}
    except Exception as e:
        db.rollback()
        return {
            "success": False,
            "message": f"删除消息失败: {str(e)}"
        }

def delete_conversation(db: Session, user_id: int, conversation_id: str) -> Dict[str, Any]:
    """
    删除整个会话（仅对当前用户）
    """
    try:
        conversation = db.query(Conversation).filter(Conversation.conversation_id == conversation_id).first()

        if not conversation:
            return {"success": False, "message": "会话不存在"}

        # 检查用户是否是会话的参与者
        if conversation.user1_id != user_id and conversation.user2_id != user_id:
            return {"success": False, "message": "无权删除此会话"}

        # 标记用户作为发送方的消息为已删除
        sent_messages = db.query(Message).filter(
            Message.conversation_id == conversation_id,
            Message.sender_id == user_id,
            Message.status != 3  # 未被发送方删除
        ).all()

        for msg in sent_messages:
            if msg.status == 4:  # 接收方已删除
                db.delete(msg)  # 物理删除
            else:
                msg.status = 3  # 标记为发送方删除

        # 标记用户作为接收方的消息为已删除
        received_messages = db.query(Message).filter(
            Message.conversation_id == conversation_id,
            Message.receiver_id == user_id,
            Message.status != 4  # 未被接收方删除
        ).all()

        for msg in received_messages:
            if msg.status == 3:  # 发送方已删除
                db.delete(msg)  # 物理删除
            else:
                msg.status = 4  # 标记为接收方删除

        db.commit()

        # 检查会话是否还有消息
        remaining_messages = db.query(Message).filter(Message.conversation_id == conversation_id).count()
        if remaining_messages == 0:
            # 没有消息时，将latest_message_id设为None，但保留会话记录
            conversation.latest_message_id = None
            db.commit()

        return {"success": True, "message": "会话删除成功"}
    except Exception as e:
        db.rollback()
        return {
            "success": False,
            "message": f"删除会话失败: {str(e)}"
        }

def register_device_token(db: Session, user_id: int, device_token: str) -> Dict[str, Any]:
    """
    注册设备令牌用于推送通知
    """
    try:
        # 检查用户是否存在
        user = db.query(UserAccount).filter(UserAccount.user_id == user_id).first()
        if not user:
            return {"success": False, "message": "用户不存在"}
        
        # 检查设备令牌是否已存在
        token = db.query(DeviceToken).filter(
            DeviceToken.user_id == user_id,
            DeviceToken.device_token == device_token
        ).first()
        
        if token:
            # 已存在则更新状态
            token.is_active = True
        else:
            # 不存在则创建新记录
            token = DeviceToken(
                user_id=user_id,
                device_token=device_token,
                is_active=True
            )
            db.add(token)
            
        db.commit()
        
        return {
            "success": True,
            "message": "设备令牌注册成功"
        }
    except Exception as e:  # 添加异常捕获
        db.rollback()  # 回滚事务
        return {
            "success": False,
            "message": f"设备令牌注册失败: {str(e)}"
        }

def clear_device_tokens(db: Session, user_id: int) -> Dict[str, Any]:
    """
    清空用户的所有设备令牌
    """
    try:
        # 检查用户是否存在
        user = db.query(UserAccount).filter(UserAccount.user_id == user_id).first()
        if not user:
            return {"success": False, "message": "用户不存在"}

        # 删除用户的所有设备令牌
        db.query(DeviceToken).filter(DeviceToken.user_id == user_id).delete()
        db.commit()

        return {
            "success": True,
            "message": "设备令牌清空成功"
        }
    except Exception as e:
        db.rollback()
        return {
            "success": False,
            "message": f"设备令牌清空失败: {str(e)}"
        }

def get_online_users() -> List[int]:
    """
    获取当前在线用户列表
    """
    return list(websocket_manager.active_connections.keys())

def get_user_status(user_id: int) -> Dict[str, Any]:
    """
    获取用户状态信息
    """
    is_online = websocket_manager.is_user_online(user_id)
    status = websocket_manager.user_page_status.get(user_id, {})

    return {
        "user_id": user_id,
        "is_online": is_online,
        "page": status.get("page", ""),
        "conversation_id": status.get("conversation_id", "")
    }

def set_conversation_mute(db: Session, user_id: int, conversation_id: str, is_muted: bool) -> Dict[str, Any]:
    """
    设置用户会话的免打扰状态
    """
    try:
        # 检查用户是否存在
        user = db.query(UserAccount).filter(UserAccount.user_id == user_id).first()
        if not user:
            return {"success": False, "message": "用户不存在"}

        # 检查会话是否存在且用户是参与者
        conversation = db.query(Conversation).filter(Conversation.conversation_id == conversation_id).first()
        if not conversation:
            return {"success": False, "message": "会话不存在"}

        if conversation.user1_id != user_id and conversation.user2_id != user_id:
            return {"success": False, "message": "无权设置此会话的免打扰状态"}

        # 查找或创建免打扰设置
        mute_setting = db.query(UserConversationMute).filter(
            UserConversationMute.user_id == user_id,
            UserConversationMute.conversation_id == conversation_id
        ).first()

        if mute_setting:
            # 更新现有设置
            mute_setting.is_muted = is_muted
        else:
            # 创建新设置
            mute_setting = UserConversationMute(
                user_id=user_id,
                conversation_id=conversation_id,
                is_muted=is_muted
            )
            db.add(mute_setting)

        db.commit()

        return {
            "success": True,
            "message": f"免打扰状态已{'开启' if is_muted else '关闭'}",
            "data": {
                "user_id": user_id,
                "conversation_id": conversation_id,
                "is_muted": is_muted
            }
        }
    except Exception as e:
        db.rollback()
        return {
            "success": False,
            "message": f"设置免打扰状态失败: {str(e)}"
        }

async def broadcast_user_status_change(user_id: int, is_online: bool):
    """
    广播用户状态变化（可用于未来扩展）
    """
    status_message = {
        "type": "user_status_change",
        "data": {
            "user_id": user_id,
            "is_online": is_online
        }
    }

    # 这里可以向相关用户广播状态变化
    # 例如向用户的好友或正在聊天的用户发送状态更新
    #logger.info(f"用户 {user_id} 状态变化: {'在线' if is_online else '离线'}")