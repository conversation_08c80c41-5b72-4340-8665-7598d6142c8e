import os
import sys
import time
from typing import Dict, Any, List, Optional, Tu<PERSON>
import uuid
from copy import deepcopy
from concurrent.futures import Thread<PERSON>oolExecutor
from sqlalchemy.orm import Session
from sqlalchemy import desc
import PIL
from PIL import Image
import torch
import threading
import io

# 添加必要的路径以导入LBM相关模块
LBM_PATH = os.path.abspath("/data/lsc/IClight/LBM/examples/inference")
if LBM_PATH not in sys.path:
    sys.path.append(LBM_PATH)

# 导入原始gradio_demo.py中的模型和处理函数
# 使用绝对路径导入LBM项目中的utils模块
sys.path.insert(0, "/data/lsc/IClight/LBM")
from examples.inference.utils import extract_object
from lbm.inference import get_model

# 直接导入
from models.models import ImageData, UserImageLike, Comment, UserAccount, ImageInfo
from config import OUTPUT_DIR, PAGE_SIZE, POINT_COST_PER_TASK, POINT_REWARD_FOR_SHARING
from utils.redis_utils import clear_home_page_cache, get_home_page_cache, set_home_page_cache
from services.user_service import update_user_balance, check_balance_sufficient

# 设置正确的数据类型
torch_dtype = torch.bfloat16 if torch.cuda.is_available() and hasattr(torch, 'bfloat16') else torch.float16
device = "cuda" if torch.cuda.is_available() else "cpu"

# 初始化模型（与原始代码保持一致）
if not os.path.exists(os.path.join(LBM_PATH, "ckpts", "relighting")):
    print(f"Downloading relighting LBM model from HF hub...")
    model = get_model(
        f"jasperai/LBM_relighting",
        save_dir=os.path.join(LBM_PATH, "ckpts", "relighting"),
        torch_dtype=torch_dtype,
        device=device,
    )
else:
    model_dir = os.path.join(LBM_PATH, "ckpts", "relighting")
    print(f"Loading relighting LBM model from local...")
    model = get_model(
        os.path.join(LBM_PATH, "ckpts", "relighting"),
        torch_dtype=torch_dtype,
        device=device,
    )

# 导入原始代码中的其他必要组件
try:
    from transformers import AutoModelForImageSegmentation
    birefnet = AutoModelForImageSegmentation.from_pretrained(
        "ZhengPeng7/BiRefNet", trust_remote_code=True
    ).to(device)
except Exception as e:
    print(f"Error loading BiRefNet model: {str(e)}")
    birefnet = None



# 线程池
executor = ThreadPoolExecutor(max_workers=1)  # 图像处理仍保持单线程
task_status: Dict[str, Dict] = {}
# 添加任务时间戳，用于清理过期任务
task_timestamps: Dict[str, float] = {}
# 任务过期时间（5分钟）
TASK_EXPIRATION_TIME = 300  # 秒

# 添加清理任务的线程
def cleanup_expired_tasks():
    while True:
        try:
            current_time = time.time()
            expired_tasks = []
            
            # 查找过期任务
            for task_id, timestamp in list(task_timestamps.items()):
                if current_time - timestamp > TASK_EXPIRATION_TIME:
                    expired_tasks.append(task_id)
            
            # 删除过期任务
            for task_id in expired_tasks:
                if task_id in task_status:
                    del task_status[task_id]
                if task_id in task_timestamps:
                    del task_timestamps[task_id]
            
            # 每30秒检查一次
            time.sleep(30)
        except Exception as e:
            print(f"清理过期任务时出错: {str(e)}")
            time.sleep(30)  # 出错后继续尝试

# 启动清理线程
cleanup_thread = threading.Thread(target=cleanup_expired_tasks, daemon=True)
cleanup_thread.start()

def paste_rotated_fg(bg_image, fg_img, X, Y, angle):
    fg_x,fg_y=fg_img.size
    X=X+fg_x//2
    Y=Y+fg_y//2
    # 创建与背景图同尺寸的空白图（RGB模式）
    bg_width, bg_height = bg_image.size
    fg_image = Image.new('RGB', (bg_width, bg_height), (0, 0, 0))
    
    # 旋转前景图（启用expand避免裁剪）[1,4](@ref)
    rotated_fg = fg_img.rotate(angle, expand=True)
    rotated_width, rotated_height = rotated_fg.size
    
    # 计算粘贴位置：使旋转后图像的中心点位于(X,Y)[6](@ref)
    paste_x = X - rotated_width // 2
    paste_y = Y - rotated_height // 2
    # 粘贴旋转后的前景图
    fg_image.paste(rotated_fg, (int(paste_x), int(paste_y)))
    return fg_image

def paste_rotated_mask(bg_image, fg_img, X, Y, angle):
    fg_x,fg_y=fg_img.size
    X=X+fg_x//2
    Y=Y+fg_y//2
    # 创建与背景图同尺寸的空白图（RGB模式）
    bg_width, bg_height = bg_image.size
    fg_image = Image.new('L', (bg_width, bg_height), 0)
    
    # 旋转前景图（启用expand避免裁剪）[1,4](@ref)
    rotated_fg = fg_img.rotate(angle, expand=True)
    rotated_width, rotated_height = rotated_fg.size
    
    # 计算粘贴位置：使旋转后图像的中心点位于(X,Y)[6](@ref)
    paste_x = X - rotated_width // 2
    paste_y = Y - rotated_height // 2
    # 粘贴旋转后的前景图
    fg_image.paste(rotated_fg, (int(paste_x), int(paste_y)))
    return fg_image




def generate_low_res_image(img: Image) -> Image:
    try:
        # 获取原始尺寸
        width, height = img.size
        
        # 计算新尺寸，保持宽高比
        # 宽度最大4000，高度最大4000
        if width > height:
            # 宽图
            if width > 4000:
                new_width = 4000
                new_height = int(height * (4000 / width))
            else:
                new_width = width
                new_height = height
        else:
            # 高图
            if height > 4000:
                new_height = 4000
                new_width = int(width * (4000 / height))
            else:
                new_width = width
                new_height = height
        resized_img = img.resize((new_width, new_height), Image.LANCZOS)
        
        return resized_img
        
    except Exception as e:
        print(f"创建低分辨率图片失败: {str(e)}")
        # 可以选择返回原始图片或抛出异常
        return img
def process_image(
    fg_image: PIL.Image.Image,
    bg_image: PIL.Image.Image,
    task_id: str,
    X: int = 0,
    Y: int = 0,
    Width: int = 0,
    Height: int = 0,
    Angle: int = 0,
    num_sampling_steps: int = 4
) -> bool:
    """
    处理图像（与原始evaluate函数类似）
    """
    try:
        # 提取前景对象
        _, fg_mask = extract_object(birefnet, deepcopy(fg_image))
        
      
        fg_img_resized = fg_image.resize((Width, Height))
      
        fg_image_new = paste_rotated_fg(bg_image,fg_img_resized,X,Y,-Angle)
       
       
        fg_mask_resized=fg_mask.resize((Width, Height))
        # 将resize后的mask放置到指定位置
        fg_mask_new = paste_rotated_mask(bg_image,fg_mask_resized,X,Y,-Angle)

        # print("X",X,"Y",Y,"W",Width,"H",Height)
        # fg_image.save("/data/lsc/IClight/LBM/examples/inference/ios_service/test/fg.jpg")
        # fg_img_resized.save("/data/lsc/IClight/LBM/examples/inference/ios_service/test/fg_h.jpg")
        # bg_image.save("/data/lsc/IClight/LBM/examples/inference/ios_service/test/bg.jpg")
        # fg_mask_new.save("/data/lsc/IClight/LBM/examples/inference/ios_service/test/mk_h.jpg")
        # fg_mask.save("/data/lsc/IClight/LBM/examples/inference/ios_service/test/mk.jpg")

        fg_image_new = generate_low_res_image(fg_image_new)
        bg_image = generate_low_res_image(bg_image)
        fg_mask_new = generate_low_res_image(fg_mask_new)

        # 合成图像
        img_pasted = Image.composite(fg_image_new, bg_image, fg_mask_new)
        
        # # 获取宽高比
        # ori_h_bg, ori_w_bg = fg_image_new.size
        
        # 转换为张量并处理
        from torchvision.transforms import ToTensor, ToPILImage
        
        img_pasted_tensor = ToTensor()(img_pasted).unsqueeze(0) * 2 - 1
        batch = {
            "source_image": img_pasted_tensor.to(device).to(torch_dtype),
        }
        
        # 处理图像
        z_source = model.vae.encode(batch[model.source_key])
        output_image = model.sample(
            z=z_source,
            num_steps=num_sampling_steps,
            conditioner_inputs=batch,
            max_samples=1,
        ).clamp(-1, 1)
        
        # 转换回PIL图像
        output_image = (output_image[0].float().cpu() + 1) / 2
        output_image = ToPILImage()(output_image)
        
        # # 调整回原始大小
        # output_image = output_image.resize((ori_h_bg, ori_w_bg))
        
        # 创建目录结构
        output_dir = os.path.join(OUTPUT_DIR, "output")
        bg_dir = os.path.join(OUTPUT_DIR, "bg")
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(bg_dir, exist_ok=True)
        
        # 保存结果到新路径
        output_path = os.path.join(output_dir, f"{task_id}.webp")
        bg_path = os.path.join(bg_dir, f"{task_id}.webp")
        #将用户上传的背景图存放到模版目录
        template_bg = os.path.join(OUTPUT_DIR, "template", "user")
        template_bg_path = os.path.join(template_bg, f"{task_id}.webp")
        bg_image.save(template_bg_path, 'WEBP', quality=85)
        #output_image.save(output_path, 'JPEG', quality=85)
        output_image.save(output_path, 'WEBP', quality=85)
        bg_image.save(bg_path, 'WEBP', quality=85)
        #bg_image.save(bg_path, 'WEBP', lossless=True, quality=85)
        
        # 更新任务状态
        task_status[task_id]["status"] = "completed"
        task_status[task_id]["output_path"] = output_path
        task_status[task_id]["bg_path"] = bg_path
        
        # 更新任务时间戳（完成时更新）
        task_timestamps[task_id] = time.time()
        
        return True
    except Exception as e:
        print(f"Task {task_id} failed: {str(e)}")
        task_status[task_id]["status"] = "failed"
        task_status[task_id]["error"] = str(e)
        
        # 更新任务时间戳（失败时更新）
        task_timestamps[task_id] = time.time()
        
        return False

def submit_task(
    db: Session,
    user_id: int,
    fg_image: PIL.Image.Image,
    bg_image: PIL.Image.Image,
    X: int,
    Y: int,
    Width: int,
    Height: int,
    Angle: int
) -> Dict[str, Any]:
    """
    提交图像处理任务
    """
    # 检查用户积分是否足够
    balance_check = check_balance_sufficient(db, user_id, POINT_COST_PER_TASK)
    if not balance_check["success"]:
        return balance_check
    
    # 扣除积分
    update_user_balance(db, user_id, -POINT_COST_PER_TASK)
    
    # 生成任务ID
    task_id = str(uuid.uuid4())
    
    # 初始化任务状态
    task_status[task_id] = {
        "status": "processing",
        "user_id": user_id,
        "output_path": None,
        "bg_path": None
    }
    
    # 记录任务创建时间
    task_timestamps[task_id] = time.time()
    
    # 提交任务到线程池
    executor.submit(
        process_image,
        fg_image, bg_image, task_id,
        X, Y, Width, Height, Angle
    )
    
    return {
        "success": True,
        "task_id": task_id,
        "status": "processing"
    }

def get_task_status(task_id: str) -> Dict[str, Any]:
    """
    获取任务状态
    """
    if task_id not in task_status:
        return {"success": False, "message": "任务不存在"}
    
    status = task_status[task_id]
    
    if status["status"] == "completed":
        # 获取相对路径
        output_path = status["output_path"]
        bg_path = status["bg_path"]
        
        # 转换为相对路径
        rel_output_path = os.path.join("output", os.path.basename(output_path))
        rel_bg_path = os.path.join("bg", os.path.basename(bg_path))
        
        return {
            "success": True,
            "status": "completed",
            "output_path": rel_output_path,
            "bg_path": rel_bg_path
        }
    elif status["status"] == "failed":
        return {
            "success": False,
            "status": "failed",
            "message": status.get("error", "处理失败")
        }
    else:
        return {
            "success": True,
            "status": "processing"
        }

def save_image_to_database(
    db: Session,
    user_id: int,
    task_id: str,
    save_to_public: bool,
    title: str = None,
    content: str = None,
    category_id: int = 4
) -> Dict[str, Any]:
    """
    将处理好的图片保存到数据库
    """

    filename = f"{task_id}.webp"
    output_path = os.path.join(OUTPUT_DIR, "output", filename)
    
    # 检查文件是否存在
    if not os.path.exists(output_path):
        return {"success": False, "message": "找不到任务对应的输出文件"}
    
    # 如果用户选择保存到公共数据库
    if save_to_public:
        
        # 创建低分辨率版本的图片
        low_res_dir = os.path.join(OUTPUT_DIR, "low")
        os.makedirs(low_res_dir, exist_ok=True)
        
        low_res_path = os.path.join(low_res_dir, filename)
        
        # 打开原图并创建低分辨率版本
        try:
            with Image.open(output_path) as img:
                # 获取原始尺寸
                width, height = img.size

                # 等比例缩放图片的宽度为400
                new_width = 400
                new_height = int(height * (400 / width))

                # 调整大小
                resized_img = img.resize((new_width, new_height), Image.LANCZOS)

                # 保存低分辨率图片
                resized_img.save(low_res_path, 'WEBP', quality=75)
        except Exception as e:
            print(f"创建低分辨率图片失败: {str(e)}")
            return {"success": True, "message": "图片未保存到公共数据库"}
            # 如果失败，继续保存原图信息
        
        # 创建图片数据记录，默认审核状态为0（审核中）
        new_image = ImageData(
            user_id=user_id,
            storage_path=filename,
            like_count=0,
            audit_status=1,  # 默认为审核通过状态
            category_id=category_id
        )
        
        db.add(new_image)
        db.commit()
        db.refresh(new_image)
        
        # 如果提供了标题和内容，创建图片信息记录
        if title is not None and content is not None:
            image_info = ImageInfo(
                image_id=new_image.image_id,
                title=title,
                content=content
            )
            db.add(image_info)
            db.commit()
        
        # 创建审核记录文件
        audit_dir = os.path.join(OUTPUT_DIR, "audit")
        os.makedirs(audit_dir, exist_ok=True)
        
        audit_filename = f"audit_{new_image.image_id}.txt"
        audit_path = os.path.join(audit_dir, audit_filename)
        
        try:
            with open(audit_path, 'w', encoding='utf-8') as f:
                f.write(f"图片ID: {new_image.image_id}\n")
                f.write(f"用户ID: {user_id}\n")
                f.write(f"保存时间: {new_image.created_at}\n")
                f.write(f"图片路径: {filename}\n")
                if title:
                    f.write(f"标题: {title}\n")
                if content:
                    f.write(f"内容: {content}\n")
        except Exception as e:
            print(f"创建审核记录文件失败: {str(e)}")
            # 继续执行，不影响主流程
        
        # 奖励用户积分
        update_user_balance(db, user_id, POINT_REWARD_FOR_SHARING)
        
        # 清除首页缓存
        #clear_home_page_cache()
        
        return {
            "success": True,
            "message": "图片已保存到公共数据库",
            "image_id": new_image.image_id,
            "reward": POINT_REWARD_FOR_SHARING
        }
    
    return {"success": True, "message": "图片未保存到公共数据库"}

def get_home_page_images(db: Session, page: int = 1, user_id: int = None, sort_by: str = "rank", category_id: int = -1) -> Dict[str, Any]:
    """
    获取首页图片列表
    sort_by: 'rank' - 按赞数排序, 'time' - 按时间排序
    category_id: -1=全部, 0=风景, 1=动漫, 2=古风, 3=二次元, 4=其他
    """
    # 计算偏移量
    skip = (page - 1) * PAGE_SIZE

    # 获取缓存键，包含排序方式和类别
    cache_key = f"home_page_{page}_{sort_by}_{category_id}"
    
    # 尝试从缓存获取
    cached_data = get_home_page_cache(cache_key)
    if cached_data:
        # 即使从缓存获取，也检查一下图片是否存在
        result = []
        for img_data in cached_data:
            # 检查图片是否存在于数据库并且审核状态为通过
            image = db.query(ImageData).filter(
                ImageData.image_id == img_data.get("image_id"),
                ImageData.audit_status == 1  # 只返回审核通过的图片
            ).first()
            
            if image:
                # 如果提供了用户ID，检查用户是否点赞过该图片
                if user_id:
                    like_record = db.query(UserImageLike).filter(
                        UserImageLike.user_id == user_id,
                        UserImageLike.image_id == img_data.get("image_id"),
                        UserImageLike.liked_status == 1
                    ).first()
                    img_data["liked"] = like_record is not None
                else:
                    img_data["liked"] = False
                
                result.append(img_data)
                
                # 确保只返回PAGE_SIZE数量的图片
                if len(result) >= PAGE_SIZE:
                    break
        
        # 如果缓存数据有过滤，更新缓存
        if len(result) != len(cached_data):
            set_home_page_cache(cache_key, result)
            
        return {
            "success": True,
            "images": result,
            "page": page,
            "page_size": PAGE_SIZE,
            "cached": True,
            "sort_by": sort_by,
            "category_id": category_id
        }
    
    # 构建基础查询条件
    query = db.query(ImageData).filter(ImageData.audit_status == 1)

    # 如果指定了类别，添加类别筛选条件
    if category_id != -1:
        query = query.filter(ImageData.category_id == category_id)

    # 根据排序选项选择排序方式
    if sort_by == "time":
        # 按时间排序
        images = query.order_by(desc(ImageData.created_at))\
                     .offset(skip).limit(PAGE_SIZE).all()
    else:
        # 默认按点赞数和时间排序
        images = query.order_by(desc(ImageData.like_count), desc(ImageData.created_at))\
                     .offset(skip).limit(PAGE_SIZE).all()
    
    # 如果没有更多图片，返回空列表
    if not images:
        return {
            "success": True,
            "images": [],
            "page": page,
            "page_size": PAGE_SIZE,
            "cached": False,
            "sort_by": sort_by,
            "category_id": category_id
        }
    
    # 转换为字典
    result = []
    for image in images:
        image_dict = image.to_dict()
        
        # 如果提供了用户ID，检查用户是否点赞过该图片
        if user_id:
            like_record = db.query(UserImageLike).filter(
                UserImageLike.user_id == user_id,
                UserImageLike.image_id == image.image_id,
                UserImageLike.liked_status == 1
            ).first()
            image_dict["liked"] = like_record is not None
        else:
            image_dict["liked"] = False
        
        result.append(image_dict)
    
    # 缓存结果
    set_home_page_cache(cache_key, result)
    
    return {
        "success": True,
        "images": result,
        "page": page,
        "page_size": PAGE_SIZE,
        "cached": False,
        "sort_by": sort_by,
        "category_id": category_id
    }

def like_image(db: Session, image_id: int, user_id: int) -> Dict[str, Any]:
    """
    给图片点赞或取消点赞
    """
    # 查找图片
    image = db.query(ImageData).filter(ImageData.image_id == image_id).first()
    
    if not image:
        return {"success": False, "message": "图片不存在"}
    
    # 查找用户点赞记录
    like_record = db.query(UserImageLike).filter(
        UserImageLike.user_id == user_id,
        UserImageLike.image_id == image_id
    ).first()
    
    # 如果没有点赞记录或记录状态为0（取消点赞），则进行点赞操作
    if not like_record:
        # 创建新的点赞记录
        new_like = UserImageLike(
            user_id=user_id,
            image_id=image_id,
            liked_status=1
        )
        db.add(new_like)
        
        # 更新图片点赞数
        image.like_count += 1
        db.commit()
        
        # 清除首页缓存
        clear_home_page_cache()
        
        return {
            "success": True,
            "image_id": image.image_id,
            "like_count": image.like_count,
            "liked": True
        }
    elif like_record.liked_status == 0:
        # 更新点赞记录状态
        like_record.liked_status = 1
        
        # 更新图片点赞数
        image.like_count += 1
        db.commit()
        
        # 清除首页缓存
        clear_home_page_cache()
        
        return {
            "success": True,
            "image_id": image.image_id,
            "like_count": image.like_count,
            "liked": True
        }
    else:
        # 已经点赞，执行取消点赞操作
        like_record.liked_status = 0
        
        # 更新图片点赞数（确保不会小于0）
        image.like_count = max(0, image.like_count - 1)
        db.commit()
        
        # 清除首页缓存
        clear_home_page_cache()
        
        return {
            "success": True,
            "image_id": image.image_id,
            "like_count": image.like_count,
            "liked": False
        }

def get_image_detail(db: Session, image_id: int, user_id: int = None) -> Dict[str, Any]:
    """
    获取图片详情
    """
    # 查找图片
    image = db.query(ImageData).filter(ImageData.image_id == image_id).first()
    
    if not image:
        return {"success": False, "message": "图片不存在"}
    
    # 查找用户
    user = db.query(UserAccount).filter(UserAccount.user_id == image.user_id).first()
    
    if not user:
        return {"success": False, "message": "图片作者不存在"}
    
    # 判断当前用户是否已点赞该图片
    liked = False
    if user_id:
        like_record = db.query(UserImageLike).filter(
            UserImageLike.user_id == user_id,
            UserImageLike.image_id == image_id,
            UserImageLike.liked_status == 1
        ).first()
        liked = like_record is not None
    
    # 查找前景图
    foreground_path = os.path.join("bg", image.storage_path)
    
    # 查找图片信息（标题和内容）
    image_info = db.query(ImageInfo).filter(ImageInfo.image_id == image_id).first()
    
    # 构建图片详情
    image_dict = image.to_dict()
    image_dict["liked"] = liked
    
    # 如果有图片信息，添加到详情中
    if image_info:
        image_dict["title"] = image_info.title
        image_dict["content"] = image_info.content
    
    # 构建响应
    return {
        "success": True,
        "image": image_dict,
        "foreground": {
            "foreground_id": None,  # 不再使用
            "background_id": image_id,
            "storage_path": foreground_path
        }
    }

def delete_image(db: Session, user_id: int, image_id: int) -> Dict[str, Any]:
    """
    删除用户上传的图片
    """
    # 查找图片
    image = db.query(ImageData).filter(ImageData.image_id == image_id).first()
    
    if not image:
        return {"success": False, "message": "图片不存在"}
    
    # 检查图片是否属于该用户
    if image.user_id != user_id:
        return {"success": False, "message": "无权删除此图片"}
    
    try:
        # 从存储路径中提取文件名
        storage_path = image.storage_path  # 例如: "output/task_id.webp"
        
        # 提取文件名，无论存储路径是绝对路径还是相对路径
        if "/" in storage_path:
            filename = storage_path.split("/")[-1]  # 例如: "task_id.webp"
        else:
            filename = storage_path
            
        # 提取任务ID
        task_id = os.path.splitext(filename)[0]  # 例如: "task_id"
        
        
        # 删除物理文件 - 输出图片
        output_paths = [
            os.path.join(OUTPUT_DIR, "output", filename),  # 新路径
            os.path.join(OUTPUT_DIR, storage_path),        # 存储的路径
            os.path.join(OUTPUT_DIR, filename)             # 旧路径
        ]
        
        for path in output_paths:
            if os.path.exists(path):
                os.remove(path)
        
        # 删除物理文件 - 背景图
        bg_path = os.path.join(OUTPUT_DIR, "bg", filename)
        if os.path.exists(bg_path):
            os.remove(bg_path)
        
        # 删除低分辨率图片
        low_res_path = os.path.join(OUTPUT_DIR, "low", filename)
        if os.path.exists(low_res_path):
            os.remove(low_res_path)
        
        # 从数据库中删除记录
        db.delete(image)
        db.commit()
        
        # 清除首页缓存
        clear_home_page_cache()
        
        return {"success": True, "message": "图片已删除"}
    except Exception as e:
        db.rollback()
        print(f"删除图片失败: {str(e)}")
        return {"success": False, "message": f"删除图片失败: {str(e)}"}

def get_user_liked_images(db: Session, user_id: int, skip: int = 0, limit: int = 20) -> Dict[str, Any]:
    """
    获取用户喜欢的图片列表
    """
    # 查询用户点赞的图片（状态为1的记录）
    liked_images = db.query(ImageData)\
        .join(UserImageLike, UserImageLike.image_id == ImageData.image_id)\
        .filter(UserImageLike.user_id == user_id, UserImageLike.liked_status == 1)\
        .order_by(desc(ImageData.created_at))\
        .offset(skip).limit(limit).all()
    
    # 如果没有更多图片，返回空列表
    if not liked_images:
        return {
            "success": True,
            "user_id": user_id,
            "images": [],
            "total": 0,
            "skip": skip,
            "limit": limit
        }
    
    # 转换为字典
    result = []
    for image in liked_images:
        image_dict = image.to_dict()
        # 标记为已点赞
        image_dict["liked"] = True
        result.append(image_dict)
    
    # 获取总数
    total = db.query(UserImageLike)\
        .filter(UserImageLike.user_id == user_id, UserImageLike.liked_status == 1)\
        .count()
    
    return {
        "success": True,
        "user_id": user_id,
        "images": result,
        "total": total,
        "skip": skip,
        "limit": limit
    }

# 添加评论相关的函数
def add_comment(db, image_id, user_id, content, parent_comment_id=None):
    """
    添加评论
    """
    # 检查图片是否存在
    image = db.query(ImageData).filter(ImageData.image_id == image_id).first()
    if not image:
        return {
            "success": False,
            "message": "图片不存在"
        }
    
    # 检查用户是否存在
    user = db.query(UserAccount).filter(UserAccount.user_id == user_id).first()
    if not user:
        return {
            "success": False,
            "message": "用户不存在"
        }
    
    # 如果有父评论ID，检查父评论是否存在
    if parent_comment_id:
        parent_comment = db.query(Comment).filter(Comment.comment_id == parent_comment_id).first()
        if not parent_comment:
            return {
                "success": False,
                "message": "回复的评论不存在"
            }
        
        # 检查父评论是否属于同一张图片
        if parent_comment.image_id != image_id:
            return {
                "success": False,
                "message": "回复的评论不属于该图片"
            }
    
    # 创建新评论
    new_comment = Comment(
        image_id=image_id,
        user_id=user_id,
        parent_comment_id=parent_comment_id,
        content=content
    )
    
    try:
        db.add(new_comment)
        db.commit()
        db.refresh(new_comment)
        
        # 返回评论信息
        return {
            "success": True,
            "message": "评论成功",
            "comment": new_comment.to_dict()
        }
    except Exception as e:
        db.rollback()
        return {
            "success": False,
            "message": f"评论失败: {str(e)}"
        }

def get_comments(db, image_id, user_id=None):
    """
    获取图片的评论列表，按照创建时间降序排列
    支持任意级别的评论展示
    """
    # 检查图片是否存在
    image = db.query(ImageData).filter(ImageData.image_id == image_id).first()
    if not image:
        return {
            "success": False,
            "message": "图片不存在"
        }
    
    try:
        # 获取所有评论，按照创建时间降序排序
        comments = db.query(Comment).filter(
            Comment.image_id == image_id,
            Comment.parent_comment_id == None  # 只获取一级评论
        ).order_by(Comment.created_at.desc()).all()
        
        # 获取图片下的所有评论（包括一级、二级、三级等所有评论）
        all_comments = db.query(Comment).filter(
            Comment.image_id == image_id
        ).all()
        
        # 创建评论ID到评论对象的映射，便于快速查找
        comment_map = {comment.comment_id: comment for comment in all_comments}
        
        # 获取每个一级评论的所有回复
        result_comments = []
        for comment in comments:
            comment_dict = comment.to_dict()
            
            # 获取所有与此一级评论相关的回复
            all_replies = []
            
            # 查找所有直接回复一级评论的评论（二级评论）
            direct_replies = [c for c in all_comments if c.parent_comment_id == comment.comment_id]
            
            # 按创建时间升序排序
            direct_replies.sort(key=lambda x: x.created_at)
            
            # 将所有直接回复添加到结果中
            for reply in direct_replies:
                all_replies.append(reply.to_dict())
                
                # 递归查找所有回复此评论的评论
                def find_nested_replies(parent_comment_id):
                    nested_replies = [c for c in all_comments if c.parent_comment_id == parent_comment_id]
                    nested_replies.sort(key=lambda x: x.created_at)
                    
                    for nested_reply in nested_replies:
                        all_replies.append(nested_reply.to_dict())
                        # 继续查找回复此评论的评论
                        find_nested_replies(nested_reply.comment_id)
                
                # 查找所有回复此二级评论的评论
                find_nested_replies(reply.comment_id)
            
            # 添加所有回复到一级评论中
            comment_dict["replies"] = all_replies
            result_comments.append(comment_dict)
        
        return {
            "success": True,
            "image_id": image_id,
            "comments": result_comments
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"获取评论失败: {str(e)}"
        }

def delete_comment(db, comment_id, user_id):
    """
    删除评论（只能删除自己的评论）
    同时递归删除该评论下的所有子评论
    """
    # 检查评论是否存在
    comment = db.query(Comment).filter(Comment.comment_id == comment_id).first()
    if not comment:
        return {
            "success": False,
            "message": "评论不存在"
        }
    
    # 检查是否是评论的作者
    if comment.user_id != user_id:
        return {
            "success": False,
            "message": "只能删除自己的评论"
        }
    
    try:
        # 递归查找并删除所有子评论
        def delete_child_comments(parent_id):
            # 查找所有回复此评论的评论
            child_comments = db.query(Comment).filter(Comment.parent_comment_id == parent_id).all()
            
            for child in child_comments:
                # 递归删除子评论的子评论
                delete_child_comments(child.comment_id)
                # 删除子评论
                db.delete(child)
        
        # 开始递归删除
        delete_child_comments(comment_id)
        
        # 删除主评论
        db.delete(comment)
        db.commit()
        
        return {
            "success": True,
            "message": "评论已删除"
        }
    except Exception as e:
        db.rollback()
        return {
            "success": False,
            "message": f"删除评论失败: {str(e)}"
        }

# 添加模板图片相关函数
def get_template_images(page: int = 1, limit: int = 20) -> Dict[str, Any]:
    """
    获取模板图片列表
    
    参数:
        page: 页码，从1开始
        limit: 每页记录数，默认20
    
    返回:
        Dict: 包含模板图片信息的字典
    """
    try:
        # 构建模板图片目录路径
        template_dir = os.path.join(OUTPUT_DIR, "template", "low")
        template_high_dir = os.path.join(OUTPUT_DIR, "template", "high")
        
        # 确保目录存在
        os.makedirs(template_dir, exist_ok=True)
        os.makedirs(template_high_dir, exist_ok=True)
        
        # 检查目录是否为空
        if not os.path.exists(template_dir) or not os.listdir(template_dir):
            print(f"模板目录不存在或为空: {template_dir}")
            # 创建空数组返回
            return {
                "success": True,
                "message": "模板目录为空",
                "templates": [],
                "total": 0,
                "page": page,
                "limit": limit,
                "has_more": False
            }
        
        # 获取所有图片文件
        template_files = [f for f in os.listdir(template_dir) 
                         if f.lower().endswith(('.jpg', '.jpeg', '.png', '.webp'))]
        
        # 按名称排序
        template_files.sort()
        
        # 计算分页
        total = len(template_files)
        skip = (page - 1) * limit
        end = min(skip + limit, total)
        
        # 提取当前页的文件
        current_page_files = template_files[skip:end]
        
        # 构建返回数据
        templates = []
        for filename in current_page_files:
            templates.append({
                "filename": filename,
                "path": f"template/low/{filename}"
            })
        
        return {
            "success": True,
            "templates": templates,
            "total": total,
            "page": page,
            "limit": limit,
            "has_more": end < total
        }
    
    except Exception as e:
        print(f"获取模板图片列表失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取模板图片列表失败: {str(e)}",
            "templates": []
        } 

def get_pending_audit_images(db: Session, user_id: int) -> Dict[str, Any]:
    """
    获取用户所有审核中的图片状态
    """
    # 查询用户所有审核中的图片（audit_status=0）
    pending_images = db.query(ImageData).filter(
        ImageData.user_id == user_id,
        ImageData.audit_status == 0
    ).all()
    
    if not pending_images:
        return {
            "success": True,
            "user_id": user_id,
            "pending_images": [],
            "count": 0
        }
    
    # 转换为字典
    result = []
    for image in pending_images:
        image_dict = {
            "image_id": image.image_id,
            "storage_path": image.storage_path,
            "created_at": image.created_at.isoformat() if image.created_at else None,
            "audit_status": image.audit_status
        }
        result.append(image_dict)
    
    return {
        "success": True,
        "user_id": user_id,
        "pending_images": result,
        "count": len(result)
    } 

def update_task_image(task_id: str, image_data: bytes) -> Dict[str, Any]:
    """
    更新任务的输出图像，包括高分辨率和低分辨率版本。
    """
    try:
        filename = f"{task_id}.webp"
        output_path = os.path.join(OUTPUT_DIR, "output", filename)
        low_res_path = os.path.join(OUTPUT_DIR, "low", filename)

        # 检查原始文件是否存在
        if not os.path.exists(output_path):
            return {"success": False, "message": "原始输出文件不存在"}

        # 将字节数据转换为Pillow Image对象
        with Image.open(io.BytesIO(image_data)) as img:
            # 1. 替换高分辨率图像
            img.save(output_path, 'WEBP', quality=85)

            # 2. 创建并替换低分辨率图像
            # 获取原始尺寸
            width, height = img.size

            # 等比例缩放图片的宽度为400
            new_width = 400
            new_height = int(height * (400 / width))

            # 调整大小并保存
            resized_img = img.resize((new_width, new_height), Image.LANCZOS)
            resized_img.save(low_res_path, 'WEBP', quality=75)

        return {"success": True, "message": "图像已成功更新"}

    except Exception as e:
        print(f"更新任务 {task_id} 的图像失败: {str(e)}")
        return {"success": False, "message": f"服务器内部错误: {str(e)}"} 