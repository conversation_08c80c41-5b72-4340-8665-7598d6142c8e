import os

# 数据库配置
DB_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "root",
    "database": "lbm_ic",
    "charset": "utf8mb4"
}

# Redis配置
REDIS_CONFIG = {
    "host": "localhost",
    "port": 6379,
    "db": 0,
    "password": "13880584744"
}

# 腾讯云短信配置
SMS_CONFIG = {
    "app_id": "1400937987",
    "secret_id": "AKIDoX5RcO4YptnW2Ll6GCvgXebO3XilcGgt",
    "secret_key": "85oJck02rX7arwoSvGXoALK9Vil3B6rL",
    "sign_name": "数联天工科技",
    "template_id": "2268965"
}

# 令牌配置
TOKEN_CONFIG = {
    "secret_key": "your_secret_key",  # 在生产环境中应使用安全的密钥
    "algorithm": "HS256",
    "expire_days": 30
}

# 图片存储路径
OUTPUT_DIR = "/data5/IC-Light/output/IC"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 分页配置
PAGE_SIZE = 20

# 创建必要的子目录
os.makedirs(os.path.join(OUTPUT_DIR, "output"), exist_ok=True)
os.makedirs(os.path.join(OUTPUT_DIR, "bg"), exist_ok=True)
os.makedirs(os.path.join(OUTPUT_DIR, "low"), exist_ok=True)

# 积分配置
POINT_COST_PER_TASK = 2  # 每次任务消耗积分
POINT_REWARD_FOR_SHARING = 1  # 分享图片奖励积分
INITIAL_BALANCE = 100  # 新用户初始积分 