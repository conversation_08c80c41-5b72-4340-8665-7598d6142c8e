#!/usr/bin/env python
"""
主入口文件 - 直接在当前目录运行应用
"""
import os
import sys
import uvicorn

# 设置环境变量
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../../"))
sys.path.insert(0, project_root)

if __name__ == "__main__":
    print(f"当前目录: {current_dir}")
    print(f"项目根目录: {project_root}")
    print(f"系统路径: {sys.path}")
    
    # 运行应用
    uvicorn.run(
        "examples.inference.ios_service.app:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    ) 