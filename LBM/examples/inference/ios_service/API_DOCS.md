后端服务启动脚本："/Users/<USER>/Desktop/sorealhuman/LBM/examples/inference/ios_service/run_service.sh",后端8000反向代理到了公网ip8888，sorealhuman.com域名指向公网ip。
# IC Light iOS API 文档

本文档描述了 IC Light iOS 应用的后端 API 接口。

## 基本信息

- 基础URL: `https://sorealhuman.com:8888`
- 所有响应格式均为 JSON
- 认证方式: 用户ID + Token

## 认证接口

### 发送验证码

**请求**:
- 方法: `POST`
- 路径: `/auth/send_code`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "phone": "13800138000"
  }
  ```

**响应**:
```json
{
  "success": true,
  "message": "验证码已发送"
}
```

### 验证验证码

**请求**:
- 方法: `POST`
- 路径: `/auth/verify_code`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "phone": "13800138000",
    "code": "123456"
  }
  ```

**响应**:
```json
{
  "success": true,
  "message": "验证码验证成功"
}
```

### 注册用户

**请求**:
- 方法: `POST`
- 路径: `/auth/register`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "phone": "13800138000",
    "username": "用户名",
    "avatar": "4-2"
  }
  ```

**响应**:
```json
{
  "success": true,
  "message": "注册成功",
  "user_id": 1,
  "username": "用户名",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 用户登录

**请求**:
- 方法: `POST`
- 路径: `/auth/login`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "phone": "13800138000"
  }
  ```

**响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "user_id": 1,
  "username": "用户名",
  "avatar": "4-2",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 验证令牌

**请求**:
- 方法: `POST`
- 路径: `/auth/validate_token`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "user_id": 1,
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
  ```

**响应**:
```json
{
  "valid": true,
  "user_id": 1,
  "username": "用户名"
}
```

### 注销账户

**请求**:
- 方法: `POST`
- 路径: `/auth/delete_account`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "user_id": 1
  }
  ```

**响应**:
```json
{
  "success": true,
  "message": "账户已成功注销"
}
```

## 用户接口

### 获取用户积分余额

**请求**:
- 方法: `GET`
- 路径: `/users/{user_id}/balance`

**响应**:
```json
{
  "success": true,
  "user_id": 1,
  "username": "用户名",
  "balance": 100
}
```

### 验证App内购买并更新用户余额

**请求**:
- 方法: `POST`
- 路径: `/users/verify_purchase`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "user_id": 1,
    "product_id": "com.nienie.nn10",
    "receipt_data": "收据数据"
  }
  ```

**响应**:
```json
{
  "success": true,
  "user_id": 1,
  "balance": 110,
  "product_id": "com.nienie.nn10",
  "coin_amount": 10,
  "message": "充值成功"
}
```

### 获取用户上传的图片

**请求**:
- 方法: `GET`
- 路径: `/users/{user_id}/images`
- 查询参数:
  - `skip`: 跳过的数量，默认为0
  - `limit`: 返回的数量，默认为20，最大为100

**响应**:
```json
{
  "success": true,
  "user_id": 1,
  "images": [
    {
      "image_id": 1,
      "user_id": 1,
      "username": "用户名",
      "storage_path": "12345_output.jpg",
      "like_count": 10,
      "created_at": "2023-08-10T15:30:45"
    }
  ],
  "total": 1,
  "skip": 0,
  "limit": 20
}
```

## 图片接口

### 提交图像处理任务

**请求**:
- 方法: `POST`
- 路径: `/images/submit_task`
- 内容类型: `multipart/form-data`
- 表单参数:
  - `user_id`: 用户ID (整数)
  - `fg_image`: 前景图文件
  - `bg_image`: 背景图文件
  - `X`: 边界框左上角X坐标 (整数)
  - `Y`: 边界框左上角Y坐标 (整数)
  - `Width`: 边界框宽度 (整数)
  - `Height`: 边界框高度 (整数)
  - `Angle`: 前景图旋转角度 (整数)

**响应**:
```json
{
  "success": true,
  "task_id": "d290f1ee-6c54-4b01-90e6-d701748f0851",
  "status": "processing"
}
```

### 获取任务状态

**请求**:
- 方法: `GET`
- 路径: `/images/task_status/{task_id}`

**响应**:
```json
{
  "success": true,
  "status": "completed",
  "output_path": "/data5/IC-Light/output/IC/d290f1ee-6c54-4b01-90e6-d701748f0851_output.jpg",
  "bg_path": "/data5/IC-Light/output/IC/d290f1ee-6c54-4b01-90e6-d701748f0851_bg.jpg"
}
```

### 保存图片到数据库

**请求**:
- 方法: `POST`
- 路径: `/images/save_image`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "user_id": 1,
    "task_id": "d290f1ee-6c54-4b01-90e6-d701748f0851",
    "save_to_public": true
  }
  ```

**响应**:
```json
{
  "success": true,
  "message": "图片已保存到公共数据库",
  "image_id": 1,
  "reward": 1
}
```

### 获取首页图片列表

**请求**:
- 方法: `GET`
- 路径: `/images/home`
- 查询参数:
  - `page`: 页码，默认为1

**响应**:
```json
{
  "success": true,
  "images": [
    {
      "image_id": 1,
      "user_id": 1,
      "username": "用户名",
      "avatar": "4-2",
      "storage_path": "12345_output.jpg",
      "like_count": 10,
      "created_at": "2023-08-10T15:30:45"
    }
  ],
  "page": 1,
  "page_size": 20,
  "cached": false
}
```

### 给图片点赞

**请求**:
- 方法: `POST`
- 路径: `/images/like`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "image_id": 1
  }
  ```

**响应**:
```json
{
  "success": true,
  "image_id": 1,
  "like_count": 11
}
```

### 获取图片详情

**请求**:
- 方法: `GET`
- 路径: `/images/{image_id}`

**响应**:
```json
{
  "success": true,
  "image": {
    "image_id": 1,
    "user_id": 1,
    "username": "用户名",
    "storage_path": "12345_output.jpg",
    "like_count": 11,
    "created_at": "2023-08-10T15:30:45"
  },
  "foreground": {
    "foreground_id": 1,
    "background_id": 1,
    "storage_path": "12345_bg.jpg"
  }
}
```

### 获取图片文件

**请求**:
- 方法: `GET`
- 路径: `/images/file/{filename}`

**响应**:
- 图片文件内容
- 内容类型: 根据图片类型设置，通常为 `image/jpeg` 或 `image/png`

## 反馈接口

### 提交内容反馈

**请求**:
- 方法: `POST`
- 路径: `/feedback`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "user_id": 1,
    "image_id": 123,
    "feedback_type": "广告",
    "image_path": "12345_output.jpg"
  }
  ```

**响应**:
```json
{
  "success": true,
  "message": "反馈已记录"
}
```

- `feedback_type`: 反馈类型，可选值包括：
  - `广告`: 标记为广告内容
  - `相似过多`: 标记为重复内容
  - `色情低俗`: 标记为不适当内容
  - `内容不适`: 标记为不适合的内容
  - `夸张博眼球`: 标记为标题党内容

注意：用户的不感兴趣内容（不喜欢的图片和作者）存储在客户端本地，不会发送到服务器。

## 私信接口

### 获取用户私信会话列表

**请求**:
- 方法: `GET`
- 路径: `/messages/conversations/{user_id}`
- 查询参数:
  - `skip`: 跳过的数量，默认为0
  - `limit`: 返回的数量，默认为20，最大为100

**响应**:
```json
{
  "success": true,
  "data": {
    "conversations": [
      {
        "conversation_id": "1_2",
        "user_id": 2,
        "username": "用户2",
        "avatar_url": "avatar2.jpg",
        "latest_message": "你好，最近怎么样？",
        "latest_message_time": "2023-08-15T10:30:45",
        "unread_count": 3
      }
    ],
    "total": 1
  }
}
```

### 获取会话消息列表

**请求**:
- 方法: `GET`
- 路径: `/messages/conversation/{conversation_id}/user/{user_id}`
- 查询参数:
  - `skip`: 跳过的数量，默认为0
  - `limit`: 返回的数量，默认为50，最大为200

**响应**:
```json
{
  "success": true,
  "data": {
    "messages": [
      {
        "message_id": 5,
        "sender_id": 1,
        "receiver_id": 2,
        "content": "你好！",
        "status": 2,
        "created_at": "2023-08-15T10:28:45",
        "is_self": true
      },
      {
        "message_id": 6,
        "sender_id": 2,
        "receiver_id": 1,
        "content": "你好，最近怎么样？",
        "status": 2,
        "created_at": "2023-08-15T10:30:45",
        "is_self": false
      }
    ],
    "other_user": {
      "user_id": 2,
      "username": "用户2",
      "avatar_url": "avatar2.jpg"
    },
    "total": 2
  }
}
```

### 发送私信消息

**请求**:
- 方法: `POST`
- 路径: `/messages/send`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "sender_id": 1,
    "receiver_id": 2,
    "content": "你好，很高兴认识你！"
  }
  ```

**响应**:
```json
{
  "success": true,
  "data": {
    "message_id": 7,
    "conversation_id": "1_2",
    "created_at": "2023-08-15T11:20:30"
  }
}
```

### 删除单条消息

**请求**:
- 方法: `DELETE`
- 路径: `/messages/message`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "user_id": 1,
    "message_id": 7
  }
  ```

**响应**:
```json
{
  "success": true,
  "message": "删除成功"
}
```

### 删除整个会话

**请求**:
- 方法: `DELETE`
- 路径: `/messages/conversation`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "user_id": 1,
    "conversation_id": "1_2"
  }
  ```

**响应**:
```json
{
  "success": true,
  "message": "会话删除成功"
}
```

### 注册设备令牌

**请求**:
- 方法: `POST`
- 路径: `/messages/register_device_token`
- 内容类型: `application/json`
- 请求体:
  ```json
  {
    "user_id": 1,
    "device_token": "64字符的设备令牌"
  }
  ```

**响应**:
```json
{
  "success": true,
  "message": "设备令牌注册成功"
}
```

注意：私信功能需要在客户端实现本地消息存储以减轻服务器负担。在消息推送方面，服务器会使用Apple的APNs服务向接收方发送推送通知。在客户端启动时，需要通过上面的注册设备令牌接口将设备令牌发送到服务器，以便服务器能够向客户端推送通知。



