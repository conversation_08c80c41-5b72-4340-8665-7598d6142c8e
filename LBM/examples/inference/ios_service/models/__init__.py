from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session

# 直接导入
from config import DB_CONFIG
from models.models import Base, UserAccount, ImageData, UserConversationMute

# 创建数据库连接
db_url = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"
engine = create_engine(db_url)

# 创建会话工厂
session_factory = sessionmaker(bind=engine)
SessionLocal = scoped_session(session_factory)

# 获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 导出模型
__all__ = ["Base", "engine", "SessionLocal", "get_db", "UserAccount", "ImageData", "ForegroundImage", "UserConversationMute"]