#!/usr/bin/env python3
"""
消息系统改进测试脚本
验证所有改进功能是否正常工作
"""

import asyncio
import websockets
import json
import requests
import time
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8888"
WS_URL = "ws://localhost:8888/ws/messages"

class MessageImprovementTester:
    def __init__(self):
        self.test_users = [1, 2]  # 测试用户ID
        self.websockets = {}
        self.received_messages = []
        self.user_statuses = {}
        
    async def connect_websocket(self, user_id):
        """连接WebSocket"""
        try:
            uri = f"{WS_URL}/{user_id}"
            websocket = await websockets.connect(uri)
            self.websockets[user_id] = websocket
            print(f"✅ 用户 {user_id} WebSocket连接成功")
            
            # 启动消息监听
            asyncio.create_task(self.listen_messages(user_id, websocket))
            
            return True
        except Exception as e:
            print(f"❌ 用户 {user_id} WebSocket连接失败: {e}")
            return False
    
    async def listen_messages(self, user_id, websocket):
        """监听WebSocket消息"""
        try:
            async for message in websocket:
                data = json.loads(message)
                self.received_messages.append({
                    "user_id": user_id,
                    "timestamp": datetime.now(),
                    "data": data
                })
                print(f"📨 用户 {user_id} 收到消息: {data.get('type', 'unknown')}")
                
        except websockets.exceptions.ConnectionClosed:
            print(f"🔌 用户 {user_id} WebSocket连接已关闭")
        except Exception as e:
            print(f"❌ 用户 {user_id} 消息监听出错: {e}")
    
    async def update_page_status(self, user_id, page, conversation_id=""):
        """更新用户页面状态"""
        if user_id in self.websockets:
            message = {
                "type": "page_status",
                "page": page,
                "conversation_id": conversation_id
            }
            await self.websockets[user_id].send(json.dumps(message))
            print(f"📱 用户 {user_id} 页面状态已更新: {page}")
            return True
        return False
    
    def send_message(self, sender_id, receiver_id, content):
        """发送消息"""
        url = f"{BASE_URL}/messages/send"
        data = {
            "sender_id": sender_id,
            "receiver_id": receiver_id,
            "content": content
        }
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    print(f"✅ 消息发送成功: {sender_id} -> {receiver_id}: {content}")
                    return True
                else:
                    print(f"❌ 消息发送失败: {result.get('message')}")
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 消息发送异常: {e}")
        
        return False
    
    def get_online_users(self):
        """获取在线用户列表"""
        try:
            response = requests.get(f"{BASE_URL}/messages/online_users")
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    return result.get("data", {}).get("online_users", [])
        except Exception as e:
            print(f"❌ 获取在线用户失败: {e}")
        return []
    
    def get_user_status(self, user_id):
        """获取用户状态"""
        try:
            response = requests.get(f"{BASE_URL}/messages/user_status/{user_id}")
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    return result.get("data", {})
        except Exception as e:
            print(f"❌ 获取用户状态失败: {e}")
        return {}
    
    async def test_page_status_management(self):
        """测试页面状态管理"""
        print("\n🧪 测试页面状态管理...")
        
        # 用户1进入聊天列表页
        await self.update_page_status(1, "ConversationListView")
        await asyncio.sleep(0.5)
        
        # 用户2进入聊天页
        await self.update_page_status(2, "ChatView", "1_2")
        await asyncio.sleep(0.5)
        
        # 检查状态
        status1 = self.get_user_status(1)
        status2 = self.get_user_status(2)
        
        print(f"📊 用户1状态: {status1}")
        print(f"📊 用户2状态: {status2}")
        
        # 验证状态
        if status1.get("page") == "ConversationListView":
            print("✅ 用户1页面状态正确")
        else:
            print("❌ 用户1页面状态错误")
            
        if status2.get("page") == "ChatView" and status2.get("conversation_id") == "1_2":
            print("✅ 用户2页面状态正确")
        else:
            print("❌ 用户2页面状态错误")
    
    async def test_smart_notification_logic(self):
        """测试智能通知逻辑"""
        print("\n🧪 测试智能通知逻辑...")
        
        # 场景1：用户在聊天页，应该收到WebSocket消息但不发送推送
        print("📱 场景1: 用户在聊天页")
        await self.update_page_status(2, "ChatView", "1_2")
        await asyncio.sleep(0.5)
        
        # 发送消息
        self.send_message(1, 2, "测试消息 - 用户在聊天页")
        await asyncio.sleep(2)
        
        # 场景2：用户在其他页面，应该发送推送通知
        print("📱 场景2: 用户在其他页面")
        await self.update_page_status(2, "ProfileView")
        await asyncio.sleep(0.5)
        
        # 发送消息
        self.send_message(1, 2, "测试消息 - 用户在其他页面")
        await asyncio.sleep(2)
        
        # 场景3：用户在会话列表页，应该收到WebSocket消息
        print("📱 场景3: 用户在会话列表页")
        await self.update_page_status(2, "ConversationListView")
        await asyncio.sleep(0.5)
        
        # 发送消息
        self.send_message(1, 2, "测试消息 - 用户在会话列表页")
        await asyncio.sleep(2)
    
    async def test_websocket_real_time_updates(self):
        """测试WebSocket实时更新"""
        print("\n🧪 测试WebSocket实时更新...")
        
        initial_count = len(self.received_messages)
        
        # 发送多条消息
        messages = [
            "实时消息测试 1",
            "实时消息测试 2", 
            "实时消息测试 3"
        ]
        
        for i, content in enumerate(messages):
            sender = 1 if i % 2 == 0 else 2
            receiver = 2 if i % 2 == 0 else 1
            self.send_message(sender, receiver, content)
            await asyncio.sleep(1)
        
        # 检查接收到的消息数量
        new_messages = len(self.received_messages) - initial_count
        print(f"📊 发送了 {len(messages)} 条消息，接收到 {new_messages} 条WebSocket消息")
        
        if new_messages >= len(messages):
            print("✅ WebSocket实时更新正常")
        else:
            print("❌ WebSocket实时更新可能有问题")
    
    async def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始消息系统改进综合测试")
        print("=" * 50)
        
        try:
            # 1. 连接WebSocket
            print("1️⃣ 连接WebSocket...")
            for user_id in self.test_users:
                success = await self.connect_websocket(user_id)
                if not success:
                    print(f"❌ 用户 {user_id} 连接失败，跳过测试")
                    return
            
            await asyncio.sleep(2)
            
            # 2. 检查在线用户
            print("\n2️⃣ 检查在线用户...")
            online_users = self.get_online_users()
            print(f"📊 在线用户: {online_users}")
            
            if set(self.test_users).issubset(set(online_users)):
                print("✅ 所有测试用户都在线")
            else:
                print("❌ 部分测试用户不在线")
            
            # 3. 测试页面状态管理
            await self.test_page_status_management()
            
            # 4. 测试智能通知逻辑
            await self.test_smart_notification_logic()
            
            # 5. 测试WebSocket实时更新
            await self.test_websocket_real_time_updates()
            
            # 6. 总结
            print("\n📊 测试总结:")
            print(f"   - 总共接收到 {len(self.received_messages)} 条WebSocket消息")
            print(f"   - 测试用户: {self.test_users}")
            print(f"   - 在线用户: {online_users}")
            
            # 显示最近的消息
            print("\n📨 最近的WebSocket消息:")
            for msg in self.received_messages[-5:]:
                print(f"   用户{msg['user_id']}: {msg['data'].get('type')} - {msg['timestamp'].strftime('%H:%M:%S')}")
            
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")
        
        finally:
            # 关闭连接
            print("\n🔌 关闭WebSocket连接...")
            for user_id, websocket in self.websockets.items():
                await websocket.close()
            
            print("✅ 测试完成")

async def main():
    """主函数"""
    tester = MessageImprovementTester()
    
    print("消息系统改进测试")
    print("请确保服务器正在运行在 localhost:8888")
    print("按 Ctrl+C 可以随时中断测试")
    
    try:
        await tester.run_comprehensive_test()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
