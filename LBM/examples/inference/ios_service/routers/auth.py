from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional
import json
from datetime import datetime
import logging
from pathlib import Path

# 配置日志
logger = logging.getLogger(__name__)
# 直接导入
from models import get_db
from services.auth_service import send_sms_code, verify_sms_code, register_user, login_user, validate_token, delete_account
from services.message_service import clear_device_tokens

router = APIRouter(
    prefix="/auth",
    tags=["认证"],
    responses={404: {"description": "Not found"}},
)

# 请求模型
class SendCodeRequest(BaseModel):
    phone: str

class VerifyCodeRequest(BaseModel):
    phone: str
    code: str

class RegisterRequest(BaseModel):
    phone: str
    username: str
    avatar: str

class TokenValidationRequest(BaseModel):
    user_id: int
    token: str

class DeleteAccountRequest(BaseModel):
    user_id: int

# 用户反馈模型
class FeedbackModel(BaseModel):
    user_id: int
    image_id: int
    feedback_type: str  # 反馈类型，如"广告"、"色情低俗"等
    image_path: Optional[str] = None  # 图片路径，可选

@router.post("/feedback", tags=["反馈"])
async def submit_feedback(feedback: FeedbackModel):
    """
    提交用户对图片的反馈
    """
    try:
        # 创建反馈目录（如果不存在）
        feedback_dir = Path("feedback_logs")
        feedback_dir.mkdir(exist_ok=True)
        
        # 创建反馈日志文件，按日期命名
        log_file = feedback_dir / f"feedback_{datetime.now().strftime('%Y-%m-%d')}.txt"
        
        # 记录反馈信息
        with open(log_file, "a", encoding="utf-8") as f:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            feedback_info = {
                "timestamp": timestamp,
                "user_id": feedback.user_id,
                "image_id": feedback.image_id,
                "feedback_type": feedback.feedback_type,
                "image_path": feedback.image_path
            }
            f.write(json.dumps(feedback_info, ensure_ascii=False) + "\n")
        
        return {"success": True, "message": "反馈已记录"}
    except Exception as e:
        logger.error(f"记录反馈失败: {str(e)}")
        return {"success": False, "message": f"记录反馈失败: {str(e)}"}
# 路由
@router.post("/send_code")
async def api_send_code(request: SendCodeRequest):
    """
    发送验证码
    """
    result = send_sms_code(request.phone)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

@router.post("/verify_code")
async def api_verify_code(request: VerifyCodeRequest):
    """
    验证验证码
    """
    result = verify_sms_code(request.phone, request.code)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

@router.post("/register")
async def api_register(request: RegisterRequest, db: Session = Depends(get_db)):
    """
    注册用户
    """
    result = register_user(db, request.phone, request.username, request.avatar)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

@router.post("/login")
async def api_login(request: SendCodeRequest, db: Session = Depends(get_db)):
    """
    用户登录
    """
    result = login_user(db, request.phone)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

@router.post("/validate_token")
async def api_validate_token(request: TokenValidationRequest, db: Session = Depends(get_db)):
    """
    验证用户令牌
    """
    result = validate_token(db, request.user_id, request.token)
    
    if not result["valid"]:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=result["message"]
        )
    
    return result 

@router.post("/delete_account")
async def api_delete_account(request: DeleteAccountRequest, db: Session = Depends(get_db)):
    """
    注销用户账户
    """
    # 先清空设备令牌
    clear_device_tokens(db, request.user_id)

    # 然后删除账户
    result = delete_account(db, request.user_id)

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )

    return result