from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query
from sqlalchemy.orm import Session
import json
import logging
from typing import Dict, Any

# 直接导入
from models import get_db
from services.message_service import websocket_manager, broadcast_user_status_change

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/ws",
    tags=["WebSocket"],
)

@router.websocket("/messages/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: int):
    """
    WebSocket消息端点
    """
    try:
        # 建立连接
        await websocket_manager.connect(websocket, user_id)

        # 广播用户上线状态
        await broadcast_user_status_change(user_id, True)
        
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理不同类型的消息
                message_type = message.get("type")
                
                if message_type == "page_status":
                    # 更新用户页面状态
                    page = message.get("page", "")
                    conversation_id = message.get("conversation_id", "")
                    websocket_manager.update_user_status(user_id, page, conversation_id)
                    
                    # 发送确认消息
                    await websocket.send_text(json.dumps({
                        "type": "status_updated",
                        "data": {
                            "page": page,
                            "conversation_id": conversation_id
                        }
                    }))
                
                elif message_type == "ping":
                    # 心跳检测
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": message.get("timestamp")
                    }))
                
                else:
                    logger.warning(f"未知消息类型: {message_type}")
                    
            except json.JSONDecodeError:
                logger.error("接收到无效的JSON数据")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "无效的JSON格式"
                }))
            except Exception as e:
                logger.error(f"处理WebSocket消息时出错: {str(e)}")
                break
                
    except WebSocketDisconnect:
        logger.info(f"用户 {user_id} WebSocket连接断开")
    except Exception as e:
        logger.error(f"WebSocket连接出错: {str(e)}")
    finally:
        # 清理连接
        websocket_manager.disconnect(user_id)

        # 广播用户下线状态
        await broadcast_user_status_change(user_id, False)

@router.websocket("/test")
async def websocket_test(websocket: WebSocket):
    """
    WebSocket测试端点
    """
    await websocket.accept()
    try:
        while True:
            data = await websocket.receive_text()
            await websocket.send_text(f"Echo: {data}")
    except WebSocketDisconnect:
        logger.info("测试WebSocket连接断开")
