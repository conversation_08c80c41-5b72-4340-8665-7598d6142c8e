from fastapi import APIRouter, Depends, HTTPException, status, Query, File, Form, UploadFile
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel
import os
from typing import Optional
from PIL import Image

# 直接导入
from models import get_db
from services.image_service import (
    submit_task, get_task_status, save_image_to_database,
    get_home_page_images, like_image, get_image_detail, delete_image,
    add_comment, get_comments, delete_comment, get_template_images,
    get_pending_audit_images, update_task_image
)
from config import OUTPUT_DIR

import io
try:
    from pillow_heif import register_heif_opener, HeifImage
    HEIC_SUPPORTED = True
    print("HEIC 支持已启用")
    register_heif_opener()
except ImportError:
    HEIC_SUPPORTED = False
    print("警告: pillow-heif 未安装，HEIC 支持不可用")

# === HEIC 处理函数 ===
def process_heic_image(upload_file: UploadFile) -> Image.Image:
    """专用于处理 HEIC 图像的函数"""
    if not HEIC_SUPPORTED:
        raise HTTPException(
            status_code=415, 
            detail="服务器不支持 HEIC 格式，请安装 pillow-heif 库"
        )
    
    # 直接读取字节数据
    heic_data = upload_file.file.read()
    
    try:
        # 方法1: 使用注册的 opener（如果成功）
        img = Image.open(io.BytesIO(heic_data)).convert('RGB')
        return img
    except Exception as e:
        print(f"使用直接方法失败，回退到 HEIF 库: {str(e)}")


router = APIRouter(
    prefix="/images",
    tags=["图片"],
    responses={404: {"description": "Not found"}},
)


# 请求模型
class SaveImageRequest(BaseModel):
    user_id: int
    task_id: str
    save_to_public: bool
    title: Optional[str] = None
    content: Optional[str] = None

class LikeImageRequest(BaseModel):
    image_id: int

class DeleteImageRequest(BaseModel):
    user_id: int
    image_id: int

class AddCommentRequest(BaseModel):
    user_id: int
    content: str
    parent_comment_id: Optional[int] = None

# 模板相关的路由 - 需要在通用路由之前定义，避免路由冲突
@router.get("/templates")
async def api_get_templates(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100)
):
    """
    获取模板图片列表
    """
    result = get_template_images(page, limit)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["message"]
        )
    
    return result

@router.get("/file/template/low/{filename}")
async def api_get_template_low_res_image(filename: str):
    """
    获取低分辨率模板图片
    """
    file_path = os.path.join(OUTPUT_DIR, "template", "low", filename)
    
    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板图片不存在"
        )
    
    return FileResponse(
        file_path, 
        media_type="image/jpeg", 
        filename=filename
    )

@router.get("/file/template/high/{filename}")
async def api_get_template_high_res_image(filename: str):
    """
    获取高分辨率模板图片
    """
    file_path = os.path.join(OUTPUT_DIR, "template", "high", filename)

    if not os.path.exists(file_path):
        # 如果高分辨率图不存在，尝试使用低分辨率图
        low_res_path = os.path.join(OUTPUT_DIR, "template", "low", filename)
        if os.path.exists(low_res_path):
            return FileResponse(
                low_res_path,
                media_type="image/jpeg",
                filename=filename
            )

        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板图片不存在"
        )

    return FileResponse(
        file_path,
        media_type="image/jpeg",
        filename=filename
    )

@router.get("/recommend")
async def api_get_recommend_images():
    """
    获取推荐广告图片列表
    """
    recommend_dir = os.path.join(OUTPUT_DIR, "recommend")

    if not os.path.exists(recommend_dir):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="推荐图片目录不存在"
        )

    try:
        # 获取目录下所有图片文件
        image_files = []
        for filename in os.listdir(recommend_dir):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')):
                image_files.append(filename)

        # 按文件名排序
        image_files.sort()

        return {
            "success": True,
            "images": image_files,
            "count": len(image_files)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取推荐图片列表失败: {str(e)}"
        )

@router.get("/file/recommend/{filename}")
async def api_get_recommend_image(filename: str):
    """
    获取推荐广告图片文件
    """
    file_path = os.path.join(OUTPUT_DIR, "recommend", filename)

    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="推荐图片不存在"
        )

    return FileResponse(
        file_path,
        media_type="image/jpeg",
        filename=filename
    )

# 路由
@router.post("/submit_task")
async def api_submit_task(
    user_id: int = Form(...),
    fg_image: UploadFile = File(...),
    bg_image: UploadFile = File(...),
    X: int = Form(...),
    Y: int = Form(...),
    Width: int = Form(...),
    Height: int = Form(...),
    Angle: int = Form(...),
    db: Session = Depends(get_db)
):
    """
    提交图像处理任务
    """
    # 读取上传的图片
    fg_img = Image.open(fg_image.file).convert('RGB')
    # bg_img = Image.open(bg_image.file).convert('RGB')
    
    bg_img = process_heic_image(bg_image)

    # 提交任务
    result = submit_task(db, user_id, fg_img, bg_img, X, Y, Width, Height,Angle)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

@router.get("/task_status/{task_id}")
async def api_get_task_status(task_id: str):
    """
    获取任务状态
    """
    result = get_task_status(task_id)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["message"]
        )
    
    return result

@router.post("/save_image")
async def api_save_image(
    user_id: int = Form(...),
    task_id: str = Form(...),
    save_to_public: bool = Form(...),
    title: Optional[str] = Form(None),
    content: Optional[str] = Form(None),
    category_id: int = Form(4),  # 默认为其他类别
    image: Optional[UploadFile] = File(None),
    db: Session = Depends(get_db)
):
    """
    将处理好的图片保存到数据库，并可选地更新图片
    """
    # 如果提供了新图片，先更新图片
    if image:
        try:
            image_data = await image.read()
            update_result = update_task_image(task_id, image_data)
            
            if not update_result["success"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=update_result["message"]
                )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"更新图像时出错: {str(e)}"
            )
    
    # 保存图片到数据库
    result = save_image_to_database(
        db,
        user_id,
        task_id,
        save_to_public,
        title,
        content,
        category_id
    )
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

@router.get("/home")
async def api_get_home_page_images(
    page: int = Query(1, ge=1),
    user_id: Optional[int] = None,
    sort_by: str = Query("rank", regex="^(rank|time)$"),  # 只允许rank或time
    category_id: int = Query(-1, ge=-1, le=4),  # -1表示全部类别，0-4表示具体类别
    db: Session = Depends(get_db)
):
    """
    获取首页图片列表，根据排序选项和类别排序
    category_id: -1=全部, 0=风景, 1=动漫, 2=古风, 3=二次元, 4=其他
    """
    result = get_home_page_images(db, page, user_id, sort_by, category_id)

    return result

@router.post("/{image_id}/like")
async def api_like_image(image_id: int, user_id: int, db: Session = Depends(get_db)):
    """
    给图片点赞或取消点赞
    """
    result = like_image(db, image_id, user_id)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["message"]
        )
    
    return result

@router.get("/{image_id}", response_model_exclude_none=True)
async def api_get_image_detail(image_id: int, user_id: Optional[int] = None, db: Session = Depends(get_db)):
    """
    获取图片详情
    """
    result = get_image_detail(db, image_id, user_id)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["message"]
        )
    
    return result

# 添加评论相关的路由
@router.post("/{image_id}/comments")
async def api_add_comment(
    image_id: int,
    request: AddCommentRequest,
    db: Session = Depends(get_db)
):
    """
    添加评论
    """
    result = add_comment(
        db,
        image_id,
        request.user_id,
        request.content,
        request.parent_comment_id
    )
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

@router.get("/{image_id}/comments")
async def api_get_comments(
    image_id: int,
    user_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """
    获取图片评论
    """
    result = get_comments(db, image_id, user_id)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["message"]
        )
    
    return result

@router.delete("/comments/{comment_id}")
async def api_delete_comment(
    comment_id: int,
    user_id: int,
    db: Session = Depends(get_db)
):
    """
    删除评论
    """
    result = delete_comment(db, comment_id, user_id)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

@router.get("/audit/pending/{user_id}")
async def api_get_pending_audit_images(user_id: int, db: Session = Depends(get_db)):
    """
    获取用户审核中的图片列表
    """
    result = get_pending_audit_images(db, user_id)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["message"]
        )
    
    return result

@router.get("/file/{filename}")
async def api_get_image_file(filename: str):
    """
    获取图片文件
    """
    file_path = os.path.join(OUTPUT_DIR, filename)
    
    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="图片不存在"
        )
    
    return FileResponse(file_path)

@router.get("/file/low/{filename}")
async def api_get_low_res_image(filename: str):
    """
    获取低分辨率图片文件
    """
    file_path = os.path.join(OUTPUT_DIR, "low", filename)
    
    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="低分辨率图片不存在"
        )
    
    return FileResponse(file_path)

@router.get("/file/output/{filename}")
async def api_get_output_image(filename: str):
    """
    获取输出图片文件
    """
    file_path = os.path.join(OUTPUT_DIR, "output", filename)
    
    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="输出图片不存在"
        )
    
    return FileResponse(file_path)

@router.get("/file/bg/{filename}")
async def api_get_background_image(filename: str):
    """
    获取背景图片文件
    """
    file_path = os.path.join(OUTPUT_DIR, "bg", filename)
    
    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="背景图片不存在"
        )
    
    return FileResponse(file_path)

@router.delete("/{image_id}")
async def api_delete_image(image_id: int, user_id: int, db: Session = Depends(get_db)):
    """
    删除用户上传的图片
    """
    result = delete_image(db, user_id, image_id)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

# 注意：下面的通配符路由必须放在最后，以避免路由冲突
@router.get("/{filename}")
async def api_get_image_file_legacy(filename: str):
    """
    获取图片文件（兼容旧版本）
    """
    # 首先尝试在output目录查找
    file_path = os.path.join(OUTPUT_DIR, "output", filename)
    
    if not os.path.exists(file_path):
        # 如果在output目录找不到，尝试在根目录查找
        file_path = os.path.join(OUTPUT_DIR, filename)
        
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="图片不存在"
            )
    
    return FileResponse(file_path) 