from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional, List

# 直接导入
from models import get_db
from services.message_service import (
    get_user_conversations,
    get_conversation_messages,
    send_message,
    delete_message,
    delete_conversation,
    register_device_token,
    clear_device_tokens,
    get_online_users,
    get_user_status,
    set_conversation_mute
)

router = APIRouter(
    prefix="/messages",
    tags=["私信"],
    responses={404: {"description": "Not found"}},
)

# 请求模型
class SendMessageRequest(BaseModel):
    sender_id: int
    receiver_id: int
    content: str

class DeleteMessageRequest(BaseModel):
    user_id: int
    message_id: int

class DeleteConversationRequest(BaseModel):
    user_id: int
    conversation_id: str

class RegisterDeviceTokenRequest(BaseModel):
    user_id: int
    device_token: str

class ClearDeviceTokensRequest(BaseModel):
    user_id: int

class GetConversationsRequest(BaseModel):
    user_id: int
    read_message_ids: Optional[dict] = {}  # {conversation_id: last_read_message_id}
    skip: Optional[int] = 0
    limit: Optional[int] = 20

class SetConversationMuteRequest(BaseModel):
    user_id: int
    conversation_id: str
    is_muted: bool

# 路由
@router.post("/conversations")
async def api_get_conversations(
    request: GetConversationsRequest,
    db: Session = Depends(get_db)
):
    """
    获取用户的私信会话列表（支持未读消息计算）
    """
    result = get_user_conversations(
        db,
        request.user_id,
        request.read_message_ids,
        request.skip,
        request.limit
    )

    if not result["success"]:
        # 只有在真正出错时才返回错误状态码
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )

    return result



@router.get("/conversation/{conversation_id}/user/{user_id}")
async def api_get_conversation_messages(
    conversation_id: str,
    user_id: int,
    last_message_id: int = Query(0, ge=0, description="客户端已有的最后一条消息ID，返回ID大于此值的新消息"),
    db: Session = Depends(get_db)
):
    """
    获取会话的新消息列表（增量获取）
    """
    result = get_conversation_messages(db, conversation_id, user_id, last_message_id)

    if not result["success"]:
        # 只有在真正出错时才返回错误状态码
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )

    return result

@router.post("/send")
async def api_send_message(request: SendMessageRequest, db: Session = Depends(get_db)):
    """
    发送私信消息
    """
    result = send_message(db, request.sender_id, request.receiver_id, request.content)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

@router.delete("/message")
async def api_delete_message(request: DeleteMessageRequest, db: Session = Depends(get_db)):
    """
    删除单条消息
    """
    result = delete_message(db, request.user_id, request.message_id)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

@router.delete("/conversation")
async def api_delete_conversation(request: DeleteConversationRequest, db: Session = Depends(get_db)):
    """
    删除整个会话
    """
    result = delete_conversation(db, request.user_id, request.conversation_id)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

@router.post("/register_device_token")
async def api_register_device_token(request: RegisterDeviceTokenRequest, db: Session = Depends(get_db)):
    """
    注册设备令牌用于推送通知
    """
    result = register_device_token(db, request.user_id, request.device_token)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

@router.delete("/clear_device_tokens")
async def api_clear_device_tokens(request: ClearDeviceTokensRequest, db: Session = Depends(get_db)):
    """
    清空用户的所有设备令牌
    """
    result = clear_device_tokens(db, request.user_id)

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )

    return result

@router.get("/online_users")
async def api_get_online_users():
    """
    获取当前在线用户列表
    """
    try:
        online_users = get_online_users()
        return {
            "success": True,
            "data": {
                "online_users": online_users,
                "count": len(online_users)
            }
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"获取在线用户失败: {str(e)}"
        }

@router.get("/user_status/{user_id}")
async def api_get_user_status(user_id: int):
    """
    获取用户状态信息
    """
    try:
        status = get_user_status(user_id)
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"获取用户状态失败: {str(e)}"
        }

@router.post("/set_conversation_mute")
async def api_set_conversation_mute(
    request: SetConversationMuteRequest,
    db: Session = Depends(get_db)
):
    """
    设置用户会话的免打扰状态
    """
    result = set_conversation_mute(
        db,
        request.user_id,
        request.conversation_id,
        request.is_muted
    )

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )

    return result