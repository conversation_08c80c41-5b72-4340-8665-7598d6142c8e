from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional

# 直接导入
from models import get_db
from services.user_service import get_user_balance, get_user_images, verify_purchase
from services.image_service import get_user_liked_images

router = APIRouter(
    prefix="/users",
    tags=["用户"],
    responses={404: {"description": "Not found"}},
)

# 请求模型
class VerifyPurchaseRequest(BaseModel):
    user_id: int
    product_id: str
    receipt_data: str
    is_sandbox: bool = False
    transaction_id: Optional[str] = None
    original_transaction_id: Optional[str] = None

# 路由
@router.get("/{user_id}/balance")
async def api_get_balance(user_id: int, db: Session = Depends(get_db)):
    """
    获取用户积分余额
    """
    result = get_user_balance(db, user_id)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["message"]
        )
    
    return result

@router.post("/verify_purchase")
async def api_verify_purchase(request: VerifyPurchaseRequest, db: Session = Depends(get_db)):
    """
    验证App内购买并更新用户余额
    """
    result = verify_purchase(
        db, 
        request.user_id, 
        request.product_id, 
        request.receipt_data,
        request.is_sandbox,
        request.transaction_id,
        request.original_transaction_id
    )
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["message"]
        )
    
    return result

@router.get("/{user_id}/images")
async def api_get_user_images(
    user_id: int, 
    skip: int = Query(0, ge=0), 
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    获取用户上传的图片
    """
    result = get_user_images(db, user_id, skip, limit)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["message"]
        )
    
    return result

@router.get("/{user_id}/liked_images")
async def api_get_user_liked_images(
    user_id: int, 
    skip: int = Query(0, ge=0), 
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    获取用户喜欢的图片
    """
    result = get_user_liked_images(db, user_id, skip, limit)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["message"]
        )
    
    return result 