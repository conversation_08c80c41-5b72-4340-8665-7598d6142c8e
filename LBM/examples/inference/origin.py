import sys
import glob
import logging
import os
from copy import deepcopy
from concurrent.futures import T<PERSON><PERSON><PERSON>oolExecutor
from typing import Dict, Optional, <PERSON><PERSON>
import uuid
import time

import numpy as np
import PIL
import torch
from fastapi import FastAPI, UploadFile, File, Form
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from huggingface_hub import snapshot_download
from PIL import Image
from torchvision.transforms import ToPILImage, ToTensor
from transformers import AutoModelForImageSegmentation
from utils import extract_object, resize_and_center_crop
from pydantic import BaseModel

from lbm.inference import get_model

app = FastAPI()

# 配置静态文件目录
OUTPUT_DIR = "/data5/IC-Light/output/IC"
os.makedirs(OUTPUT_DIR, exist_ok=True)
app.mount("/output", StaticFiles(directory=OUTPUT_DIR), name="output")

PATH = os.path.dirname(os.path.abspath(__file__))
os.environ["GRADIO_TEMP_DIR"] = ".gradio"

# 初始化模型
if not os.path.exists(os.path.join(PATH, "ckpts", "relighting")):
    logging.info(f"Downloading relighting LBM model from HF hub...")
    model = get_model(
        f"jasperai/LBM_relighting",
        save_dir=os.path.join(PATH, "ckpts", "relighting"),
        torch_dtype=torch.bfloat16,
        device="cuda",
    )
else:
    model_dir = os.path.join(PATH, "ckpts", "relighting")
    logging.info(f"Loading relighting LBM model from local...")
    model = get_model(
        os.path.join(PATH, "ckpts", "relighting"),
        torch_dtype=torch.bfloat16,
        device="cuda",
    )

ASPECT_RATIOS = {
    str(512 / 2048): (512, 2048),
    str(1024 / 1024): (1024, 1024),
    str(2048 / 512): (2048, 512),
    str(896 / 1152): (896, 1152),
    str(1152 / 896): (1152, 896),
    str(512 / 1920): (512, 1920),
    str(640 / 1536): (640, 1536),
    str(768 / 1280): (768, 1280),
    str(1280 / 768): (1280, 768),
    str(1536 / 640): (1536, 640),
    str(1920 / 512): (1920, 512),
}

birefnet = AutoModelForImageSegmentation.from_pretrained(
    "ZhengPeng7/BiRefNet", trust_remote_code=True
).cuda()

# 线程池和任务状态管理
executor = ThreadPoolExecutor(max_workers=1)  # 单线程执行
task_status: Dict[str, Dict] = {}
processing_lock = False

def evaluate(
    fg_image: PIL.Image.Image,
    bg_image: PIL.Image.Image,
    task_id: str,
    num_sampling_steps: int = 4,
) -> bool:
    try:
        ori_h_bg, ori_w_bg = fg_image.size
        ar_bg = ori_h_bg / ori_w_bg
        closest_ar_bg = min(ASPECT_RATIOS, key=lambda x: abs(float(x) - ar_bg))
        dimensions_bg = ASPECT_RATIOS[closest_ar_bg]

        _, fg_mask = extract_object(birefnet, deepcopy(fg_image))

        fg_image = resize_and_center_crop(fg_image, dimensions_bg[0], dimensions_bg[1])
        fg_mask = resize_and_center_crop(fg_mask, dimensions_bg[0], dimensions_bg[1])
        bg_image = resize_and_center_crop(bg_image, dimensions_bg[0], dimensions_bg[1])

        img_pasted = Image.composite(fg_image, bg_image, fg_mask)

        img_pasted_tensor = ToTensor()(img_pasted).unsqueeze(0) * 2 - 1
        batch = {
            "source_image": img_pasted_tensor.cuda().to(torch.bfloat16),
        }

        z_source = model.vae.encode(batch[model.source_key])

        output_image = model.sample(
            z=z_source,
            num_steps=num_sampling_steps,
            conditioner_inputs=batch,
            max_samples=1,
        ).clamp(-1, 1)

        output_image = (output_image[0].float().cpu() + 1) / 2
        output_image = ToPILImage()(output_image)

        # paste the output image on the background image
        output_image = Image.composite(output_image, bg_image, fg_mask)
        output_image = output_image.resize((ori_h_bg, ori_w_bg))
        
        # 直接保存为32位webp格式
        output_path = os.path.join(OUTPUT_DIR, f"{task_id}.webp")
        output_image.save(output_path, 'WEBP', lossless=True, quality=100)
        
        return True
    except Exception as e:
        logging.error(f"Task {task_id} failed: {str(e)}")
        return False

@app.post("/submit_task")
async def submit_task(
    fg_image: UploadFile = File(...),
    bg_image: UploadFile = File(...),
    X: int = Form(...),
    Y: int = Form(...),
    Width: int = Form(...),
    Height: int = Form(...),
):
    global processing_lock
    
    task_id = str(uuid.uuid4())
    
    # 保存上传的图片
    fg_img = Image.open(fg_image.file).convert('RGB')
    # 根据图片模式选择正确的转换
    bg_img = Image.open(bg_image.file)
    if bg_img.mode in ['RGBA', 'LA']:
        bg_img = bg_img.convert('RGBA')  # 保持32位
    else:
        bg_img = bg_img.convert('RGB')   # 24位
    
    # 1. 将前景图resize成指定宽高
    fg_img_resized = fg_img.resize((Width, Height))
    
    # 2. 创建一个与背景图大小相同的空白图片
    bg_width, bg_height = bg_img.size
    new_fg_img = Image.new('RGB', (bg_width, bg_height), (255, 255, 255))
    
    # 3. 将resize后的前景图放置到指定位置
    new_fg_img.paste(fg_img_resized, (X, Y))
    
    # 4. 保存所有图片
    output_dir = "/data5/IC-Light/output/IC"
    os.makedirs(output_dir, exist_ok=True)
    
    original_fg_path = os.path.join(output_dir, f"{task_id}_original_fg.png")
    new_fg_path = os.path.join(output_dir, f"{task_id}_new_fg.png")
    bg_path = os.path.join(output_dir, f"{task_id}_bg.png")
    
    fg_img.save(original_fg_path)
    new_fg_img.save(new_fg_path)
    bg_img.save(bg_path)
    
    task_status[task_id] = {
        "status": "processing",
        "output_path": None
    }
    
    def process_task():
        global processing_lock
        processing_lock = True
        
        success = evaluate(new_fg_img, bg_img, task_id)
        
        if success:
            task_status[task_id]["status"] = "completed"
            task_status[task_id]["output_path"] = f"/output/{task_id}.webp"
        else:
            task_status[task_id]["status"] = "failed"
            
        processing_lock = False
    
    # 提交任务到线程池
    executor.submit(process_task)
    
    return JSONResponse({
        "task_id": task_id,
        "status": "processing"
    })

@app.get("/task_status/{task_id}")
async def get_task_status(task_id: str):
    if task_id not in task_status:
        return JSONResponse({
            "status": "not_found"
        }, status_code=404)
    
    return JSONResponse({
        "status": task_status[task_id]["status"],
        "output_path": task_status[task_id]["output_path"]
    })

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
