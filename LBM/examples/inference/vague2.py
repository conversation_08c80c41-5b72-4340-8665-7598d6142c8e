import cv2
import numpy as np

def adjust_background_blur(image_path, mask_path, blur_kernel_size=51, output_path='output.jpg'):
    # 1. 加载并转换图像
    image = cv2.imread(image_path)
    back=cv2.imread("/data/lsc/IClight/LBM/img/out/R-C.jpg")
    background = cv2.GaussianBlur(back, (blur_kernel_size, blur_kernel_size), 0)
    cv2.imwrite("/data/lsc/IClight/LBM/img/out/background.jpg", background)  
    
    
    # 2. 加载并处理掩模
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)  
    _, mask = cv2.threshold(mask, 127, 255, cv2.THRESH_BINARY)  
    # 4. 背景模糊处理
    blurred_bg = cv2.GaussianBlur(image, (blur_kernel_size, blur_kernel_size), 0)
    foreground = cv2.bitwise_and(image, image, mask=mask)
    background = cv2.bitwise_and(blurred_bg, blurred_bg, mask=cv2.bitwise_not(mask))
    result = cv2.add(foreground, background)
    cv2.imwrite(output_path, result)  

# 调用示例
if __name__ == "__main__":
    adjust_background_blur(
        image_path='/data/lsc/IClight/LBM/img/out/bf584180-b1b3-4ddd-97fb-767ea5fd6468_output_2587_1728.webp',
        mask_path='/data/lsc/IClight/LBM/img/out/bf584180-b1b3-4ddd-97fb-767ea5fd6468_mask_2587_1728.png',
        output_path='/data/lsc/IClight/LBM/img/out/out.jpg'  
    )