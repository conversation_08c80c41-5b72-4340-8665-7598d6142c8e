version: '3'
services:
  lhm-ic:  # 新增的服务：docker-compose up -d lhm-ic
    build:
      context: .
    container_name: LHM-IC
    image: vue_vue-app
    ports:
      - "5190:5190"  # 修改后的新端口
    volumes:
      - /data/lsc/LHM-IC:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - HOST=0.0.0.0
      - VITE_API_URL=http://**********:7863
    working_dir: /app
    tty: true
    stdin_open: true
    command: npx vite --host 0.0.0.0 --port 5190 --strictPort  # 修改端口参数
    restart: unless-stopped