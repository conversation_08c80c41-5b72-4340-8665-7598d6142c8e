# LHM 前端界面

这是一个使用Vue 3和Vite构建的前端界面，用于与LHM（大型可动人体模型）后端API进行交互。

## 功能

- 上传人物图像和姿势图像
- 支持从示例图片中选择
- 显示处理后的参考图像和渲染结果
- 响应式设计，适配不同屏幕尺寸

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式运行

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

## 接口说明

前端通过以下接口与后端通信：

- POST `/api/process`: 处理图像并返回结果
  - 请求参数: 
    - `input_image`: 人物图像文件
    - `pose_image`: 姿势图像文件
    - `model_name`: 模型名称，默认为"LHM-1B-HF"
  - 响应格式:
    ```json
    {
      "success": true,
      "processed_image": "图像URL路径",
      "rendered_image": "图像URL路径"
    }
    ```

## 项目结构

```
/
├── src/               # 源代码
│   ├── App.vue        # 主应用组件  
│   ├── main.js        # 应用入口
│   └── style.css      # 全局样式
├── public/            # 静态资源
├── index.html         # HTML入口
├── vite.config.js     # Vite配置
└── package.json       # 项目依赖
``` 