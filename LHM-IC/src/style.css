/* 全局样式 */
:root {
  --primary-color: #3a7bd5;
  --secondary-color: #00d2ff;
  --accent-color: #4CAF50;
  --bg-color: #f9f9f9;
  --card-bg: #ffffff;
  --text-color: #333333;
  --light-text: #666666;
  --border-color: #e0e0e0;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --border-radius: 12px;
  --small-radius: 8px;
  --box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  --hover-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
  --transition: all 0.3s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
}

button {
  cursor: pointer;
  font-family: inherit;
  transition: var(--transition);
}

img {
  max-width: 100%;
  transition: var(--transition);
}

h1, h2, h3, h4, h5, h6 {
  color: var(--primary-color);
  font-weight: 600;
  line-height: 1.3;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--secondary-color);
}

.gradient-text {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}