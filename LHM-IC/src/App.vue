<template>
  <div class="main-container">
    <header class="header">
      <div class="logo-container">
        <h1 class="title">人大出版社"七秩风华·AI映像"体验版</h1>
      </div>
    </header>
    
    <main>
      <!-- 人物图像和裁剪界面 -->
      <div class="content-grid">
        <!-- 调整为两列布局：人物图像和裁剪框 -->
        <div class="background-panel-container">
          <ImagePanel
            title="请上传您摆好姿势的照片"
            :imageUrl="renderedImage"
            placeholder="请上传人物图像"
            :showUploadControls="true"
            :showExamples="false"
            @file-selected="handleRenderedImageFile"
            :showDownloadButton="false"
          />
        </div>
        
        <!-- 裁剪器组件，始终显示 -->
        <div class="cropper-panel">
          <ImageCropper
            :imageUrl="renderedImage || placeholderImage"
            @crop-confirmed="handleCroppedImage"
            @reset-to-default="handleResetImage"
            ref="imageCropper"
          />
        </div>
      </div>

      <!-- 背景图片滑动卡片选择器 -->
      <BackgroundGallery
        @background-selected="handleBackgroundImageFile"
        @custom-background-click="triggerBackgroundFileSelect"
        :renderedPoseImage="renderedImage"
      />

      <!-- 背景和重光照功能 -->
      <div class="content-grid second-row">
        <!-- 第一列：使用新的背景图编辑器组件 -->
        <div class="background-panel-container">
          <BackgroundImageEditor
            :renderedPoseImage="renderedImage"
            :canUpload="canUploadBackground"
            :backgroundFile="backgroundImageFile"
            @file-selected="handleBackgroundImageFile"
            @box-change="handleBoundingBoxChange"
            @blur-value-change="handleBlurValueChange"
            ref="backgroundImageEditor"
          />
        </div>
        
        <!-- 第三列：重光照图 -->
        <ImagePanel
          title="结果"
          :imageUrl="relightedImage"
          placeholder="请查收您的专属AI映像"
          :showUploadControls="false"
          :showExamples="false"
          :showDownloadButton="!!relightedImage"
          :showProcessButton="true"
          :isProcessButtonDisabled="!canRelight || isRelightProcessing"
          :isProcessing="isRelightProcessing"
          :statusMessage="isRelightProcessing ? '重光照处理中...' : (hasError ? errorMessage : '')"
          :processButtonText="'开始生成'"
          :customFileName="relightedImageFileName"
          @process-clicked="processRelighting"
        />
      </div>
    </main>
  </div>
</template>

<script>
import axios from 'axios';
import ImagePanel from './components/ImagePanel.vue';
import LoadingIndicator from './components/LoadingIndicator.vue';
import BackgroundImageEditor from './components/BackgroundImageEditor.vue';
import ImageCropper from './components/ImageCropper.vue';
import BackgroundGallery from './components/BackgroundGallery.vue';

export default {
  name: 'LHMProcessor',
  components: {
    ImagePanel,
    LoadingIndicator,
    BackgroundImageEditor,
    ImageCropper,
    BackgroundGallery
  },
  data() {
    return {
      backgroundImage: null,  // 背景图片URL
      relightedImage: null,   // 重光照结果图片URL
      backgroundImageFile: null,  // 背景图片文件
      renderedImage: null,
      renderedImageBlob: null,
      originalRenderedImage: null, // 存储原始渲染图像
      isCropped: false,           // 是否已裁剪
      cachedExampleImages: {},
      isRelightProcessing: false, // 重光照处理状态
      errorMessage: '',
      successMessage: '',
      hasError: false,
      // 使用base64编码的占位图，一个简单的灰色占位图
      placeholderImage: 'data:image/png;base64,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',
      // 存储边界框的位置和尺寸
      boundingBox: {
        x: 0,
        y: 0,
        width: 0,
        height: 0
      },
      blurValue: 1, // 添加模糊值参数，默认为1
      allExampleImages: [
        '/images/00000000_joker_2.jpg',
        '/images/yl.jpg',
        '/images/00000000_shigao.jpg',
        '/images/000000_half2.jpg',
        '/images/yingmuhuadao.jpg',
        '/images/7.JPG',
        '/images/4.JPG',
        '/images/11.JPG',
        '/images/14.JPG',
        '/images/000057.png',
        '/images/0000_test.png',
        '/images/00000_half.png'
      ],
      isMobileDevice: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
      relightedImageFileName: ''
    };
  },
  computed: {
    canRelight() {
      return this.renderedImage && this.backgroundImage && 
             this.boundingBox && 
             this.boundingBox.width > 0 && 
             this.boundingBox.height > 0;
    },
    canUploadBackground() {
      // 只有在渲染图像存在且已裁剪或原始图像不存在的情况下才能上传背景图
      return this.renderedImage && (this.isCropped || !this.originalRenderedImage);
    }
  },
  beforeUnmount() {
    // 清理blob URLs
    if (this.renderedImageBlob) {
      URL.revokeObjectURL(this.renderedImageBlob);
    }
    
    // 清理背景图像URL
    if (this.backgroundImage && this.backgroundImage.startsWith('blob:')) {
      URL.revokeObjectURL(this.backgroundImage);
    }
    
    // 清理重光照图像URL
    if (this.relightedImage && this.relightedImage.startsWith('blob:')) {
      URL.revokeObjectURL(this.relightedImage);
    }
    
    // 清理原始渲染图像URL
    if (this.originalRenderedImage && this.originalRenderedImage.startsWith('blob:')) {
      URL.revokeObjectURL(this.originalRenderedImage);
    }
  },
  methods: {
    // 处理直接上传的渲染图像文件
    async handleRenderedImageFile(file) {
      // 创建Blob URL，保留原始MIME类型
      const blob = new Blob([await file.arrayBuffer()], { type: file.type });
      const blobUrl = URL.createObjectURL(blob);
      
      // 更新渲染图像
      this.renderedImage = blobUrl;
      
      // 重置裁剪状态
      this.originalRenderedImage = null;
      this.isCropped = false;
      
      // 清除错误消息
      this.errorMessage = '';
      this.successMessage = '';
    },

    // 处理背景图像文件
    async handleBackgroundImageFile(file) {
      // 确保file是有效的
      if (!file) {
        console.error('Invalid background image file');
        return;
      }

      this.backgroundImageFile = file;
      
      // 如果之前有背景图URL，先释放
      if (this.backgroundImage && this.backgroundImage.startsWith('blob:')) {
        URL.revokeObjectURL(this.backgroundImage);
      }
      
      // 创建新的blob URL
      this.backgroundImage = URL.createObjectURL(file);
      
      // 清除之前的重光照结果
      this.relightedImage = null;
      this.errorMessage = '';
      this.successMessage = '';
    },
    
    // 处理边界框变化
    handleBoundingBoxChange(dimensions) {
      // 存储整数边界框数据
      this.boundingBox = {
        x: Math.round(dimensions.x),
        y: Math.round(dimensions.y),
        width: Math.round(dimensions.width),
        height: Math.round(dimensions.height)
      };
    },
    
    // 处理模糊值变化
    handleBlurValueChange(value) {
      this.blurValue = value;
    },
    
    // 处理重光照请求
    async processRelighting() {
      // 检查必要条件
      if (!this.renderedImage || !this.backgroundImage || !this.boundingBox) {
        return;
      }
      
      // 重置状态
      this.isRelightProcessing = true;
      this.errorMessage = '';
      this.successMessage = '';
      this.hasError = false;
      this.relightedImage = null;
      
      try {
        // 创建FormData对象
        const formData = new FormData();
        
        // 将渲染后图像转换为File对象
        const renderedImageBlob = await fetch(this.renderedImage).then(r => r.blob());
        // 检测MIME类型
        const mimeType = renderedImageBlob.type || 'image/jpeg';
        const fileExt = mimeType.split('/')[1] || 'jpg';
        const renderedImageFile = new File([renderedImageBlob], `rendered_pose.${fileExt}`, { type: mimeType });
        
        // 添加参数
        formData.append('fg_image', renderedImageFile);
        formData.append('bg_image', this.backgroundImageFile);
        formData.append('X', String(this.boundingBox.x));
        formData.append('Y', String(this.boundingBox.y));
        formData.append('Width', String(this.boundingBox.width));
        formData.append('Height', String(this.boundingBox.height));
        
        // 提交任务
        const response = await axios.post(
          '/submit_task',
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            timeout: 300000 // 5分钟超时
          }
        );
        
        // 保存任务ID
        if (response.data && response.data.task_id) {
          const taskId = response.data.task_id;
          
          // 轮询任务状态
          let completed = false;
          let attempts = 0;
          const maxAttempts = 60; // 最多尝试60次，每次间隔5秒
          
          while (!completed && attempts < maxAttempts) {
            attempts++;
            
            // 等待5秒
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 检查任务状态
            const statusResponse = await axios.get(`/task_status/${taskId}`);
            
            if (statusResponse.data.status === 'completed') {
              completed = true;
              
              // 获取结果图片
              const outputPath = statusResponse.data.output_path;
              
              // 提取文件名用于下载
              const fileName = outputPath ? outputPath.split('/').pop() : 'relighted_image.webp';
              this.relightedImageFileName = fileName; // 保存文件名供下载使用
              
              // 修正路径 - 将/output路径转换为/relighting_output路径
              let relightedImageUrl = '';
              if (outputPath && outputPath.startsWith('/output/')) {
                // 替换路径前缀
                relightedImageUrl = `/relighting_output/${fileName}`;
              } else {
                relightedImageUrl = this.ensureValidImageUrl(outputPath);
              }
              
              try {
                // 缓存重光照结果图片
                const response = await fetch(relightedImageUrl, { 
                  cache: 'no-store',
                  credentials: 'omit', 
                  mode: 'cors'  
                });
                
                if (!response.ok) {
                  throw new Error(`图片加载失败: ${response.status} ${response.statusText}`);
                }
                
                const blob = await response.blob();
                const blobUrl = URL.createObjectURL(blob);
                this.relightedImage = blobUrl;
              } catch (error) {
                // 使用原始URL作为后备
                this.relightedImage = relightedImageUrl;
              }
              
              this.successMessage = '重光照处理成功';
            } else if (statusResponse.data.status === 'failed') {
              this.errorMessage = '重光照处理失败';
              this.hasError = true;
              break;
            }
          }
          
          if (!completed) {
            this.errorMessage = '重光照处理超时';
            this.hasError = true;
          }
        } else {
          this.errorMessage = '提交重光照任务失败';
          this.hasError = true;
        }
      } catch (error) {
        this.errorMessage = '重光照处理失败';
        this.hasError = true;
      } finally {
        this.isRelightProcessing = false;
      }
    },
    
    // 加载示例图片
    async loadExampleImage(imagePath, type) {
      try {
        // 检查是否已经缓存了这张图片
        if (this.cachedExampleImages[imagePath]) {
          const cachedFile = this.cachedExampleImages[imagePath];
          
          if (type === 'rendered') {
            this.handleRenderedImageFile(cachedFile);
          } else if (type === 'background') {
            this.handleBackgroundImageFile(cachedFile);
          }
          return;
        }
        
        // 如果没有缓存，请求图片并缓存
        // 使用缓存策略避免重复请求
        const response = await fetch(imagePath, {
          cache: 'force-cache', // 优先使用缓存
          credentials: 'omit', // 不发送cookies
          mode: 'cors'  // 使用CORS
        });
        const blob = await response.blob();
        const fileName = imagePath.split('/').pop();
        const mimeType = `image/${fileName.split('.').pop().toLowerCase()}`;
        const file = new File([blob], fileName, { type: mimeType });
        
        // 缓存这个文件对象
        this.cachedExampleImages[imagePath] = file;
        
        if (type === 'rendered') {
          this.handleRenderedImageFile(file);
        } else if (type === 'background') {
          this.handleBackgroundImageFile(file);
        }
      } catch (error) {
        this.errorMessage = '加载示例图片失败，请重试';
      }
    },
    
    // 确保图片URL有效，如果是相对路径则保持原样
    ensureValidImageUrl(url) {
      if (!url) return null;
      
      // 对于以/output开头的路径，保持原样，使用代理访问
      if (url.startsWith('/output')) {
        return url;
      }
      
      // 如果已经是完整URL，确保使用HTTPS
      if (url.startsWith('http://') || url.startsWith('https://')) {
        // 将HTTP替换为HTTPS
        return url.replace(/^http:/, 'https:');
      }
      
      // 其他相对路径，确保以/开头
      if (!url.startsWith('/')) {
        return `/${url}`;
      }
      
      return url;
    },
    
    // 缓存图片到本地
    async cacheImages(renderedImageUrl) {
      const cachePromises = [];
      let renderedImageBlob = null;

      // 缓存渲染后的图片
      if (renderedImageUrl) {
        // 如果已经是blob URL，直接使用
        if (renderedImageUrl.startsWith('blob:')) {
          this.renderedImageBlob = renderedImageUrl;
          renderedImageBlob = renderedImageUrl;
        } else {
          // 转换协议为HTTPS（如果是HTTP）
          const secureUrl = renderedImageUrl.replace(/^http:/, 'https:');
          
          const renderedPromise = fetch(secureUrl, { 
            cache: 'no-store',
            credentials: 'omit', // 不发送cookies
            mode: 'cors'  // 使用CORS
          })
            .then(response => response.blob())
            .then(blob => {
              renderedImageBlob = URL.createObjectURL(blob);
              this.renderedImageBlob = renderedImageBlob;
              return renderedImageBlob;
            })
            .catch(error => {
              return null;
            });
          
          cachePromises.push(renderedPromise);
        }
      }

      // 等待所有缓存操作完成
      await Promise.all(cachePromises);

      // 返回缓存结果
      return {
        renderedImageBlob
      };
    },
    
    // 重置所有状态
    resetAllStates() {
      // 清除缓存的blob URLs，防止内存泄漏
      if (this.renderedImageBlob) {
        URL.revokeObjectURL(this.renderedImageBlob);
        this.renderedImageBlob = null;
      }
      
      // 不清除缓存的示例图片，因为它们可能会再次使用
      
      // 重置处理状态
      this.isRelightProcessing = false; // 重置重光照处理状态
      this.errorMessage = this.hasError ? this.errorMessage : ''; // 保留错误信息，避免覆盖
      this.successMessage = '';
      
      // 重置裁剪状态
      if (this.originalRenderedImage && this.originalRenderedImage.startsWith('blob:')) {
        URL.revokeObjectURL(this.originalRenderedImage);
      }
      this.originalRenderedImage = null;
      this.isCropped = false;
      
      // 清除重光照图像的blob URL
      if (this.relightedImage && this.relightedImage.startsWith('blob:')) {
        URL.revokeObjectURL(this.relightedImage);
      }
      // 不立即将relightedImage设置为null，避免UI突然变化
    },
    
    // 处理裁剪后的图像
    handleCroppedImage(croppedImageUrl) {
      // 如果是第一次裁剪，保存原始图像
      if (!this.originalRenderedImage) {
        this.originalRenderedImage = this.renderedImage;
      }
      
      // 更新渲染图像为裁剪后的图像
      this.renderedImage = croppedImageUrl;
      this.isCropped = true;
    },
    
    // 处理恢复原始图像
    handleResetImage(originalImageUrl) {
      // 恢复到原始图像
      if (this.originalRenderedImage) {
        this.renderedImage = this.originalRenderedImage;
        this.isCropped = false;
      }
    },

    // 添加custom-background-click事件处理
    triggerBackgroundFileSelect() {
      // 触发BackgroundImageEditor组件中的文件选择
      if (this.$refs.backgroundImageEditor) {
        this.$refs.backgroundImageEditor.triggerFileSelect();
      }
    }
  }
};
</script>

<style scoped>
:root {
  --primary-color: #3a7bd5;
  --secondary-color: #00d2ff;
  --accent-color: #4CAF50;
  --text-color: #333;
  --light-text: #666;
  --border-radius: 12px;
  --box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

.main-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 30px;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  color: var(--text-color);
  background-color: #f9f9f9;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.logo-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  font-size: 32px;
  color: var(--primary-color);
  margin: 0;
  margin-bottom: 15px;
  font-weight: 600;
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 基本网格布局 */
.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 25px;
  margin-bottom: 35px;
  height: 600px; /* 设置固定高度 */
}

/* 第二行的布局 */
.second-row {
  margin-top: 25px; /* 增加与第一行的间距 */
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 25px;
  height: 600px; /* 与第一行保持一致的高度 */
}

/* 调整内部元素高度 */
.background-panel-container,
.cropper-panel {
  height: 100%;
  max-height: 600px;
  overflow: hidden;
  background-color: #fff;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  display: flex; /* 确保子元素可以使用flex布局 */
  flex-direction: column; /* 垂直排列子元素 */
}

.background-panel-container:hover,
.cropper-panel:hover {
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 调整图片容器 */
.image-container {
  height: 100% !important; /* 修改为100%高度，填充整个容器 */
  max-height: 550px !important;
  aspect-ratio: auto !important; /* 覆盖默认的宽高比 */
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: var(--transition);
}

/* 背景图编辑器容器 */
.background-panel-container {
  grid-column: 1;
}

/* 裁剪面板 */
.cropper-panel {
  height: 100%;
  background-color: #fff;
  border-radius: var(--border-radius);
  padding: 20px;
  display: flex;
  flex-direction: column;
  grid-column: 2;
  overflow: hidden;
}

/* 调整裁剪器内部样式 */
.cropper-panel .image-cropper-container {
  height: 100% !important;
  max-height: 550px !important;
  overflow: hidden;
}

/* 调整裁剪器中的图像容器 */
.cropper-panel .cropper-container {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border-radius: calc(var(--border-radius) - 5px);
}

/* 确保第二行元素高度一致 */
.second-row .image-panel,
.second-row .background-image-editor {
  height: 100%;
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

/* 调整第二行图片容器的高度 */
.second-row .image-container {
  height: 100% !important; /* 确保填充整个容器 */
  aspect-ratio: auto; /* 移除固定宽高比 */
  flex: 1; /* 让容器占据可用空间 */
}

.empty-panel {
  visibility: hidden;
}

.action-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 35px 0;
}

.process-btn {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
  padding: 14px 34px;
  border-radius: 30px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: var(--transition);
  min-width: 180px;
  box-shadow: 0 4px 15px rgba(58, 123, 213, 0.3);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.process-btn:disabled {
  background: linear-gradient(45deg, #ccc, #ddd);
  cursor: not-allowed;
  box-shadow: none;
}

.process-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(58, 123, 213, 0.4);
}

.error-message {
  margin-top: 25px;
  padding: 15px 20px;
  background-color: #fff5f5;
  color: #e53e3e;
  border-radius: var(--border-radius);
  border-left: 4px solid #e53e3e;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.1);
}

.success-message {
  margin-top: 25px;
  padding: 15px 20px;
  background-color: #f0fff4;
  color: #38a169;
  border-radius: var(--border-radius);
  border-left: 4px solid #38a169;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 4px 12px rgba(56, 161, 105, 0.1);
}

.task-status {
  margin-top: 15px;
  text-align: center;
  font-size: 14px;
  color: var(--light-text);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-grid,
  .second-row {
    grid-template-columns: 1fr 1fr;
    height: auto; /* 在中等屏幕上取消固定高度 */
    min-height: 500px; /* 但保持最小高度 */
  }
  
  /* 调整中等屏幕上的图像容器 */
  .image-container {
    height: 100% !important;
    max-height: none !important;
    min-height: 300px !important; /* 确保最小高度 */
    aspect-ratio: auto !important; /* 移除固定宽高比 */
  }
  
  /* 调整中等屏幕上的裁剪器容器 */
  .cropper-panel .image-cropper-container {
    height: 100% !important;
    max-height: none !important;
    min-height: 300px !important; /* 确保最小高度 */
  }
}

@media (max-width: 768px) {
  .main-container {
    padding: 15px;
  }

  .title {
    font-size: 24px;
  }

  .content-grid, 
  .second-row {
    grid-template-columns: 1fr !important;
    height: auto;
    gap: 15px;
    min-height: 400px; /* 移动设备上的最小高度 */
  }
  
  .cropper-panel,
  .background-panel-container {
    grid-column: 1;
    margin-bottom: 20px;
    min-height: 400px; /* 移动设备上的最小高度 */
  }
  
  /* 调整小屏幕上的图像容器和裁剪器容器 */
  .image-container,
  .cropper-panel .image-cropper-container {
    height: 100% !important;
    max-height: none !important;
    min-height: 300px !important; /* 确保最小高度 */
    aspect-ratio: auto !important;
  }
}

/* 背景图片滑动卡片选择器样式 */
.background-gallery {
  margin: 25px 0;
  width: 100%;
  background-color: #fff;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.background-gallery:hover {
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}
</style> 