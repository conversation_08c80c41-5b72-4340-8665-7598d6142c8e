<template>
  <div class="image-panel">
    <h2>{{ title }}</h2>
    <div 
      class="image-container" 
      :class="{ 'clickable': showUploadControls }" 
      @click="showUploadControls && triggerFileSelect()" 
      @touchend.prevent="showUploadControls && triggerFileSelect()"
    >
      <div v-if="imageUrl" class="image-wrapper">
        <img 
          :src="imageUrl" 
          alt="上传图像" 
          class="image-display" 
          @error="handleImageError"
        />
        <!-- 预览大图按钮 -->
        <button 
          v-if="showDownloadButton && imageUrl" 
          @click.stop.prevent="previewImage" 
          class="preview-btn"
          title="预览大图"
        >
          <span class="preview-icon">🔍</span>
        </button>
        <!-- 下载按钮 -->
        <button 
          v-if="showDownloadButton && imageUrl" 
          @click.stop.prevent="downloadImage" 
          class="download-btn"
          title="下载图片"
        >
          <span class="download-icon">⬇️</span>
        </button>
      </div>
      <div v-else-if="statusMessage" class="status-message" :class="{'error-status': isErrorMessage}">
        <div class="status-icon">{{ isErrorMessage ? '❌' : '⏳' }}</div>
        <div class="status-text">{{ statusMessage }}</div>
      </div>
      <div v-else class="placeholder">
        <div class="placeholder-icon">{{ icon }}</div>
        <div class="placeholder-text">{{ showUploadControls ? '上传的人物分辨率越高，生成结果越好哦' : placeholder }}</div>
        <div v-if="showUploadControls && isMobileDevice" class="placeholder-hint">可从相册选择或拍照</div>
      </div>
      
      <input 
        type="file" 
        ref="fileInput" 
        @change="onFileSelected" 
        accept="image/*" 
        style="display: none;" 
      />
    </div>
    
    <!-- 示例图像区域 -->
    <div v-if="showExamples && examples && examples.length" class="examples-container">
      <h3 v-if="!hideExampleTitle">示例图像</h3>
      
      <!-- 例子图片库 -->
      <ImageGallery 
        :images="examples" 
        :display-count="3" 
        @image-selected="onExampleSelected" 
      />
    </div>
    
    <!-- 处理按钮 -->
    <button 
      v-if="showProcessButton" 
      @click="onProcessClicked" 
      :disabled="isProcessButtonDisabled" 
      class="process-btn"
    >
      {{ isProcessing ? '处理中...' : processButtonText }}
    </button>
    
    <!-- 图片预览模态框 -->
    <teleport to="body">
      <ImagePreviewModal
        :visible="isPreviewVisible"
        :imageUrl="imageUrl"
        @close="isPreviewVisible = false"
      />
    </teleport>
  </div>
</template>

<script>
import ImageGallery from './ImageGallery.vue';
import ImagePreviewModal from './ImagePreviewModal.vue';

export default {
  name: 'ImagePanel',
  components: {
    ImageGallery,
    ImagePreviewModal
  },
  props: {
    title: {
      type: String,
      required: true
    },
    imageUrl: {
      type: String,
      default: null
    },
    placeholder: {
      type: String,
      default: '请上传图像'
    },
    icon: {
      type: String,
      default: '📷'
    },
    showUploadControls: {
      type: Boolean,
      default: true
    },
    showExamples: {
      type: Boolean,
      default: true
    },
    examples: {
      type: Array,
      default: () => []
    },
    statusMessage: {
      type: String,
      default: ''
    },
    showDownloadButton: {
      type: Boolean,
      default: false
    },
    showProcessButton: {
      type: Boolean,
      default: false
    },
    isProcessButtonDisabled: {
      type: Boolean,
      default: false
    },
    isProcessing: {
      type: Boolean,
      default: false
    },
    hideExampleTitle: {
      type: Boolean,
      default: false
    },
    processButtonText: {
      type: String,
      default: '开始处理'
    },
    customFileName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isMobileDevice: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
      isClicking: false,
      isPreviewVisible: false
    };
  },
  computed: {
    isErrorMessage() {
      // 检查状态消息是否为错误消息
      return this.statusMessage && (
        this.statusMessage.includes('失败') || 
        this.statusMessage.includes('错误') || 
        this.statusMessage.includes('请重试')
      );
    }
  },
  methods: {
    // 触发文件选择
    triggerFileSelect() {
      // 添加点击反馈效果
      this.isClicking = true;
      setTimeout(() => {
        this.isClicking = false;
      }, 200);
      
      this.$refs.fileInput.click();
    },
    
    // 文件选择后的处理
    onFileSelected(event) {
      const file = event.target.files[0];
      if (file) {
        this.$emit('file-selected', file);
      }
    },
    
    // 选择示例图像
    onExampleSelected(imagePath) {
      this.$emit('example-selected', imagePath);
    },
    
    // 处理图片加载错误
    handleImageError(event) {
      // 不要在这里尝试重新加载图片，避免额外的GET请求
    },
    
    // 预览大图
    previewImage(event) {
      event.preventDefault();
      event.stopPropagation();
      
      if (!this.imageUrl) return;
      
      // 阻止事件继续传播
      if (event.stopImmediatePropagation) {
        event.stopImmediatePropagation();
      }
      
      this.isPreviewVisible = true;
    },
    
    // 下载图片
    downloadImage(event) {
      event.preventDefault();
      event.stopPropagation();
      
      if (!this.imageUrl) return;
      
      // 创建下载链接
      let imageFileName = '';
      let fileExtension = '.jpg'; // 默认扩展名
      
      // 检测图像格式
      if (this.imageUrl) {
        const url = this.imageUrl.toLowerCase();
        if (url.endsWith('.png') || url.includes('image/png')) {
          fileExtension = '.png';
        } else if (url.endsWith('.gif') || url.includes('image/gif')) {
          fileExtension = '.gif';
        } else if (url.endsWith('.webp') || url.includes('image/webp')) {
          fileExtension = '.webp';
        } else if (url.endsWith('.jpg') || url.endsWith('.jpeg') || url.includes('image/jpeg')) {
          fileExtension = '.jpg';
        }
      }
      
      // 如果提供了自定义文件名，使用它
      if (this.customFileName) {
        imageFileName = this.customFileName;
      } else {
        // 否则根据标题生成文件名
        const baseName = this.title.includes('参考') ? '参考图像' : 
                        (this.title.includes('重光照') ? '重光照图像' : '渲染图像');
        imageFileName = baseName + fileExtension;
      }
      
      // 下载图片 - 确保使用安全连接
      try {
        const a = document.createElement('a');
        a.style.display = 'none';
        
        // 如果是blob URL，直接使用
        if (this.imageUrl.startsWith('blob:')) {
          a.href = this.imageUrl;
          a.download = imageFileName;
          document.body.appendChild(a);
          a.click();
          
          // 清理
          setTimeout(() => {
            document.body.removeChild(a);
          }, 100);
        } 
        // 否则，先获取图片并创建安全的blob URL
        else {
          // 先获取图片再下载，确保HTTPS安全
          fetch(this.imageUrl)
            .then(response => response.blob())
            .then(blob => {
              const secureUrl = URL.createObjectURL(blob);
              a.href = secureUrl;
              a.download = imageFileName;
              document.body.appendChild(a);
              a.click();
              
              // 清理
              setTimeout(() => {
                document.body.removeChild(a);
                URL.revokeObjectURL(secureUrl);
              }, 100);
            })
            .catch(error => {
              // 下载失败，但不输出错误信息
              console.error('下载图片失败:', error);
              
              // 对于移动设备，提供一个备选方案：打开图片在新标签页
              if (this.isMobileDevice) {
                window.open(this.imageUrl, '_blank');
              }
            });
        }
      } catch (error) {
        // 捕获错误
        console.error('下载图片出错:', error);
        
        // 对于移动设备，提供一个备选方案：打开图片在新标签页
        if (this.isMobileDevice) {
          window.open(this.imageUrl, '_blank');
        }
      }
    },
    
    // 处理按钮点击事件
    onProcessClicked() {
      this.$emit('process-clicked');
    }
  }
};
</script>

<style scoped>
.image-panel {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  overflow: hidden;
}

.image-panel:hover {
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

h2 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 20px;
  color: #3a7bd5;
  text-align: center;
  font-weight: 600;
}

h3 {
  font-size: 16px;
  color: #666;
  margin: 15px 0 10px;
}

.image-container {
  flex: 1;
  border: 2px dashed #e0e0e0;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
  min-height: 300px; /* 增加最小高度 */
  height: calc(100% - 60px); /* 减去标题和按钮的高度 */
}

.image-container.clickable {
  cursor: pointer;
}

.image-container.clickable:hover {
  border-color: #3a7bd5;
  background-color: #f0f7ff;
}

.image-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.image-display {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 20px;
  text-align: center;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.7;
}

.placeholder-text {
  font-size: 14px;
  line-height: 1.5;
  max-width: 80%;
}

.placeholder-hint {
  margin-top: 10px;
  font-size: 12px;
  color: #3a7bd5;
}

.examples-container {
  margin-top: 15px;
}

.status-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
  text-align: center;
  width: 100%;
  height: 100%;
}

.error-status {
  color: #e53e3e;
}

.status-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.status-text {
  font-size: 14px;
  line-height: 1.5;
}

.preview-btn {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: linear-gradient(45deg, #3a7bd5, #00d2ff);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  opacity: 0.8;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 10; /* 确保按钮在最上层 */
}

.preview-btn:hover {
  opacity: 1;
  transform: scale(1.1);
}

.preview-icon {
  font-size: 18px;
}

.download-btn {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: linear-gradient(45deg, #3a7bd5, #00d2ff);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  opacity: 0.8;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 10; /* 确保按钮在最上层 */
}

.download-btn:hover {
  opacity: 1;
  transform: scale(1.1);
}

.download-icon {
  font-size: 18px;
}

.process-btn {
  background: linear-gradient(45deg, #3a7bd5, #00d2ff);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 30px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  margin-top: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(58, 123, 213, 0.3);
  text-transform: uppercase;
  letter-spacing: 1px;
  align-self: center; /* 确保按钮居中 */
}

.process-btn:disabled {
  background: linear-gradient(45deg, #ccc, #ddd);
  cursor: not-allowed;
  box-shadow: none;
}

.process-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(58, 123, 213, 0.4);
}

@media (max-width: 768px) {
  .image-panel {
    padding: 15px;
    min-height: 400px; /* 确保移动设备上的最小高度 */
  }
  
  .image-container {
    min-height: 250px; /* 移动设备上的最小高度 */
  }
  
  h2 {
    font-size: 18px;
  }
  
  .placeholder-icon {
    font-size: 36px;
  }
  
  .process-btn {
    padding: 10px 20px;
    font-size: 14px;
  }
  
  /* 移动设备上的按钮样式 */
  .preview-btn,
  .download-btn {
    width: 36px;
    height: 36px;
    bottom: 8px;
  }
  
  .preview-btn {
    left: 8px;
  }
  
  .download-btn {
    right: 8px;
  }
  
  .preview-icon,
  .download-icon {
    font-size: 16px;
  }
}
</style> 