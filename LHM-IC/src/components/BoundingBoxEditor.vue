<template>
  <div class="bounding-box-editor" ref="editorContainer">
    <div 
      class="background-container" 
      ref="backgroundContainer"
      :style="{ position: 'relative', overflow: 'hidden' }"
    >
      <!-- 背景图像 -->
      <img 
        v-if="backgroundImage"
        ref="backgroundImg"
        :src="backgroundImage"
        @load="onBackgroundImageLoad"
        class="background-image"
        alt="背景图像"
      />
      
      <!-- 边界框 -->
      <div 
        v-if="backgroundImage && renderedPoseImage && boundingBoxVisible"
        ref="boundingBox"
        class="bounding-box"
        :style="boundingBoxStyle"
        @mousedown.stop="startDragging"
        @touchstart.prevent.stop="startDragging"
        @wheel.stop="onBoxWheel"
      >
        <!-- 添加渲染后姿势图像在边界框中显示 -->
        <img 
          v-if="renderedPoseImage"
          :src="renderedPoseImage"
          class="overlay-pose-image"
          alt="渲染姿势图像"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BoundingBoxEditor',
  props: {
    backgroundImage: {
      type: String,
      default: null
    },
    renderedPoseImage: {
      type: String,
      default: null
    },
    originalBackgroundChanged: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 边界框的位置和大小（显示用）
      boundingBox: {
        x: 0,
        y: 0,
        width: 0,
        height: 0
      },
      // 背景图像的原始尺寸
      backgroundImageDimensions: {
        width: 0,
        height: 0
      },
      // 渲染后姿势图像的原始尺寸
      renderedPoseImageDimensions: {
        width: 0,
        height: 0
      },
      // 固定的宽高比
      aspectRatio: 1,
      // 拖动相关
      isDragging: false,
      dragStart: { x: 0, y: 0 },
      dragOffset: { x: 0, y: 0 },
      // 边界框是否可见
      boundingBoxVisible: false,
      // 缩放级别
      scale: 1,
      // 背景图缩放和位置信息
      backgroundInfo: {
        scale: 1,
        left: 0,
        top: 0,
        width: 0,
        height: 0
      },
      // 添加初始边界框尺寸
      initialBoxDimensions: {
        width: 0,
        height: 0
      }
    };
  },
  computed: {
    boundingBoxStyle() {
      return {
        position: 'absolute',
        left: `${Math.round(this.boundingBox.x)}px`,
        top: `${Math.round(this.boundingBox.y)}px`,
        width: `${Math.round(this.boundingBox.width)}px`,
        height: `${Math.round(this.boundingBox.height)}px`,
        border: '2px solid red',
        boxSizing: 'border-box',
        cursor: 'move',
        pointerEvents: 'all'
      };
    }
  },
  watch: {
    backgroundImage(newVal, oldVal) {
      // 只有当原始背景图变化时，才重新计算边界框
      if (this.originalBackgroundChanged) {
        this.resetBoxDimensions();
        this.loadBackgroundImage();
      } else {
        // 如果只是背景图URL变化（例如应用了模糊效果），但原始背景图没变
        // 只更新背景图尺寸，不重置边界框位置
        this.loadBackgroundImageDimensions();
      }
    },
    renderedPoseImage() {
      this.resetBoxDimensions();
      // 当渲染后姿势图像变化时，重新加载图像并计算边界框位置
      this.loadPoseImage();
    },
    originalBackgroundChanged(newVal) {
      if (newVal && this.backgroundImage) {
        this.resetBoxDimensions();
        this.loadBackgroundImage();
      }
    }
  },
  mounted() {
    // 添加全局事件监听
    window.addEventListener('mousemove', this.onDrag);
    window.addEventListener('mouseup', this.stopDragging);
    window.addEventListener('touchmove', this.onDrag);
    window.addEventListener('touchend', this.stopDragging);
    
    // 初始化加载图像
    if (this.backgroundImage) {
      this.loadBackgroundImage();
    }
    if (this.renderedPoseImage) {
      this.loadPoseImage();
    }
  },
  beforeUnmount() {
    // 移除全局事件监听
    window.removeEventListener('mousemove', this.onDrag);
    window.removeEventListener('mouseup', this.stopDragging);
    window.removeEventListener('touchmove', this.onDrag);
    window.removeEventListener('touchend', this.stopDragging);
  },
  methods: {
    // 加载背景图像
    loadBackgroundImage() {
      if (!this.backgroundImage) return;
      
      const img = new Image();
      img.onload = () => {
        this.backgroundImageDimensions = {
          width: img.width,
          height: img.height
        };
        
        // 如果两张图都已加载，且原始背景图已改变，则计算初始边界框
        if (this.renderedPoseImageDimensions.width > 0 && 
            this.renderedPoseImageDimensions.height > 0 && 
            this.originalBackgroundChanged) {
          this.calculateInitialBoundingBox();
        }
      };
      img.src = this.backgroundImage;
    },
    
    // 只加载背景图片尺寸，不重置边界框
    loadBackgroundImageDimensions() {
      if (!this.backgroundImage) return;
      
      const img = new Image();
      img.onload = () => {
        this.backgroundImageDimensions = {
          width: img.width,
          height: img.height
        };
        
        // 更新背景图信息，但不重置边界框
        if (this.boundingBoxVisible && this.$refs.backgroundContainer) {
          this.updateBackgroundInfo();
        }
      };
      img.src = this.backgroundImage;
    },
    
    // 更新背景图信息，不重置边界框
    updateBackgroundInfo() {
      if (!this.backgroundImage) return;
      
      const container = this.$refs.backgroundContainer;
      if (!container) return;
      
      const containerRect = container.getBoundingClientRect();
      const containerWidth = containerRect.width;
      const containerHeight = containerRect.height;
      
      const bgWidth = this.backgroundImageDimensions.width;
      const bgHeight = this.backgroundImageDimensions.height;
      
      // 计算背景图像的实际显示尺寸和位置
      const bgDisplayScale = Math.min(
        containerWidth / bgWidth,
        containerHeight / bgHeight
      );
      
      const displayedBgWidth = bgWidth * bgDisplayScale;
      const displayedBgHeight = bgHeight * bgDisplayScale;
      
      // 计算背景图的位置（居中）
      const bgLeft = (containerWidth - displayedBgWidth) / 2;
      const bgTop = (containerHeight - displayedBgHeight) / 2;
      
      // 保存背景图信息
      this.backgroundInfo = {
        scale: bgDisplayScale,
        left: bgLeft,
        top: bgTop,
        width: displayedBgWidth,
        height: displayedBgHeight
      };
      
      // 发送边界框变化事件，更新相对坐标
      this.emitBoundingBoxChange();
    },
    
    // 加载渲染后姿势图像
    loadPoseImage() {
      if (!this.renderedPoseImage) return;
      
      const img = new Image();
      img.onload = () => {
        this.renderedPoseImageDimensions = {
          width: img.width,
          height: img.height
        };
        
        // 计算宽高比并固定
        this.aspectRatio = img.width / img.height;
        
        // 如果背景图已加载且原始背景图已改变，则计算初始边界框
        // 或者，如果背景图已加载但边界框尚未显示，也计算初始边界框
        if (this.backgroundImageDimensions.width > 0 && 
            this.backgroundImageDimensions.height > 0 && 
            (this.originalBackgroundChanged || !this.boundingBoxVisible)) {
          this.calculateInitialBoundingBox();
        } else if (this.boundingBoxVisible) {
          // 如果边界框已经显示，只更新信息
          this.updateBackgroundInfo();
        }
      };
      img.src = this.renderedPoseImage;
    },
    
    // 计算初始边界框位置
    calculateInitialBoundingBox() {
      if (!this.backgroundImage || !this.renderedPoseImage) return;
      
      const bgWidth = this.backgroundImageDimensions.width;
      const bgHeight = this.backgroundImageDimensions.height;
      const poseWidth = this.renderedPoseImageDimensions.width;
      const poseHeight = this.renderedPoseImageDimensions.height;
      
      // 获取显示容器的实际尺寸
      const container = this.$refs.backgroundContainer;
      if (!container) return;
      
      const containerRect = container.getBoundingClientRect();
      const containerWidth = containerRect.width;
      const containerHeight = containerRect.height;
      
      // 计算背景图像的实际显示尺寸和位置
      const bgDisplayScale = Math.min(
        containerWidth / bgWidth,
        containerHeight / bgHeight
      );
      
      const displayedBgWidth = bgWidth * bgDisplayScale;
      const displayedBgHeight = bgHeight * bgDisplayScale;
      
      // 计算背景图的位置（居中）
      const bgLeft = (containerWidth - displayedBgWidth) / 2;
      const bgTop = (containerHeight - displayedBgHeight) / 2;
      
      // 保存背景图信息
      this.backgroundInfo = {
        scale: bgDisplayScale,
        left: bgLeft,
        top: bgTop,
        width: displayedBgWidth,
        height: displayedBgHeight
      };
      
      // 计算边界框的初始大小（不超过渲染后姿势图像的原始大小）
      let boxWidth = Math.min(poseWidth, bgWidth);
      let boxHeight = Math.min(poseHeight, bgHeight);
      
      // 保持宽高比
      const boxAspectRatio = poseWidth / poseHeight;
      if (boxWidth / boxHeight > boxAspectRatio) {
        boxWidth = boxHeight * boxAspectRatio;
      } else {
        boxHeight = boxWidth / boxAspectRatio;
      }

      // 保存初始边界框尺寸
      this.initialBoxDimensions = {
        width: boxWidth,
        height: boxHeight
      };
      
      // 转换为显示尺寸
      const displayedBoxWidth = boxWidth * bgDisplayScale;
      const displayedBoxHeight = boxHeight * bgDisplayScale;
      
      // 计算边界框位置（居中放置）
      const boxX = bgLeft + (displayedBgWidth - displayedBoxWidth) / 2;
      const boxY = bgTop + (displayedBgHeight - displayedBoxHeight) / 2;
      
      // 设置边界框
      this.boundingBox = {
        x: boxX,
        y: boxY,
        width: displayedBoxWidth,
        height: displayedBoxHeight
      };
      
      // 显示边界框
      this.boundingBoxVisible = true;
      
      // 通知父组件更新值
      this.emitBoundingBoxChange();
    },
    
    // 重置边界框尺寸
    resetBoxDimensions() {
      this.boundingBox = {
        x: 0,
        y: 0,
        width: 0,
        height: 0
      };
      this.boundingBoxVisible = false;
    },
    
    // 背景图加载完成后的事件
    onBackgroundImageLoad() {
      if (this.renderedPoseImage) {
        // 当原始背景图变化时，才重新计算边界框
        if (this.originalBackgroundChanged) {
          this.calculateInitialBoundingBox();
        } else {
          // 如果只是应用了模糊效果，只更新背景信息，保持边界框位置
          this.updateBackgroundInfo();
        }
      }
    },
    
    // 开始拖动
    startDragging(event) {
      event.preventDefault();
      
      this.isDragging = true;
      
      // 获取鼠标/触摸位置
      const position = this.getEventPosition(event);
      
      // 获取边界框的位置
      const boundingBoxRect = this.$refs.boundingBox.getBoundingClientRect();
      const containerRect = this.$refs.backgroundContainer.getBoundingClientRect();
      
      // 计算偏移量
      this.dragStart = {
        x: position.x,
        y: position.y
      };
      
      this.dragOffset = {
        x: position.x - (boundingBoxRect.left - containerRect.left),
        y: position.y - (boundingBoxRect.top - containerRect.top)
      };
    },
    
    // 拖动过程
    onDrag(event) {
      if (!this.isDragging) return;
      
      event.preventDefault();
      
      // 获取当前位置
      const position = this.getEventPosition(event);
      
      // 计算新位置
      let newX = position.x - this.dragOffset.x;
      let newY = position.y - this.dragOffset.y;
      
      // 确保边界框左上角不会超出背景图
      const minX = this.backgroundInfo.left;
      const minY = this.backgroundInfo.top;
      // 允许右边界和下边界超出背景图
      
      // 限制左上角的位置
      this.boundingBox.x = Math.max(minX, newX);
      this.boundingBox.y = Math.max(minY, newY);
      
      // 通知父组件更新值
      this.emitBoundingBoxChange();
    },
    
    // 停止拖动
    stopDragging() {
      this.isDragging = false;
    },
    
    // 只有在边界框内滚动才会缩放
    onBoxWheel(event) {
      event.preventDefault();
      event.stopPropagation();
      
      // 执行缩放
      this.handleWheelZoom(event);
    },
    
    // 处理滚轮缩放
    handleWheelZoom(event) {
      if (!this.backgroundImage || !this.renderedPoseImage) return;
      
      // 计算缩放比例
      const scaleFactor = event.deltaY < 0 ? 1.1 : 0.9;
      this.scale *= scaleFactor;
      
      // 限制最小和最大缩放值
      this.scale = Math.max(0.1, Math.min(this.scale, 5));
      
      // 获取边界框的中心点
      const centerX = this.boundingBox.x + this.boundingBox.width / 2;
      const centerY = this.boundingBox.y + this.boundingBox.height / 2;
      
      // 计算新尺寸，保持宽高比
      let newWidth = this.boundingBox.width * scaleFactor;
      let newHeight = newWidth / this.aspectRatio;
      
      // 设置为初始边界框尺寸的10倍作为最大限制
      const maxDisplayWidth = this.initialBoxDimensions.width * this.backgroundInfo.scale * 10;
      const maxDisplayHeight = this.initialBoxDimensions.height * this.backgroundInfo.scale * 10;
      
      if (newWidth > maxDisplayWidth) {
        newWidth = maxDisplayWidth;
        newHeight = newWidth / this.aspectRatio;
      }
      if (newHeight > maxDisplayHeight) {
        newHeight = maxDisplayHeight;
        newWidth = newHeight * this.aspectRatio;
      }
      
      // 确保边界框不会过小
      if (newWidth < 20 || newHeight < 20) return;
      
      // 计算新位置，保持中心点不变
      let newX = centerX - newWidth / 2;
      let newY = centerY - newHeight / 2;
      
      // 确保边界框左上角不会超出背景图
      const minX = this.backgroundInfo.left;
      const minY = this.backgroundInfo.top;
      // 允许右边界和下边界超出背景图
      
      // 限制左上角的位置
      newX = Math.max(minX, newX);
      newY = Math.max(minY, newY);
      
      // 更新边界框
      this.boundingBox = {
        x: newX,
        y: newY,
        width: newWidth,
        height: newHeight
      };
      
      // 通知父组件更新值
      this.emitBoundingBoxChange();
    },
    
    // 获取事件位置
    getEventPosition(event) {
      const containerRect = this.$refs.backgroundContainer.getBoundingClientRect();
      
      let clientX, clientY;
      
      if (event.touches && event.touches.length) {
        // 触摸事件
        clientX = event.touches[0].clientX;
        clientY = event.touches[0].clientY;
      } else {
        // 鼠标事件
        clientX = event.clientX;
        clientY = event.clientY;
      }
      
      return {
        x: clientX - containerRect.left,
        y: clientY - containerRect.top
      };
    },
    
    // 更新边界框
    updateBoundingBox() {
      // 确保维持宽高比
      if (this.boundingBox.width !== 0 && this.boundingBox.height !== 0) {
        const newAspectRatio = this.boundingBox.width / this.boundingBox.height;
        
        if (Math.abs(newAspectRatio - this.aspectRatio) > 0.01) {
          // 根据当前宽度计算高度
          this.boundingBox.height = this.boundingBox.width / this.aspectRatio;
        }
      }
      
      // 通知父组件更新值
      this.emitBoundingBoxChange();
    },
    
    // 向父组件发送边界框变化事件
    emitBoundingBoxChange() {
      // 计算相对于背景图左上角的实际坐标和尺寸
      const relativeX = (this.boundingBox.x - this.backgroundInfo.left) / this.backgroundInfo.scale;
      const relativeY = (this.boundingBox.y - this.backgroundInfo.top) / this.backgroundInfo.scale;
      const actualWidth = this.boundingBox.width / this.backgroundInfo.scale;
      const actualHeight = this.boundingBox.height / this.backgroundInfo.scale;
      
      // 发送实际尺寸
      this.$emit('box-change', {
        x: Math.round(relativeX),
        y: Math.round(relativeY),
        width: Math.round(actualWidth),
        height: Math.round(actualHeight)
      });
    }
  }
};
</script>

<style scoped>
.bounding-box-editor {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.background-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f2f5;
  position: relative;
  overflow: hidden;
  min-height: 350px; /* 增加最小高度，与父容器保持一致 */
}

.background-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.bounding-box {
  position: absolute;
  border: 3px solid #4CAF50;
  box-sizing: border-box;
  cursor: move;
  box-shadow: 0 0 0 2000px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.bounding-box:hover {
  border-color: #3a7bd5;
  box-shadow: 0 0 0 2000px rgba(0, 0, 0, 0.4);
}

.overlay-pose-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  opacity: 0.3;/*透明度*/
  transition: opacity 0.3s ease;
}

.bounding-box:hover .overlay-pose-image {
  opacity: 0.5;
}

/* 添加调整大小手柄 */
.resize-handle {
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: #4CAF50;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.resize-handle.top-left {
  top: -6px;
  left: -6px;
  cursor: nwse-resize;
}

.resize-handle.top-right {
  top: -6px;
  right: -6px;
  cursor: nesw-resize;
}

.resize-handle.bottom-left {
  bottom: -6px;
  left: -6px;
  cursor: nesw-resize;
}

.resize-handle.bottom-right {
  bottom: -6px;
  right: -6px;
  cursor: nwse-resize;
}

@media (max-width: 768px) {
  .background-container {
    min-height: 250px; /* 移动设备上的最小高度 */
  }

  .bounding-box {
    border-width: 4px;
  }
  
  .resize-handle {
    width: 16px;
    height: 16px;
  }
  
  .resize-handle.top-left {
    top: -8px;
    left: -8px;
  }
  
  .resize-handle.top-right {
    top: -8px;
    right: -8px;
  }
  
  .resize-handle.bottom-left {
    bottom: -8px;
    left: -8px;
  }
  
  .resize-handle.bottom-right {
    bottom: -8px;
    right: -8px;
  }
}
</style> 