<template>
  <div class="background-gallery">
    <div class="gallery-header">
      <h3 class="gallery-title">背景模板</h3>
      <button class="custom-bg-button" @click="$emit('custom-background-click')">自定义背景图</button>
    </div>
    <div class="gallery-container">
      <button class="scroll-button left" @click="scrollLeft">
        <span>&lt;</span>
      </button>
      <div class="gallery-scroll" ref="galleryScroll">
        <div v-if="isLoading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>加载背景模板中...</p>
        </div>
        <div v-else-if="hasError" class="error-container">
          <p>{{ errorMessage }}</p>
          <button @click="loadBackgroundImages" class="retry-button">重试</button>
        </div>
        <template v-else>
          <div 
            v-for="(image, index) in backgroundImages" 
            :key="index" 
            class="gallery-card"
            @click="selectBackground(image)"
            :class="{ 'active': selectedImage === image }"
          >
            <img :src="image.url" :alt="`背景模板 ${index + 1}`" class="gallery-image" />
          </div>
        </template>
      </div>
      <button class="scroll-button right" @click="scrollRight">
        <span>&gt;</span>
      </button>
    </div>
    <!-- 添加提示消息 -->
    <div v-if="showNoImageAlert" class="alert-message">
      请先上传您摆好姿势的照片哦
    </div>
  </div>
</template>

<script>
export default {
  name: 'BackgroundGallery',
  props: {
    // 添加人物图像URL作为属性
    renderedPoseImage: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      backgroundImages: [],
      selectedImage: null,
      scrollAmount: 300, // 每次滚动的像素数
      isLoading: true,   // 加载状态
      hasError: false,   // 错误状态
      errorMessage: '加载背景模板失败，请重试', // 错误信息
      showNoImageAlert: false, // 控制提示消息的显示
    };
  },
  mounted() {
    // 加载背景图片
    this.loadBackgroundImages();
  },
  methods: {
    // 加载背景图片
    async loadBackgroundImages() {
      // 重置状态
      this.isLoading = true;
      this.hasError = false;
      
      try {
        // 背景图片路径 - 修改为正确的公共路径
        const basePath = '/images/background/';
        // 根据目录中的文件创建图片列表
        const imageFiles = [
          '0.png', '1.png', '2.png', '3.png', '4.png',
          '5.png', '6.png', '7.png', '8.png', '9.png',
          '10.png', '11.jpg'
        ];
        
        // 创建图片对象并预加载
        const imagePromises = imageFiles.map(async (file) => {
          const url = basePath + file;
          
          // 预加载图片
          return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve({
              url,
              filename: file,
              loaded: true
            });
            img.onerror = () => reject(new Error(`无法加载图片: ${file}`));
            img.src = url;
          });
        });
        
        // 等待所有图片加载完成
        const loadedImages = await Promise.allSettled(imagePromises);
        
        // 过滤出成功加载的图片
        this.backgroundImages = loadedImages
          .filter(result => result.status === 'fulfilled')
          .map(result => result.value);
        
        // 如果没有成功加载任何图片，抛出错误
        if (this.backgroundImages.length === 0) {
          throw new Error('无法加载任何背景模板');
        }
      } catch (error) {
        console.error('Error loading background images:', error);
        this.hasError = true;
        this.errorMessage = '加载背景模板失败，请重试';
      } finally {
        this.isLoading = false;
      }
    },
    
    // 选择背景图片
    async selectBackground(image) {
      try {
        // 检查是否已上传人物图像
        if (!this.renderedPoseImage) {
          // 显示提示消息
          this.showNoImageAlert = true;
          
          // 3秒后自动隐藏提示
          setTimeout(() => {
            this.showNoImageAlert = false;
          }, 3000);
          
          return;
        }
        
        // 隐藏提示消息
        this.showNoImageAlert = false;
        
        // 设置选中状态
        this.selectedImage = image;
        
        // 获取图片文件
        const response = await fetch(image.url);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
        }
        
        const blob = await response.blob();
        const file = new File([blob], image.filename, { type: blob.type });
        
        // 触发选择事件，将文件发送给父组件
        this.$emit('background-selected', file);
      } catch (error) {
        console.error('Error selecting background image:', error);
      }
    },
    
    // 向左滚动
    scrollLeft() {
      if (this.$refs.galleryScroll) {
        this.$refs.galleryScroll.scrollLeft -= this.scrollAmount;
      }
    },
    
    // 向右滚动
    scrollRight() {
      if (this.$refs.galleryScroll) {
        this.$refs.galleryScroll.scrollLeft += this.scrollAmount;
      }
    }
  }
};
</script>

<style scoped>
.background-gallery {
  width: 100%;
  margin: 20px 0;
  padding: 15px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  position: relative; /* 添加相对定位，用于提示消息的绝对定位 */
}

.gallery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.gallery-title {
  font-size: 18px;
  color: #333;
  font-weight: 600;
  margin: 0;
}

.custom-bg-button {
  background-color: #f5f7fa;
  color: #666;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 30px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.custom-bg-button:hover {
  background-color: #e8eaed;
  color: #333;
}

/* 添加提示消息样式 */
.alert-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  z-index: 100;
  font-size: 16px;
  animation: fadeIn 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -60%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

.gallery-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.gallery-scroll {
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
  padding: 10px 0;
  width: 100%;
  gap: 15px;
  min-height: 140px; /* 确保在加载时有足够的高度 */
}

/* 隐藏滚动条 */
.gallery-scroll::-webkit-scrollbar {
  display: none;
}

.gallery-card {
  flex: 0 0 auto;
  width: 180px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.gallery-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.gallery-card.active {
  border-color: #3a7bd5;
  box-shadow: 0 0 0 2px rgba(58, 123, 213, 0.3);
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-card:hover .gallery-image {
  transform: scale(1.05);
}

.scroll-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.scroll-button:hover {
  background-color: #f0f0f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.scroll-button.left {
  left: -18px;
}

.scroll-button.right {
  right: -18px;
}

.scroll-button span {
  font-size: 18px;
  font-weight: bold;
  color: #555;
}

/* 加载和错误状态样式 */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 120px;
  color: #666;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(58, 123, 213, 0.3);
  border-radius: 50%;
  border-top-color: #3a7bd5;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.retry-button {
  margin-top: 10px;
  padding: 6px 12px;
  background-color: #3a7bd5;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #2c5ea0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .gallery-card {
    width: 140px;
    height: 90px;
  }
  
  .scroll-button {
    width: 30px;
    height: 30px;
  }
  
  .scroll-button span {
    font-size: 16px;
  }
  
  .loading-container,
  .error-container {
    height: 90px;
  }
}
</style> 