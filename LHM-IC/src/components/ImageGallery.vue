<template>
  <div class="gallery-container">
    <h3 class="gallery-title">{{ title }}</h3>
    <div class="gallery-grid">
      <div 
        v-for="(image, index) in displayImages" 
        :key="index" 
        class="gallery-item"
        @click="onImageSelect(image)"
      >
        <img :src="image" :alt="`示例${index+1}`" class="gallery-image" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImageGallery',
  props: {
    title: {
      type: String,
      default: '示例图片'
    },
    images: {
      type: Array,
      required: true
    },
    displayCount: {
      type: Number,
      default: 6
    }
  },
  computed: {
    displayImages() {
      // 如果图片数量少于显示数量，则全部显示
      if (this.images.length <= this.displayCount) {
        return this.images;
      }
      
      // 随机选择指定数量的图片
      const shuffled = [...this.images].sort(() => 0.5 - Math.random());
      return shuffled.slice(0, this.displayCount);
    }
  },
  methods: {
    onImageSelect(imageUrl) {
      this.$emit('image-selected', imageUrl);
    }
  }
}
</script>

<style scoped>
.gallery-container {
  margin-bottom: 15px;
}

.gallery-title {
  font-size: 16px;
  margin: 0 0 12px 0;
  color: #3a7bd5;
  font-weight: 600;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.gallery-item {
  cursor: pointer;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.gallery-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: #3a7bd5;
}

.gallery-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0) 70%, rgba(0,0,0,0.4) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:hover::after {
  opacity: 1;
}

.gallery-image {
  width: 100%;
  height: 80px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-image {
  transform: scale(1.05);
}

@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .gallery-image {
    height: 70px;
  }
}
</style> 