<template>
  <div class="image-preview-modal" v-if="visible" @click.self="closeModal">
    <div class="modal-content" @click.stop>
      <div class="image-container" ref="imageContainer">
        <img 
          :src="imageUrl" 
          alt="预览图片" 
          class="preview-image"
          ref="previewImage"
          @wheel.prevent.stop="handleWheel"
          @mousedown.prevent.stop="startDrag"
          :style="{
            transform: `scale(${scale}) translate(${translateX}px, ${translateY}px)`,
            cursor: isDragging ? 'grabbing' : 'grab'
          }"
        />
      </div>
      <button class="close-button" @click.stop="closeModal">×</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImagePreviewModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    imageUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      scale: 1,
      translateX: 0,
      translateY: 0,
      isDragging: false,
      startX: 0,
      startY: 0,
      lastTranslateX: 0,
      lastTranslateY: 0
    };
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 当模态框显示时，重置状态
        this.scale = 1;
        this.translateX = 0;
        this.translateY = 0;
        this.isDragging = false;
        // 添加键盘事件监听
        window.addEventListener('keydown', this.handleKeyDown);
        // 添加全局鼠标事件监听
        window.addEventListener('mousemove', this.handleDrag);
        window.addEventListener('mouseup', this.stopDrag);
      } else {
        // 当模态框隐藏时，移除事件监听
        window.removeEventListener('keydown', this.handleKeyDown);
        window.removeEventListener('mousemove', this.handleDrag);
        window.removeEventListener('mouseup', this.stopDrag);
      }
    }
  },
  methods: {
    closeModal() {
      this.$emit('close');
    },
    handleWheel(event) {
      // 阻止事件冒泡和默认行为
      event.stopPropagation();
      event.preventDefault();
      
      // 根据滚轮方向放大或缩小图片
      const delta = event.deltaY > 0 ? -0.1 : 0.1;
      this.scale = Math.max(0.1, Math.min(5, this.scale + delta));
    },
    handleKeyDown(event) {
      // 按ESC键关闭模态框
      if (event.key === 'Escape') {
        this.closeModal();
      }
    },
    startDrag(event) {
      // 阻止事件冒泡和默认行为
      event.stopPropagation();
      event.preventDefault();
      
      this.isDragging = true;
      this.startX = event.clientX;
      this.startY = event.clientY;
      this.lastTranslateX = this.translateX;
      this.lastTranslateY = this.translateY;
    },
    handleDrag(event) {
      if (!this.isDragging) return;
      
      // 阻止事件冒泡
      event.stopPropagation();
      
      // 计算鼠标移动距离
      const dx = event.clientX - this.startX;
      const dy = event.clientY - this.startY;
      
      // 更新位移量，除以当前缩放比例以保持一致的拖动感觉
      this.translateX = this.lastTranslateX + dx / this.scale;
      this.translateY = this.lastTranslateY + dy / this.scale;
    },
    stopDrag(event) {
      if (event) {
        event.stopPropagation();
      }
      this.isDragging = false;
    }
  },
  beforeUnmount() {
    // 确保在组件销毁时移除事件监听
    window.removeEventListener('keydown', this.handleKeyDown);
    window.removeEventListener('mousemove', this.handleDrag);
    window.removeEventListener('mouseup', this.stopDrag);
  }
};
</script>

<style scoped>
.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
  /* 将事件隔离到模态框层 */
  isolation: isolate;
}

.modal-content {
  position: relative;
  width: 1280px;
  height: 675px;
  background-color: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  /* 阻止事件传播到下层 */
  pointer-events: auto;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.05s ease;
  transform-origin: center center;
  user-select: none;
  /* 确保图片接收所有指针事件 */
  pointer-events: auto;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 36px;
  height: 36px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@media (max-width: 1280px) {
  .modal-content {
    width: 95vw;
    height: calc(95vw * 9 / 16);
    max-height: 95vh;
    max-width: calc(95vh * 16 / 9);
  }
}

@media (max-width: 768px) {
  .close-button {
    width: 32px;
    height: 32px;
    font-size: 20px;
  }
}
</style> 