<template>
  <div class="background-image-editor">
    <div class="editor-header">
      <h2>背景图</h2>
    </div>
    
    <div class="editor-content" v-if="canShowEditor">
      <BoundingBoxEditor 
        :backgroundImage="displayedBackgroundImage" 
        :renderedPoseImage="renderedPoseImage"
        :originalBackgroundChanged="originalBackgroundChanged"
        @box-change="onBoundingBoxChange"
      />
    </div>
    
    <div class="upload-section" v-if="!canShowEditor">
      <div class="upload-area">
        <div v-if="displayedBackgroundImage" class="image-preview">
          <img :src="displayedBackgroundImage" alt="背景图像" class="preview-img" />
        </div>
        <div v-else class="placeholder">
          <div class="placeholder-icon">🖼️</div>
          <div class="placeholder-text">请选择背景模板或上传自定义图像</div>
        </div>
      </div>
    </div>
    
    <input 
      type="file" 
      ref="fileInput" 
      @change="onFileSelected" 
      accept="image/*" 
      style="display: none;" 
    />
    
    <!-- 添加滑动条和模糊按钮 -->
    <div class="controls-section" v-if="displayedBackgroundImage">
      <div class="slider-container">
        <input 
          type="range" 
          class="blur-slider" 
          min="1" 
          max="211" 
          step="10" 
          v-model="blurValue"
        />
        <div class="slider-value">{{ blurValue }}</div>
      </div>
      <div class="blur-button-container">
        <button class="blur-button" @click="blurBackground" :disabled="isProcessing">
          {{ isProcessing ? '处理中...' : '模糊背景' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import BoundingBoxEditor from './BoundingBoxEditor.vue';

export default {
  name: 'BackgroundImageEditor',
  components: {
    BoundingBoxEditor
  },
  props: {
    renderedPoseImage: {
      type: String,
      default: null
    },
    canUpload: {
      type: Boolean,
      default: true
    },
    backgroundFile: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      backgroundImage: null,         // 原始背景图URL
      displayedBackgroundImage: null, // 显示的背景图URL（可能是模糊后的）
      originalImageData: null,        // 保存原始图像数据
      backgroundImageFile: null,      // 背景图文件
      boxDimensions: null,
      blurValue: 1,                  // 默认值为1
      isProcessing: false,           // 处理状态标志
      cvLoaded: false,               // OpenCV.js加载状态
      originalBackgroundChanged: false // 用于跟踪原始背景图是否改变
    };
  },
  computed: {
    canShowEditor() {
      // 当背景图和渲染后姿势图像都存在时，显示编辑器
      return this.displayedBackgroundImage && this.renderedPoseImage;
    }
  },
  mounted() {
    // 加载OpenCV.js
    this.loadOpenCV();
  },
  methods: {
    // 加载OpenCV.js
    loadOpenCV() {
      if (window.cv) {
        this.cvLoaded = true;
        return;
      }
      
      // 创建script元素
      const script = document.createElement('script');
      script.src = 'https://docs.opencv.org/4.5.5/opencv.js';
      script.async = true;
      script.onload = () => {
        this.cvLoaded = true;
      };
      script.onerror = (err) => {
        // OpenCV.js 加载失败
      };
      
      // 添加到文档中
      document.body.appendChild(script);
    },
    
    // 触发文件选择
    triggerFileSelect() {
      if (!this.canUpload) return;
      this.$refs.fileInput.click();
    },
    
    // 文件选择处理
    onFileSelected(event) {
      const file = event.target.files[0];
      if (file) {
        // 清理之前的资源
        this.clearResources();
        
        this.backgroundImageFile = file;
        const imageUrl = URL.createObjectURL(file);
        this.backgroundImage = imageUrl;
        this.displayedBackgroundImage = imageUrl;
        
        // 标记原始背景图已改变
        this.originalBackgroundChanged = true;
        
        // 保存原始图像数据
        this.saveOriginalImageData(imageUrl);
        
        this.$emit('file-selected', file);
      }
    },
    
    // 保存原始图像数据
    saveOriginalImageData(imageUrl) {
      const img = new Image();
      img.onload = () => {
        // 创建canvas来获取图像数据
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        
        // 保存原始图像数据
        this.originalImageData = {
          width: img.width,
          height: img.height,
          imageUrl: imageUrl,
          mimeType: this.backgroundImageFile.type, // 保存原始MIME类型
          fileName: this.backgroundImageFile.name  // 保存原始文件名
        };
      };
      img.src = imageUrl;
    },
    
    // 模糊背景
    blurBackground() {
      if (!this.cvLoaded || !this.originalImageData || this.isProcessing) {
        return;
      }
      
      this.isProcessing = true;
      
      // 确保blur_kernel_size是奇数
      let blur_kernel_size = parseInt(this.blurValue);
      if (blur_kernel_size % 2 === 0) {
        blur_kernel_size += 1;
      }
      
      // 使用原始图像数据
      const img = new Image();
      img.onload = () => {
        try {
          // 创建canvas
          const canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0);
          
          // 获取图像数据
          const imgData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          
          // 转换为OpenCV格式
          const src = cv.matFromImageData(imgData);
          const dst = new cv.Mat();
          
          // 应用高斯模糊
          const ksize = new cv.Size(blur_kernel_size, blur_kernel_size);
          cv.GaussianBlur(src, dst, ksize, 0, 0, cv.BORDER_DEFAULT);
          
          // 将结果转回canvas
          cv.imshow(canvas, dst);
          
          // 释放内存
          src.delete();
          dst.delete();
          
          // 获取原始MIME类型
          const originalMimeType = this.originalImageData.mimeType || 'image/jpeg';
          const originalFileName = this.originalImageData.fileName || 'background.jpg';
          const fileExtension = originalFileName.split('.').pop();
          
          // 更新显示的背景图
          const blurredImageUrl = canvas.toDataURL(originalMimeType);
          
          // 如果之前有URL，先释放
          if (this.displayedBackgroundImage !== this.backgroundImage) {
            URL.revokeObjectURL(this.displayedBackgroundImage);
          }
          
          // 标记原始背景图没有改变（只是应用了模糊）
          this.originalBackgroundChanged = false;
          
          this.displayedBackgroundImage = blurredImageUrl;
          
          // 更新背景图文件，保持原始格式
          canvas.toBlob((blob) => {
            // 创建与原始文件同类型的File对象
            const blurredFile = new File(
              [blob], 
              `blurred_${originalFileName}`, 
              { type: originalMimeType }
            );
            this.backgroundImageFile = blurredFile;
            this.$emit('file-selected', blurredFile);
          }, originalMimeType); // 指定与原图相同的MIME类型
          
        } catch (error) {
          // 出错时保持原图
        } finally {
          this.isProcessing = false;
        }
      };
      
      img.onerror = () => {
        this.isProcessing = false;
      };
      
      // 使用原始图像URL
      img.src = this.originalImageData.imageUrl;
    },
    
    // 边界框变化处理
    onBoundingBoxChange(dimensions) {
      this.boxDimensions = dimensions;
      this.$emit('box-change', dimensions);
    },
    
    // 清除资源
    clearResources() {
      if (this.backgroundImage && this.backgroundImage.startsWith('blob:')) {
        URL.revokeObjectURL(this.backgroundImage);
      }
      
      if (this.displayedBackgroundImage && 
          this.displayedBackgroundImage !== this.backgroundImage && 
          this.displayedBackgroundImage.startsWith('blob:')) {
        URL.revokeObjectURL(this.displayedBackgroundImage);
      }
      
      this.backgroundImage = null;
      this.displayedBackgroundImage = null;
      this.originalImageData = null;
      this.backgroundImageFile = null;
      this.boxDimensions = null;
      this.originalBackgroundChanged = false; // 重置标志
    },
    
    // 处理外部传入的文件
    async handleExternalFile(file) {
      // 避免重复处理相同的文件
      if (this.backgroundImageFile && 
          this.backgroundImageFile.name === file.name && 
          this.backgroundImageFile.size === file.size) {
        return;
      }
      
      // 清理之前的资源
      this.clearResources();
      
      this.backgroundImageFile = file;
      const imageUrl = URL.createObjectURL(file);
      this.backgroundImage = imageUrl;
      this.displayedBackgroundImage = imageUrl;
      
      // 标记原始背景图已改变
      this.originalBackgroundChanged = true;
      
      // 保存原始图像数据
      this.saveOriginalImageData(imageUrl);
      
      // 触发文件选择事件，通知父组件
      this.$emit('file-selected', file);
    }
  },
  beforeUnmount() {
    this.clearResources();
  },
  watch: {
    // 当渲染后姿势图像变为空时，清除背景图
    renderedPoseImage(newVal) {
      if (!newVal) {
        this.clearResources();
      }
    },
    // 监听模糊值变化
    blurValue(newVal) {
      // 触发事件，将滑动条的值传递给父组件
      this.$emit('blur-value-change', newVal);
    },
    // 监听backgroundFile变化
    backgroundFile: {
      handler(newFile) {
        if (newFile) {
          this.handleExternalFile(newFile);
        }
      },
      immediate: true
    }
  }
};
</script>

<style scoped>
.background-image-editor {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  overflow: hidden;
}

.background-image-editor:hover {
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.editor-header {
  margin-bottom: 15px;
}

.editor-header h2 {
  margin: 0;
  font-size: 20px;
  color: #3a7bd5;
  text-align: center;
  font-weight: 600;
}

.editor-content {
  flex: 1;
  overflow: hidden;
  border-radius: 8px;
  background-color: #f5f7fa;
  margin-bottom: 15px;
  height: calc(100% - 120px); /* 减去标题、控制区和按钮的高度 */
  min-height: 350px; /* 增加最小高度 */
  display: flex; /* 确保子元素可以使用flex布局 */
  flex-direction: column; /* 垂直排列子元素 */
}

.upload-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: calc(100% - 120px); /* 减去标题、控制区和按钮的高度 */
  min-height: 350px; /* 增加最小高度 */
}

.upload-area {
  width: 100%;
  height: 100%;
  border: 2px dashed #e0e0e0;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
}

.image-preview {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.preview-img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 20px;
  text-align: center;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.7;
}

.placeholder-text {
  font-size: 14px;
  line-height: 1.5;
  max-width: 80%;
}

.controls-section {
  margin-top: 15px;
  margin-bottom: 15px;
}

.slider-container {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.blur-slider {
  flex: 1;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: #e0e0e0;
  outline: none;
  border-radius: 3px;
}

.blur-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: linear-gradient(45deg, #3a7bd5, #00d2ff);
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.blur-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: linear-gradient(45deg, #3a7bd5, #00d2ff);
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  border: none;
}

.slider-value {
  margin-left: 15px;
  font-size: 14px;
  color: #666;
  min-width: 30px;
  text-align: center;
}

.blur-button-container {
  display: flex;
  justify-content: center;
}

.blur-button {
  background: linear-gradient(45deg, #3a7bd5, #00d2ff);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 30px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(58, 123, 213, 0.3);
}

.blur-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(58, 123, 213, 0.4);
}

.blur-button:disabled {
  background: linear-gradient(45deg, #ccc, #ddd);
  cursor: not-allowed;
  box-shadow: none;
}

@media (max-width: 768px) {
  .background-image-editor {
    padding: 15px;
    min-height: 400px; /* 确保移动设备上的最小高度 */
  }
  
  .editor-content,
  .upload-section {
    min-height: 250px; /* 移动设备上的最小高度 */
  }
  
  .editor-header h2 {
    font-size: 18px;
  }
  
  .placeholder-icon {
    font-size: 36px;
  }
  
  .blur-button {
    padding: 8px 16px;
    font-size: 14px;
  }
}
</style> 