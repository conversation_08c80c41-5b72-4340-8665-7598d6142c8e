<template>
  <div class="image-cropper">
    <div 
      class="cropper-container" 
      ref="cropperContainer"
      :style="{ position: 'relative' }"
    >
      <!-- 原始图像 -->
      <img 
        v-if="imageUrl"
        ref="originalImg"
        :src="imageUrl"
        @load="onImageLoad"
        class="original-image"
        alt="请确认框选人物轮廓"
      />
      
      <!-- 裁剪框 -->
      <div 
        v-if="imageUrl && cropBoxVisible"
        ref="cropBox"
        class="crop-box"
        :style="cropBoxStyle"
        @mousedown.stop="startDragging"
        @touchstart.prevent.stop="startDragging"
      >
        <!-- 添加边界手柄 -->
        <div class="handle top" @mousedown.stop="startResizing('top')"></div>
        <div class="handle bottom" @mousedown.stop="startResizing('bottom')"></div>
        <div class="handle left" @mousedown.stop="startResizing('left')"></div>
        <div class="handle right" @mousedown.stop="startResizing('right')"></div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="cropper-controls">
      <button 
        @click="resetToDefault" 
        class="reset-button"
        :disabled="!imageUrl || !originalImageStored"
      >恢复默认选框</button>
      <button 
        @click="confirmCrop" 
        class="confirm-button"
        :disabled="!imageUrl || !cropBoxVisible"
      >确认</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImageCropper',
  props: {
    imageUrl: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      // 裁剪框的位置和大小
      cropBox: {
        x: 0,
        y: 0,
        width: 0,
        height: 0
      },
      // 图像的原始尺寸
      originalImageDimensions: {
        width: 0,
        height: 0
      },
      // 拖动相关
      isDragging: false,
      dragStart: { x: 0, y: 0 },
      dragOffset: { x: 0, y: 0 },
      // 调整大小相关
      isResizing: false,
      resizeDirection: null,
      // 裁剪框是否可见
      cropBoxVisible: false,
      // 缩放级别
      scale: 1,
      // 图像缩放和位置信息
      imageInfo: {
        scale: 1,
        left: 0,
        top: 0,
        width: 0,
        height: 0
      },
      // 原始图像是否已存储
      originalImageStored: false,
      // 原始图像URL
      originalImageUrl: null,
      // 是否已裁剪
      isCropped: false
    };
  },
  computed: {
    cropBoxStyle() {
      return {
        position: 'absolute',
        left: `${Math.round(this.cropBox.x)}px`,
        top: `${Math.round(this.cropBox.y)}px`,
        width: `${Math.round(this.cropBox.width)}px`,
        height: `${Math.round(this.cropBox.height)}px`,
        border: '2px solid #4CAF50',
        boxSizing: 'border-box',
        cursor: 'move',
        pointerEvents: 'all',
        boxShadow: '0 0 0 2000px rgba(0, 0, 0, 0.5)',
        outline: '1px solid rgba(255, 255, 255, 0.7)'
      };
    }
  },
  watch: {
    imageUrl(newVal) {
      if (newVal) {
        this.loadImage();
        // 如果是新图像，存储原始图像URL
        if (!this.originalImageStored || this.originalImageUrl !== newVal) {
          this.originalImageUrl = newVal;
          this.originalImageStored = true;
          this.isCropped = false;
        }
      } else {
        this.resetCropBox();
        this.originalImageStored = false;
        this.originalImageUrl = null;
        this.isCropped = false;
      }
    }
  },
  mounted() {
    // 添加全局事件监听
    window.addEventListener('mousemove', this.onMouseMove);
    window.addEventListener('mouseup', this.stopInteraction);
    window.addEventListener('touchmove', this.onMouseMove);
    window.addEventListener('touchend', this.stopInteraction);
    
    // 初始化加载图像
    if (this.imageUrl) {
      this.loadImage();
      this.originalImageUrl = this.imageUrl;
      this.originalImageStored = true;
    }
  },
  beforeUnmount() {
    // 移除全局事件监听
    window.removeEventListener('mousemove', this.onMouseMove);
    window.removeEventListener('mouseup', this.stopInteraction);
    window.removeEventListener('touchmove', this.onMouseMove);
    window.removeEventListener('touchend', this.stopInteraction);
  },
  methods: {
    // 加载图像
    loadImage() {
      if (!this.imageUrl) return;
      
      const img = new Image();
      img.onload = () => {
        this.originalImageDimensions = {
          width: img.width,
          height: img.height
        };
        
        this.calculateInitialCropBox();
      };
      img.src = this.imageUrl;
    },
    
    // 计算初始裁剪框位置
    calculateInitialCropBox() {
      if (!this.imageUrl) return;
      
      const imgWidth = this.originalImageDimensions.width;
      const imgHeight = this.originalImageDimensions.height;
      
      // 获取显示容器的实际尺寸
      const container = this.$refs.cropperContainer;
      if (!container) return;
      
      const containerRect = container.getBoundingClientRect();
      const containerWidth = containerRect.width;
      const containerHeight = containerRect.height;
      
      // 计算图像的实际显示尺寸和位置
      const imgDisplayScale = Math.min(
        containerWidth / imgWidth,
        containerHeight / imgHeight
      );
      
      const displayedImgWidth = imgWidth * imgDisplayScale;
      const displayedImgHeight = imgHeight * imgDisplayScale;
      
      // 计算图像的位置（居中）
      const imgLeft = (containerWidth - displayedImgWidth) / 2;
      const imgTop = (containerHeight - displayedImgHeight) / 2;
      
      // 保存图像信息
      this.imageInfo = {
        scale: imgDisplayScale,
        left: imgLeft,
        top: imgTop,
        width: displayedImgWidth,
        height: displayedImgHeight
      };
      
      // 设置裁剪框与图像边界完全匹配
      this.cropBox = {
        x: imgLeft,
        y: imgTop,
        width: displayedImgWidth,
        height: displayedImgHeight
      };
      
      // 显示裁剪框
      this.cropBoxVisible = true;
    },
    
    // 重置裁剪框
    resetCropBox() {
      this.cropBox = {
        x: 0,
        y: 0,
        width: 0,
        height: 0
      };
      this.cropBoxVisible = false;
    },
    
    // 图像加载完成后的事件
    onImageLoad() {
      this.calculateInitialCropBox();
    },
    
    // 开始拖动
    startDragging(event) {
      event.preventDefault();
      
      this.isDragging = true;
      
      // 获取鼠标/触摸位置
      const position = this.getEventPosition(event);
      
      // 获取裁剪框的位置
      const cropBoxRect = this.$refs.cropBox.getBoundingClientRect();
      const containerRect = this.$refs.cropperContainer.getBoundingClientRect();
      
      // 计算偏移量
      this.dragStart = {
        x: position.x,
        y: position.y
      };
      
      this.dragOffset = {
        x: position.x - (cropBoxRect.left - containerRect.left),
        y: position.y - (cropBoxRect.top - containerRect.top)
      };
    },
    
    // 开始调整大小
    startResizing(direction) {
      this.isResizing = true;
      this.resizeDirection = direction;
      
      // 记录起始位置
      const position = this.getEventPosition(event);
      this.dragStart = {
        x: position.x,
        y: position.y
      };
    },
    
    // 处理鼠标移动（拖动或调整大小）
    onMouseMove(event) {
      if (this.isDragging) {
        this.handleDrag(event);
      } else if (this.isResizing) {
        this.handleResize(event);
      }
    },
    
    // 处理拖动
    handleDrag(event) {
      event.preventDefault();
      
      // 获取当前位置
      const position = this.getEventPosition(event);
      
      // 计算新位置
      let newX = position.x - this.dragOffset.x;
      let newY = position.y - this.dragOffset.y;
      
      // 确保裁剪框不会超出图像
      const minX = this.imageInfo.left;
      const minY = this.imageInfo.top;
      const maxX = this.imageInfo.left + this.imageInfo.width - this.cropBox.width;
      const maxY = this.imageInfo.top + this.imageInfo.height - this.cropBox.height;
      
      this.cropBox.x = Math.max(minX, Math.min(newX, maxX));
      this.cropBox.y = Math.max(minY, Math.min(newY, maxY));
    },
    
    // 处理调整大小
    handleResize(event) {
      event.preventDefault();
      
      // 获取当前位置
      const position = this.getEventPosition(event);
      
      // 计算移动距离
      const deltaX = position.x - this.dragStart.x;
      const deltaY = position.y - this.dragStart.y;
      
      // 更新起始位置
      this.dragStart = {
        x: position.x,
        y: position.y
      };
      
      // 根据调整方向更新裁剪框
      const minSize = 20;
      
      switch (this.resizeDirection) {
        case 'top':
          // 确保不超出图像上边界
          const newTopY = Math.max(this.imageInfo.top, this.cropBox.y + deltaY);
          // 确保高度不会太小
          const newTopHeight = this.cropBox.height - (newTopY - this.cropBox.y);
          if (newTopHeight >= minSize) {
            this.cropBox.y = newTopY;
            this.cropBox.height = newTopHeight;
          }
          break;
          
        case 'bottom':
          // 确保不超出图像下边界
          const maxBottomY = this.imageInfo.top + this.imageInfo.height;
          const newBottomHeight = this.cropBox.height + deltaY;
          if (this.cropBox.y + newBottomHeight <= maxBottomY && newBottomHeight >= minSize) {
            this.cropBox.height = newBottomHeight;
          }
          break;
          
        case 'left':
          // 确保不超出图像左边界
          const newLeftX = Math.max(this.imageInfo.left, this.cropBox.x + deltaX);
          // 确保宽度不会太小
          const newLeftWidth = this.cropBox.width - (newLeftX - this.cropBox.x);
          if (newLeftWidth >= minSize) {
            this.cropBox.x = newLeftX;
            this.cropBox.width = newLeftWidth;
          }
          break;
          
        case 'right':
          // 确保不超出图像右边界
          const maxRightX = this.imageInfo.left + this.imageInfo.width;
          const newRightWidth = this.cropBox.width + deltaX;
          if (this.cropBox.x + newRightWidth <= maxRightX && newRightWidth >= minSize) {
            this.cropBox.width = newRightWidth;
          }
          break;
      }
    },
    
    // 停止交互（拖动或调整大小）
    stopInteraction() {
      this.isDragging = false;
      this.isResizing = false;
      this.resizeDirection = null;
    },
    
    // 获取事件位置
    getEventPosition(event) {
      const containerRect = this.$refs.cropperContainer.getBoundingClientRect();
      
      let clientX, clientY;
      
      if (event.touches && event.touches.length) {
        // 触摸事件
        clientX = event.touches[0].clientX;
        clientY = event.touches[0].clientY;
      } else {
        // 鼠标事件
        clientX = event.clientX;
        clientY = event.clientY;
      }
      
      return {
        x: clientX - containerRect.left,
        y: clientY - containerRect.top
      };
    },
    
    // 确认裁剪
    async confirmCrop() {
      if (!this.imageUrl || !this.cropBoxVisible) return;
      
      try {
        // 创建一个canvas来裁剪图像
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // 计算实际裁剪区域（相对于原始图像）
        const actualX = (this.cropBox.x - this.imageInfo.left) / this.imageInfo.scale;
        const actualY = (this.cropBox.y - this.imageInfo.top) / this.imageInfo.scale;
        const actualWidth = this.cropBox.width / this.imageInfo.scale;
        const actualHeight = this.cropBox.height / this.imageInfo.scale;
        
        // 设置canvas尺寸为裁剪后的尺寸
        canvas.width = actualWidth;
        canvas.height = actualHeight;
        
        // 加载原始图像
        const img = new Image();
        img.crossOrigin = 'anonymous'; // 处理跨域问题
        
        // 使用Promise等待图像加载完成
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = reject;
          img.src = this.imageUrl;
        });
        
        // 在canvas上绘制裁剪后的图像
        ctx.drawImage(
          img,
          actualX, actualY, actualWidth, actualHeight,
          0, 0, canvas.width, canvas.height
        );
        
        // 获取原始图像的格式
        let imageFormat = 'image/jpeg'; // 默认格式
        
        // 从URL或文件名中检测格式
        if (this.imageUrl) {
          const url = this.imageUrl.toLowerCase();
          if (url.endsWith('.png') || url.includes('image/png')) {
            imageFormat = 'image/png';
          } else if (url.endsWith('.gif') || url.includes('image/gif')) {
            imageFormat = 'image/gif';
          } else if (url.endsWith('.webp') || url.includes('image/webp')) {
            imageFormat = 'image/webp';
          }
        }
        
        // 将canvas转换为blob URL，使用原始格式
        const blob = await new Promise(resolve => canvas.toBlob(resolve, imageFormat));
        const croppedImageUrl = URL.createObjectURL(blob);
        
        // 标记为已裁剪
        this.isCropped = true;
        
        // 发送裁剪后的图像URL给父组件
        this.$emit('crop-confirmed', croppedImageUrl);
      } catch (error) {
        // 裁剪失败，但不输出错误信息
      }
    },
    
    // 恢复默认图像
    resetToDefault() {
      if (!this.originalImageUrl || !this.originalImageStored) return;
      
      // 发送原始图像URL给父组件
      this.$emit('reset-to-default', this.originalImageUrl);
      
      // 重置裁剪状态
      this.isCropped = false;
    }
  }
};
</script>

<style scoped>
.image-cropper {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.cropper-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}

.original-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.crop-box {
  position: absolute;
  border: 2px solid #4CAF50;
  box-sizing: border-box;
  cursor: move;
  box-shadow: 0 0 0 2000px rgba(0, 0, 0, 0.5);
  outline: 1px solid rgba(255, 255, 255, 0.7);
}

.handle {
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: #4CAF50;
  border: 2px solid #fff;
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.handle.top {
  top: -6px;
  left: calc(50% - 6px);
  cursor: n-resize;
}

.handle.bottom {
  bottom: -6px;
  left: calc(50% - 6px);
  cursor: s-resize;
}

.handle.left {
  left: -6px;
  top: calc(50% - 6px);
  cursor: w-resize;
}

.handle.right {
  right: -6px;
  top: calc(50% - 6px);
  cursor: e-resize;
}

.cropper-controls {
  display: flex;
  justify-content: space-between;
  padding: 15px 0 5px;
  gap: 15px;
}

.reset-button, .confirm-button {
  padding: 10px 20px;
  border: none;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  text-align: center;
}

.reset-button {
  background-color: #f5f7fa;
  color: #666;
  border: 1px solid #ddd;
}

.reset-button:hover:not(:disabled) {
  background-color: #e8eaed;
  color: #333;
}

.reset-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f5f5f5;
  color: #aaa;
}

.confirm-button {
  background: linear-gradient(45deg, #3a7bd5, #00d2ff);
  color: white;
  box-shadow: 0 4px 15px rgba(58, 123, 213, 0.3);
}

.confirm-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(58, 123, 213, 0.4);
}

.confirm-button:disabled {
  background: linear-gradient(45deg, #ccc, #ddd);
  cursor: not-allowed;
  box-shadow: none;
}

@media (max-width: 768px) {
  .cropper-controls {
    flex-direction: column;
    padding: 10px 0;
    gap: 10px;
  }
  
  .reset-button, .confirm-button {
    padding: 8px 15px;
    font-size: 14px;
  }
  
  .handle {
    width: 16px;
    height: 16px;
  }
  
  .handle.top {
    top: -8px;
    left: calc(50% - 8px);
  }
  
  .handle.bottom {
    bottom: -8px;
    left: calc(50% - 8px);
  }
  
  .handle.left {
    left: -8px;
    top: calc(50% - 8px);
  }
  
  .handle.right {
    right: -8px;
    top: calc(50% - 8px);
  }
}
</style> 