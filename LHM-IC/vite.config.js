import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    host: '0.0.0.0',
    port: 3000,
    allowedHosts: ['sorealhuman.com'],
    proxy: {
      '/api': {
        target: 'http://**********:7863',
        changeOrigin: true,
        rewrite: (path) => path
      },
      '/output': {
        target: 'http://**********:7863',
        changeOrigin: true
      },
      '/submit_task': {
        target: 'http://**********:8000',
        changeOrigin: true
      },
      '/task_status': {
        target: 'http://**********:8000',
        changeOrigin: true
      },
      '/results': {
        target: 'http://**********:8000',
        changeOrigin: true
      },
      '/relighting_output': {
        target: 'http://**********:8000',
        changeOrigin: true
      }
    },
    cors: true
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  }
}) 